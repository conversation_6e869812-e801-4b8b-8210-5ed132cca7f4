﻿using SDHD.DC.DataCollection.ApplicationCore.Domain.Entities;
using SDHD.DC.DataCollection.ApplicationCore.Domain.Request;
using SDHD.DC.DataCollection.ApplicationCore.Domain.Response;
using SDHD.DC.DataCollection.ApplicationCore.Interfaces.Repository;
using SDHD.DC.Utilities;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace SDHD.DC.DataCollection.ApplicationCore.Interfaces.Service
{
    public interface ISystemService
    {
        Task<SysConfigResponse> GetSysConfig();
        Task<BizResponse> UpdateSysToDefaultWellStationId(string wellStationId);
        Task<RegistryResponse> Register(RegistryRequest request);
        Task<RegistryResponse> SetRegister(RegistryRequest request);
        Task<RegistryDetailResponse> GetRegisterData();
        Task<ServerConnectTestResponse> TestServerConnection(ServerConnectTestRequest request);
        Task CloseServerConnection();
        Task<RedisConfigResponse> GetRedisConfig();
        Task<BizResponse> UpdateRedisConfig(RedisConfigUpdateRequest request);
        Task<string> GetDefaultWellStationTemplateId();
        Task<string> GetSysLanguage();
        Task<BizResponse> UpdateSysLanguage(string language);
        Task<BizResponse> UpdateDefaultWellStationTemplateId(string defaultWellStationTemplateId);


        #region 变更代码 Ahri
        void GetRegisterDataay();
        void GetNationalStandardGasProductionParametersay();
        Task GetNationalStandardGasProductionParametersData();
        List<GasParameterData> GetNationalStandardGasProductionParameters();
        List<GasParameterMBData> GetNationalStandardGasProductionParametersMB();
        List<GasParameterSandData> GetNationalStandardGasProductionParametersSand();
        bool SetNationalStandardGasProductionParameters(List<GasParameterData> gasParameterDatas);
        bool SetNationalStandardGasProductionParametersMB(List<GasParameterMBData> gasParameterDatas);
        bool SetNationalStandardGasProductionParametersSand(List<GasParameterSandData> gasParameterDatas);
        bool SetUploadConfig(UploadConfig uploadConfig);
        bool SetUploadConfigZSH(UploadConfigZSH uploadConfigZSH);
        Task<UploadConfig> GetUploadConfig(bool reload = false);

        Task<List<Rank>> GetRanks();
        Task<bool> UpdateRanks(List<Rank> rank);
        List<Menu> GetDefaultMenus(int depth = 1);

        Task<List<string>> GetWebUrlConfig();

        Task<bool> SaveWebUrlConfig(List<string> urls);
        #endregion 变更代码 Ahri
    }
}
