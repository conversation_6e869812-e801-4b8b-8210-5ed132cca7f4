﻿<Window x:Class="SDHD.DC.DataMonitor.Views.Sensor.SensorDetailConfigWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:SDHD.DC.DataMonitor.Views.Sensor"
        xmlns:uc="clr-namespace:SDHD.DC.DataMonitor.UserControls"
        xmlns:loc="clr-namespace:SDHD.DC.DataMonitor.Resources.Localization"
        Icon="/assets/images/default.png"
        mc:Ignorable="d"
        Title="{loc:LocalizationExtension SensorDetailConfigTitle}" Height="700" Width="900" ResizeMode="CanResize" MinWidth="900" MinHeight="700" Style="{StaticResource chromeWindow}">
    <Window.Resources>
        <!-- 现代化样式定义 -->
        <Style x:Key="SectionHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#2C3E50"/>
            <Setter Property="Margin" Value="0,10,0,5"/>
        </Style>

        <Style x:Key="FieldLabelStyle" TargetType="TextBlock">
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,0,10,0"/>
            <Setter Property="MinWidth" Value="120"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
        </Style>

        <Style x:Key="InputControlStyle" TargetType="Control">
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,2"/>
            <Setter Property="Height" Value="25"/>
        </Style>

        <Style x:Key="ModernTextBoxStyle" TargetType="TextBox" BasedOn="{StaticResource InputControlStyle}">
            <Setter Property="Padding" Value="5,2"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#BDC3C7"/>
        </Style>

        <Style x:Key="ModernComboBoxStyle" TargetType="ComboBox" BasedOn="{StaticResource InputControlStyle}">
            <Setter Property="Padding" Value="5,2"/>
        </Style>

        <Style x:Key="CalibrationLabelStyle" TargetType="Label">
            <Setter Property="Width" Value="140"/>
            <Setter Property="HorizontalAlignment" Value="Right"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="5,0,0,0"/>
            <Setter Property="ToolTip" Value="{Binding RelativeSource={RelativeSource Self}, Path=Content}"/>
        </Style>

        <Style x:Key="CalibrationTextBoxStyle" TargetType="TextBox" BasedOn="{StaticResource ModernTextBoxStyle}">
            <Setter Property="Width" Value="100"/>
            <Setter Property="MaxLength" Value="18"/>
        </Style>

        <Style x:Key="CalibrationButtonStyle" TargetType="Button" BasedOn="{StaticResource InputControlStyle}">
            <Setter Property="Width" Value="80"/>
            <Setter Property="Padding" Value="5,2"/>
        </Style>

        <!-- 分组边框样式 -->
        <Style x:Key="SectionBorderStyle" TargetType="Border">
            <Setter Property="BorderBrush" Value="#E8E8E8"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="5"/>
            <Setter Property="Padding" Value="15"/>
            <Setter Property="Margin" Value="0,5"/>
            <Setter Property="Background" Value="#FAFAFA"/>
        </Style>
    </Window.Resources>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <uc:UcWindowHeader Grid.Row="0" Title="{loc:LocalizationExtension SensorDetailConfigTitle}"></uc:UcWindowHeader>

        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled" Padding="20">
            <StackPanel>

                <!-- 基本信息分组 -->
                <Border x:Name="panel1" Style="{StaticResource SectionBorderStyle}">
                    <StackPanel>
                        <TextBlock Text="{loc:LocalizationExtension SectionBasicInfo}" Style="{StaticResource SectionHeaderStyle}"/>

                        <!-- 传感器类型和名称 -->
                        <Grid Margin="0,10,0,0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="{Binding SensorTypeTitle}" Style="{StaticResource FieldLabelStyle}"/>
                            <TextBlock Grid.Column="1" Text="{Binding SensorNameTitle}" Style="{StaticResource FieldLabelStyle}"/>
                            <TextBlock Grid.Column="2" Text="{loc:LocalizationExtension TextProperties}" Style="{StaticResource FieldLabelStyle}"/>
                        </Grid>

                        <TextBlock Text="{loc:LocalizationExtension LabelCheckConnectionAndSetup}"
                                   Margin="0,10,0,0" FontStyle="Italic" Foreground="#7F8C8D"/>

                        <!-- 采样时间 -->
                        <Grid Margin="0,15,0,0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="{loc:LocalizationExtension LabelSamplingTime}" Style="{StaticResource FieldLabelStyle}"/>
                            <TextBox Grid.Column="1" Text="{Binding CollectInterval}" Width="80" Style="{StaticResource ModernTextBoxStyle}" MaxLength="3"/>
                            <ComboBox Grid.Column="2" Margin="10,0,0,0" MinWidth="80" Style="{StaticResource ModernComboBoxStyle}"
                                      ItemsSource="{Binding CollectIntervalUnits}" SelectedValue="{Binding CollectIntervalUnit}"
                                      SelectedValuePath="Value" DisplayMemberPath="Text"/>
                            <TextBlock Grid.Column="3" Text="{loc:LocalizationExtension LabelSamplingTimeNote}"
                                       Style="{StaticResource FieldLabelStyle}" Margin="15,0,0,0" FontStyle="Italic" Foreground="#7F8C8D"/>
                        </Grid>

                        <!-- 传感器命名 -->
                        <Grid Margin="0,15,0,0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="{loc:LocalizationExtension LabelSensorNaming}" Style="{StaticResource FieldLabelStyle}"/>
                            <ComboBox Grid.Column="1" x:Name="ddlSensorNameGroup" Width="120" Style="{StaticResource ModernComboBoxStyle}"
                                      ItemsSource="{Binding SensorNameGroups}" SelectedValue="{Binding SensorNameGroup}"
                                      SelectedValuePath="Value" DisplayMemberPath="Text" SelectionChanged="ddlSensorNameGroup_SelectionChanged"/>
                            <ComboBox Grid.Column="2" Margin="10,0,0,0" IsEditable="True" Name="ddlSensorName" Width="200" Style="{StaticResource ModernComboBoxStyle}"
                                      Text="{Binding SensorName}" ItemsSource="{Binding SensorNames}" DisplayMemberPath="Text"
                                      SelectionChanged="ddlSensorName_SelectionChanged" TextBoxBase.TextChanged="ddlSensorName_TextChanged"/>
                        </Grid>

                        <!-- 数据类型 -->
                        <Grid Margin="0,15,0,0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="{loc:LocalizationExtension LabelDataType}" Style="{StaticResource FieldLabelStyle}"/>
                            <ComboBox Grid.Column="1" x:Name="ddlModuleType" Width="150" Style="{StaticResource ModernComboBoxStyle}"
                                      ItemsSource="{Binding SensorModuleTypes}" SelectedValue="{Binding SensorModuleType}"
                                      SelectedValuePath="Value" DisplayMemberPath="Text" SelectionChanged="ddlModuleType_SelectionChanged"/>
                            <CheckBox Grid.Column="2" Margin="20,0,0,0" IsChecked="{Binding IsMuitValue}"
                                      Content="{loc:LocalizationExtension CheckBoxHighFrequencyDevice}" VerticalAlignment="Center"/>
                        </Grid>

                        <!-- 模块编号 -->
                        <Grid Margin="0,15,0,0" Visibility="{Binding IsModuleSNEnabled, Converter={StaticResource BoolToVis}}">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="{loc:LocalizationExtension LabelModuleNumber}" Style="{StaticResource FieldLabelStyle}"/>
                            <TextBox Grid.Column="1" Text="{Binding ModuleSN}" Width="120" Style="{StaticResource ModernTextBoxStyle}"
                                     TextChanged="txtModuleSN_TextChanged"/>
                            <TextBlock Grid.Column="2" Text="{loc:LocalizationExtension LabelHardwareNumberOnADC}"
                                       Style="{StaticResource FieldLabelStyle}" Margin="15,0,0,0" FontStyle="Italic" Foreground="#7F8C8D"/>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- 传感器类型和配置分组 -->
                <Border x:Name="panel2" Style="{StaticResource SectionBorderStyle}">
                    <StackPanel>
                        <TextBlock Text="{loc:LocalizationExtension SectionSensorConfig}" Style="{StaticResource SectionHeaderStyle}"/>

                        <!-- 传感器类型 -->
                        <Grid Margin="0,10,0,0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="{loc:LocalizationExtension LabelSensorType}" Style="{StaticResource FieldLabelStyle}"/>
                            <ComboBox Grid.Column="1" x:Name="ddlSensorCategory" Width="150" Style="{StaticResource ModernComboBoxStyle}"
                                      ItemsSource="{Binding SensorCategories}" SelectedValue="{Binding SensorCategory}"
                                      SelectedValuePath="Value" DisplayMemberPath="Text" SelectionChanged="ddlSensorCategory_SelectionChanged"/>
                            <CheckBox Grid.Column="2" Margin="20,0,0,0" IsChecked="{Binding IsAccumulatedYiYe}"
                                      Content="{loc:LocalizationExtension CheckBoxAccumulateDisplacement}" VerticalAlignment="Center"/>
                        </Grid>

                        <!-- 累积设置 -->
                        <Grid Margin="0,10,0,0" Visibility="{Binding IsAccumulatedYiYe, Converter={StaticResource BoolToVis}}">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="{loc:LocalizationExtension LabelBy}" Style="{StaticResource FieldLabelStyle}"/>
                            <ComboBox Grid.Column="1" Width="80" Style="{StaticResource ModernComboBoxStyle}"
                                      ItemsSource="{Binding AccumulationTimeUnits}" SelectedValue="{Binding AccumulationTimeUnit}"
                                      SelectedValuePath="Value" DisplayMemberPath="Text"/>
                            <TextBlock Grid.Column="2" Text="{loc:LocalizationExtension LabelAccumulate}" Style="{StaticResource FieldLabelStyle}"/>
                        </Grid>

                        <!-- 传感器范围 -->
                        <Grid Margin="0,15,0,0" Visibility="{Binding IsRangeEnabled, Converter={StaticResource BoolToVis}}">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="{loc:LocalizationExtension LabelSensorRange}" Style="{StaticResource FieldLabelStyle}"/>
                            <TextBox Grid.Column="1" Text="{Binding MinRange}" Width="100" Style="{StaticResource ModernTextBoxStyle}"/>
                            <TextBlock Grid.Column="2" Text="{loc:LocalizationExtension TextTo}" Style="{StaticResource FieldLabelStyle}" Margin="10,0"/>
                            <TextBox Grid.Column="3" Text="{Binding MaxRange}" Width="100" Style="{StaticResource ModernTextBoxStyle}"/>
                        </Grid>

                        <!-- ADC输出范围 -->
                        <Grid Margin="0,10,0,0" Visibility="{Binding IsRangeEnabled, Converter={StaticResource BoolToVis}}">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="{loc:LocalizationExtension LabelADCOutputRange}" Style="{StaticResource FieldLabelStyle}"/>
                            <TextBox Grid.Column="1" Text="{Binding ADCMinRange}" Width="100" Style="{StaticResource ModernTextBoxStyle}"/>
                            <TextBlock Grid.Column="2" Text="{loc:LocalizationExtension TextTo}" Style="{StaticResource FieldLabelStyle}" Margin="10,0"/>
                            <TextBox Grid.Column="3" Text="{Binding ADCMaxRange}" Width="100" Style="{StaticResource ModernTextBoxStyle}"/>
                            <TextBlock Grid.Column="4" Text="mA" Style="{StaticResource FieldLabelStyle}" Margin="10,0,0,0"/>
                        </Grid>

                        <!-- 测量单位 -->
                        <Grid Margin="0,15,0,0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="{loc:LocalizationExtension LabelMeasureUnit}" Style="{StaticResource FieldLabelStyle}"/>
                            <ComboBox Grid.Column="1" x:Name="ddlMeasureUnit" Width="150" Style="{StaticResource ModernComboBoxStyle}"
                                      ItemsSource="{Binding MeasureUnits}" SelectedValue="{Binding MeasureUnitId}"
                                      SelectedValuePath="Value" DisplayMemberPath="Text" SelectionChanged="ddlMeasureUnit_SelectionChanged"/>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- 高级设置分组 -->
                <Border x:Name="panel3" Style="{StaticResource SectionBorderStyle}">
                    <StackPanel>
                        <TextBlock Text="{loc:LocalizationExtension SectionAdvancedSettings}" Style="{StaticResource SectionHeaderStyle}"/>

                        <!-- 用户组和有效阈值 -->
                        <Grid Margin="0,10,0,0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="{loc:LocalizationExtension LabelUserGroup}" Style="{StaticResource FieldLabelStyle}"/>
                            <ComboBox Grid.Column="1" Width="120" Style="{StaticResource ModernComboBoxStyle}"
                                      ItemsSource="{Binding UserGroups}" SelectedValue="{Binding UserGroup}"
                                      SelectedValuePath="Value" DisplayMemberPath="Text"/>
                            <TextBlock Grid.Column="2" Text="{loc:LocalizationExtension LabelValidThreshold}" Style="{StaticResource FieldLabelStyle}" Margin="20,0,0,0"/>
                            <TextBox Grid.Column="3" Width="100" Style="{StaticResource ModernTextBoxStyle}"
                                     Text="{Binding ValidDataValue}" IsReadOnly="{Binding IsDisabledValidDataValue}"/>
                            <CheckBox Grid.Column="4" Margin="10,0,0,0" Content="{loc:LocalizationExtension CheckBoxEnableValidThreshold}"
                                      IsChecked="{Binding IsEnabledValidDataValue}" VerticalAlignment="Center"/>
                        </Grid>

                        <!-- 传感器附加属性 -->
                        <Grid Margin="0,15,0,0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="{loc:LocalizationExtension LabelSerialNumber}" Style="{StaticResource FieldLabelStyle}"/>
                            <TextBox Grid.Column="1" Text="{Binding SensorAdditionalPropertieTextSN}" Width="60" Style="{StaticResource ModernTextBoxStyle}"/>
                            <TextBlock Grid.Column="2" Text="{loc:LocalizationExtension LabelSensorAdditionalProperties}" Style="{StaticResource FieldLabelStyle}" Margin="15,0,0,0"/>
                            <ComboBox Grid.Column="3" x:Name="ddlSensorAdditionalPropertie" Width="140" Style="{StaticResource ModernComboBoxStyle}"
                                      ItemsSource="{Binding SensorAdditionalProperties}" DisplayMemberPath="Name"
                                      SelectedItem="{Binding SensorAdditionalPropertieSelected}"/>
                            <ComboBox Grid.Column="4" Name="ddlSensorAdditionalPropertieChild" Width="140" Style="{StaticResource ModernComboBoxStyle}" Margin="10,0,0,0"
                                      ItemsSource="{Binding SensorAdditionalPropertieChilds}" DisplayMemberPath="Name"
                                      SelectedItem="{Binding SensorAdditionalPropertieChild}"/>
                        </Grid>

                        <!-- ID设置 -->
                        <Grid Margin="0,10,0,0" Visibility="{Binding SensorAdditionalPropertieTextVisibility}">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="ID：" Style="{StaticResource FieldLabelStyle}"/>
                            <TextBox Grid.Column="1" Text="{Binding SensorAdditionalPropertieTextId}" Width="60" MaxLength="2" Style="{StaticResource ModernTextBoxStyle}"/>
                        </Grid>

                        <!-- 模块选项 -->
                        <WrapPanel Margin="0,15,0,0" Orientation="Horizontal">
                            <CheckBox Content="{loc:LocalizationExtension CheckBoxEnableModule}" IsChecked="{Binding IsEnabled}" Margin="0,0,20,0"/>
                            <CheckBox Content="{loc:LocalizationExtension CheckBoxHideSensor}" IsChecked="{Binding IsHidden}" Margin="0,0,20,0"/>
                            <CheckBox Content="{loc:LocalizationExtension CheckBoxStoreProductionParams}" IsChecked="{Binding IsStorageProductionMiddlePara}"/>
                        </WrapPanel>

                        <TextBlock Text="{loc:LocalizationExtension LabelModuleEnableNote}"
                                   Margin="0,10,0,0" FontStyle="Italic" Foreground="#7F8C8D" TextWrapping="Wrap"/>
                    </StackPanel>
                </Border>

                <!-- 显示设置、校准和公式分组 -->
                <Border x:Name="panel4" Style="{StaticResource SectionBorderStyle}">
                    <StackPanel>
                        <!-- 显示设置子分组 -->
                        <TextBlock Text="{loc:LocalizationExtension SectionDisplaySettings}" Style="{StaticResource SectionHeaderStyle}"/>

                        <!-- 图标和精度 -->
                        <Grid Margin="0,10,0,0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="{loc:LocalizationExtension LabelIcon}" Style="{StaticResource FieldLabelStyle}"/>
                            <ComboBox Grid.Column="1" Width="140" Style="{StaticResource ModernComboBoxStyle}"
                                      ItemsSource="{Binding Icons}" SelectedValue="{Binding Icon}" SelectedValuePath="Value">
                                <ComboBox.ItemTemplate>
                                    <DataTemplate>
                                        <Image Source="{Binding Text}" Style="{StaticResource imageIcon}"/>
                                    </DataTemplate>
                                </ComboBox.ItemTemplate>
                            </ComboBox>
                            <TextBlock Grid.Column="2" Text="{loc:LocalizationExtension LabelDecimalPrecision}" Style="{StaticResource FieldLabelStyle}" Margin="30,0,0,0"/>
                            <ComboBox Grid.Column="3" Width="100" Style="{StaticResource ModernComboBoxStyle}"
                                      ItemsSource="{Binding DecimalPrecisions}" SelectedValue="{Binding DecimalPrecision}"
                                      SelectedValuePath="Value" DisplayMemberPath="Text"/>
                        </Grid>

                        <!-- 颜色和多值索引 -->
                        <Grid Margin="0,15,0,0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="{loc:LocalizationExtension LabelDefaultLineColor}" Style="{StaticResource FieldLabelStyle}"/>
                            <Border Grid.Column="1" Background="{Binding LineColorBrush}" Width="80" Height="25"
                                    BorderThickness="1" BorderBrush="#BDC3C7" CornerRadius="3" Cursor="Hand" MouseDown="lineColor_Click"/>
                            <TextBlock Grid.Column="2" Text="{loc:LocalizationExtension LabelMultiValueIndex}" Style="{StaticResource FieldLabelStyle}" Margin="30,0,0,0"
                                       Visibility="{Binding IsModuleSNEnabled, Converter={StaticResource BoolToVis}}"/>
                            <TextBox Grid.Column="3" Width="100" Style="{StaticResource ModernTextBoxStyle}" MaxLength="9"
                                     Text="{Binding DevIndexID}" Visibility="{Binding IsModuleSNEnabled, Converter={StaticResource BoolToVis}}"/>
                        </Grid>

                        <!-- 传感器校准子分组 -->
                        <TextBlock Text="{loc:LocalizationExtension LabelSensorCalibration}" Style="{StaticResource SectionHeaderStyle}" Margin="0,25,0,0"/>

                        <!-- 校准点1和2 - 使用Grid布局 -->
                        <Grid Margin="0,10,0,0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="140"/>
                                <ColumnDefinition Width="110"/>
                                <ColumnDefinition Width="90"/>
                                <ColumnDefinition Width="140"/>
                                <ColumnDefinition Width="110"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="{loc:LocalizationExtension LabelReading1}" Style="{StaticResource FieldLabelStyle}"/>
                            <TextBox Grid.Column="1" Text="{Binding CorrectItem1.ReadValue}" Style="{StaticResource CalibrationTextBoxStyle}"/>
                            <Button Grid.Column="2" Content="{loc:LocalizationExtension ButtonRead}" Style="{StaticResource CalibrationButtonStyle}"
                                    Visibility="{Binding IsReadRealTimeDataVisible, Converter={StaticResource BoolToVis}}"
                                    Click="btnReadRealTimeData_Click" Uid="0" IsEnabled="{Binding IsEnableReadRealTime}"/>
                            <TextBlock Grid.Column="3" Text="{loc:LocalizationExtension LabelActualValue}" Style="{StaticResource FieldLabelStyle}"/>
                            <TextBox Grid.Column="4" Text="{Binding CorrectItem1.ActualValue}" Style="{StaticResource CalibrationTextBoxStyle}"/>
                        </Grid>

                        <Grid Margin="0,8,0,0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="140"/>
                                <ColumnDefinition Width="110"/>
                                <ColumnDefinition Width="90"/>
                                <ColumnDefinition Width="140"/>
                                <ColumnDefinition Width="110"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="{loc:LocalizationExtension LabelReading2}" Style="{StaticResource FieldLabelStyle}"/>
                            <TextBox Grid.Column="1" Text="{Binding CorrectItem2.ReadValue}" Style="{StaticResource CalibrationTextBoxStyle}"/>
                            <Button Grid.Column="2" Content="{loc:LocalizationExtension ButtonRead}" Style="{StaticResource CalibrationButtonStyle}"
                                    Visibility="{Binding IsReadRealTimeDataVisible, Converter={StaticResource BoolToVis}}"
                                    Click="btnReadRealTimeData_Click" Uid="1" IsEnabled="{Binding IsEnableReadRealTime}"/>
                            <TextBlock Grid.Column="3" Text="{loc:LocalizationExtension LabelActualValue}" Style="{StaticResource FieldLabelStyle}"/>
                            <TextBox Grid.Column="4" Text="{Binding CorrectItem2.ActualValue}" Style="{StaticResource CalibrationTextBoxStyle}"/>
                        </Grid>

                        <!-- 校准点3-7 -->
                        <Grid Margin="0,8,0,0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="140"/>
                                <ColumnDefinition Width="110"/>
                                <ColumnDefinition Width="90"/>
                                <ColumnDefinition Width="140"/>
                                <ColumnDefinition Width="110"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="{loc:LocalizationExtension LabelReading3}" Style="{StaticResource FieldLabelStyle}"/>
                            <TextBox Grid.Column="1" Text="{Binding CorrectItem3.ReadValue}" Style="{StaticResource CalibrationTextBoxStyle}"/>
                            <Button Grid.Column="2" Content="{loc:LocalizationExtension ButtonRead}" Style="{StaticResource CalibrationButtonStyle}"
                                    Visibility="{Binding IsReadRealTimeDataVisible, Converter={StaticResource BoolToVis}}"
                                    Click="btnReadRealTimeData_Click" Uid="2" IsEnabled="{Binding IsEnableReadRealTime}"/>
                            <TextBlock Grid.Column="3" Text="{loc:LocalizationExtension LabelActualValue}" Style="{StaticResource FieldLabelStyle}"/>
                            <TextBox Grid.Column="4" Text="{Binding CorrectItem3.ActualValue}" Style="{StaticResource CalibrationTextBoxStyle}"/>
                        </Grid>

                        <Grid Margin="0,8,0,0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="140"/>
                                <ColumnDefinition Width="110"/>
                                <ColumnDefinition Width="90"/>
                                <ColumnDefinition Width="140"/>
                                <ColumnDefinition Width="110"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="{loc:LocalizationExtension LabelReading4}" Style="{StaticResource FieldLabelStyle}"/>
                            <TextBox Grid.Column="1" Text="{Binding CorrectItem4.ReadValue}" Style="{StaticResource CalibrationTextBoxStyle}"/>
                            <Button Grid.Column="2" Content="{loc:LocalizationExtension ButtonRead}" Style="{StaticResource CalibrationButtonStyle}"
                                    Visibility="{Binding IsReadRealTimeDataVisible, Converter={StaticResource BoolToVis}}"
                                    Click="btnReadRealTimeData_Click" Uid="3" IsEnabled="{Binding IsEnableReadRealTime}"/>
                            <TextBlock Grid.Column="3" Text="{loc:LocalizationExtension LabelActualValue}" Style="{StaticResource FieldLabelStyle}"/>
                            <TextBox Grid.Column="4" Text="{Binding CorrectItem4.ActualValue}" Style="{StaticResource CalibrationTextBoxStyle}"/>
                        </Grid>

                        <Grid Margin="0,8,0,0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="140"/>
                                <ColumnDefinition Width="110"/>
                                <ColumnDefinition Width="90"/>
                                <ColumnDefinition Width="140"/>
                                <ColumnDefinition Width="110"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="{loc:LocalizationExtension LabelReading5}" Style="{StaticResource FieldLabelStyle}"/>
                            <TextBox Grid.Column="1" Text="{Binding CorrectItem5.ReadValue}" Style="{StaticResource CalibrationTextBoxStyle}"/>
                            <Button Grid.Column="2" Content="{loc:LocalizationExtension ButtonRead}" Style="{StaticResource CalibrationButtonStyle}"
                                    Visibility="{Binding IsReadRealTimeDataVisible, Converter={StaticResource BoolToVis}}"
                                    Click="btnReadRealTimeData_Click" Uid="4" IsEnabled="{Binding IsEnableReadRealTime}"/>
                            <TextBlock Grid.Column="3" Text="{loc:LocalizationExtension LabelActualValue}" Style="{StaticResource FieldLabelStyle}"/>
                            <TextBox Grid.Column="4" Text="{Binding CorrectItem5.ActualValue}" Style="{StaticResource CalibrationTextBoxStyle}"/>
                        </Grid>

                        <Grid Margin="0,8,0,0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="140"/>
                                <ColumnDefinition Width="110"/>
                                <ColumnDefinition Width="90"/>
                                <ColumnDefinition Width="140"/>
                                <ColumnDefinition Width="110"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="{loc:LocalizationExtension LabelReading6}" Style="{StaticResource FieldLabelStyle}"/>
                            <TextBox Grid.Column="1" Text="{Binding CorrectItem6.ReadValue}" Style="{StaticResource CalibrationTextBoxStyle}"/>
                            <Button Grid.Column="2" Content="{loc:LocalizationExtension ButtonRead}" Style="{StaticResource CalibrationButtonStyle}"
                                    Visibility="{Binding IsReadRealTimeDataVisible, Converter={StaticResource BoolToVis}}"
                                    Click="btnReadRealTimeData_Click" Uid="5" IsEnabled="{Binding IsEnableReadRealTime}"/>
                            <TextBlock Grid.Column="3" Text="{loc:LocalizationExtension LabelActualValue}" Style="{StaticResource FieldLabelStyle}"/>
                            <TextBox Grid.Column="4" Text="{Binding CorrectItem6.ActualValue}" Style="{StaticResource CalibrationTextBoxStyle}"/>
                        </Grid>

                        <Grid Margin="0,8,0,0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="140"/>
                                <ColumnDefinition Width="110"/>
                                <ColumnDefinition Width="90"/>
                                <ColumnDefinition Width="140"/>
                                <ColumnDefinition Width="110"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="{loc:LocalizationExtension LabelReading7}" Style="{StaticResource FieldLabelStyle}"/>
                            <TextBox Grid.Column="1" Text="{Binding CorrectItem7.ReadValue}" Style="{StaticResource CalibrationTextBoxStyle}"/>
                            <Button Grid.Column="2" Content="{loc:LocalizationExtension ButtonRead}" Style="{StaticResource CalibrationButtonStyle}"
                                    Visibility="{Binding IsReadRealTimeDataVisible, Converter={StaticResource BoolToVis}}"
                                    Click="btnReadRealTimeData_Click" Uid="6" IsEnabled="{Binding IsEnableReadRealTime}"/>
                            <TextBlock Grid.Column="3" Text="{loc:LocalizationExtension LabelActualValue}" Style="{StaticResource FieldLabelStyle}"/>
                            <TextBox Grid.Column="4" Text="{Binding CorrectItem7.ActualValue}" Style="{StaticResource CalibrationTextBoxStyle}"/>
                        </Grid>

                        <!-- 校准说明 -->
                        <TextBlock Margin="0,15,0,0" Text="{loc:LocalizationExtension MessageCalibrationProcess}"
                                   TextWrapping="Wrap" FontStyle="Italic" Foreground="#7F8C8D"/>


            <Label Margin="5,5,0,0">计算公式中，“Input”表示传感器读数，可直接使用其它传感器的名称。</Label>


                    </StackPanel>
                </Border>

                <!-- 计算公式分组 - 独立显示，适用于所有传感器类型 -->
                <Border x:Name="panel5" Style="{StaticResource SectionBorderStyle}">
                    <StackPanel>
                        <TextBlock Text="{loc:LocalizationExtension SectionCalculationFormula}" Style="{StaticResource SectionHeaderStyle}"/>

                        <TextBlock Text="{loc:LocalizationExtension MessageFormulaInputDescription}"
                                   Margin="0,10,0,0" FontStyle="Italic" Foreground="#7F8C8D" TextWrapping="Wrap"/>

                        <!-- 常用公式 -->
                        <Grid Margin="0,15,0,0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="{loc:LocalizationExtension LabelCommonFormulas}" Style="{StaticResource FieldLabelStyle}"/>
                            <ComboBox Grid.Column="1" Style="{StaticResource ModernComboBoxStyle}" MaxWidth="300" HorizontalAlignment="Left"
                                      ItemsSource="{Binding NormalFormulas}" SelectedValuePath="Value" DisplayMemberPath="Text"
                                      SelectionChanged="ddlNormalFormula_SelectionChanged"/>
                        </Grid>

                        <!-- 公式编辑区域 -->
                        <Grid Margin="0,15,0,0">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Text="{loc:LocalizationExtension LabelFormulaEditingArea}" Style="{StaticResource FieldLabelStyle}"/>
                            <TextBox Grid.Row="1" x:Name="txtCalFormula" Height="80" Margin="0,5,0,0" Style="{StaticResource ModernTextBoxStyle}"
                                     TextWrapping="Wrap" AcceptsReturn="True" VerticalScrollBarVisibility="Auto"
                                     Text="{Binding CalFormula, UpdateSourceTrigger=PropertyChanged}" TextChanged="txtCalFormula_TextChanged">
                                <TextBox.ContextMenu>
                                    <ContextMenu>
                                        <MenuItem Header="{loc:LocalizationExtension MenuInsertFunction}" Click="menuInsertFormula_Click"/>
                                        <MenuItem Header="{loc:LocalizationExtension MenuInsertSensorVariable}" Click="menuInsertSensorVar_Click"/>
                                        <MenuItem Header="{loc:LocalizationExtension MenuInsertInput}" Click="menuInsertFormulaInput_Click"/>
                                        <MenuItem Header="{loc:LocalizationExtension MenuInsertEnvironmentVariable}" Click="menuInsertEnvVar_Click"/>
                                    </ContextMenu>
                                </TextBox.ContextMenu>
                            </TextBox>

                            <TextBlock Grid.Row="2" Text="{loc:LocalizationExtension LabelFormulaAnalysisStatus}" Style="{StaticResource FieldLabelStyle}" Margin="0,15,0,0"/>
                            <TextBox Grid.Row="3" Height="100" Margin="0,5,0,0" Style="{StaticResource ModernTextBoxStyle}"
                                     TextWrapping="Wrap" IsReadOnly="True" VerticalScrollBarVisibility="Auto"
                                     Text="{Binding CalFormulaVerifyResult}" Background="#F8F9FA"/>
                        </Grid>
                    </StackPanel>
                </Border>

            </StackPanel>
        </ScrollViewer>

        <!-- 底部按钮区域 -->
        <Border Grid.Row="2" Background="#F5F5F5" BorderBrush="#E0E0E0" BorderThickness="0,1,0,0" Padding="20,15">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <!-- 隐藏分页按钮，只保留完成和取消 -->
                <Button x:Name="btnPrevStep" Content="{loc:LocalizationExtension ButtonPreviousStep}"
                        Click="btnPrevStep_Click" Style="{StaticResource InputControlStyle}" Width="100" Margin="0,0,15,0" Visibility="Collapsed"/>
                <Button x:Name="btnNextStep" Content="{loc:LocalizationExtension ButtonNextStep}"
                        Click="btnNextStep_Click" Style="{StaticResource InputControlStyle}" Width="100" Margin="0,0,15,0" Visibility="Collapsed"/>
                <Button x:Name="btnComplete" Content="{loc:LocalizationExtension ButtonComplete}"
                        Click="btnCompleteStep_Click" Style="{StaticResource InputControlStyle}" Width="100" Margin="0,0,15,0"/>
                <Button Content="{loc:LocalizationExtension ButtonCancel}"
                        Click="btnCancel_Click" Style="{StaticResource InputControlStyle}" Width="100"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
