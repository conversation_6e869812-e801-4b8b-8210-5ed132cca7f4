<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AppTitle" xml:space="preserve">
    <value>Система сбора данных наземных испытаний</value>
  </data>
  <data name="Welcome" xml:space="preserve">
    <value>Добро пожаловать в систему сбора данных наземных испытаний</value>
  </data>
  <data name="MenuFile" xml:space="preserve">
    <value>Файл</value>
  </data>
  <data name="MenuEdit" xml:space="preserve">
    <value>Редактировать</value>
  </data>
  <data name="MenuView" xml:space="preserve">
    <value>Вид</value>
  </data>
  <data name="MenuTools" xml:space="preserve">
    <value>Инструменты</value>
  </data>
  <data name="MenuHelp" xml:space="preserve">
    <value>Справка</value>
  </data>
  <data name="MenuSettings" xml:space="preserve">
    <value>Настройки</value>
  </data>
  <data name="MenuExit" xml:space="preserve">
    <value>Выход</value>
  </data>
  <data name="MenuCurrentProject" xml:space="preserve">
    <value>Скважина</value>
  </data>
  <data name="MenuAlarm" xml:space="preserve">
    <value>Оповещения</value>
  </data>
  <data name="MenuWindow" xml:space="preserve">
    <value>Окно</value>
  </data>
  <data name="MenuDeleteCurrentMonitor" xml:space="preserve">
    <value>Удалить монитор</value>
  </data>
  <data name="ButtonOk" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="ButtonCancel" xml:space="preserve">
    <value>Отмена</value>
  </data>
  <data name="ButtonSave" xml:space="preserve">
    <value>Сохранить</value>
  </data>
  <data name="ButtonClose" xml:space="preserve">
    <value>Закрыть</value>
  </data>
  <data name="ButtonApply" xml:space="preserve">
    <value>Применить</value>
  </data>
  <data name="SettingsTitle" xml:space="preserve">
    <value>Конфигурация системы</value>
  </data>
  <data name="LanguageSettings" xml:space="preserve">
    <value>Язык</value>
  </data>
  <data name="SelectLanguage" xml:space="preserve">
    <value>Выберите язык:</value>
  </data>
  <data name="LoadingData" xml:space="preserve">
    <value>Загрузка данных...</value>
  </data>
  <data name="DataSaved" xml:space="preserve">
    <value>Данные успешно сохранены</value>
  </data>
  <data name="ErrorOccurred" xml:space="preserve">
    <value>Произошла ошибка</value>
  </data>
  <data name="ConnectionError" xml:space="preserve">
    <value>Ошибка подключения</value>
  </data>
  <data name="MenuNewProject" xml:space="preserve">
    <value>Новый проект</value>
  </data>
  <data name="MenuOpenHistoryProject" xml:space="preserve">
    <value>Открыть проект</value>
  </data>
  <data name="MenuRefreshHistoryProject" xml:space="preserve">
    <value>Обновить список</value>
  </data>
  <data name="MenuStartCollect" xml:space="preserve">
    <value>Начать сбор данных</value>
  </data>
  <data name="MenuStopCollect" xml:space="preserve">
    <value>Остановить сбор данных</value>
  </data>
  <data name="MenuNewGraphMonitor" xml:space="preserve">
    <value>Новый монитор</value>
  </data>
  <data name="MenuOpenGraphMonitor" xml:space="preserve">
    <value>Открыть монитор</value>
  </data>
  <data name="MenuProjectLog" xml:space="preserve">
    <value>Журнал проекта</value>
  </data>
  <data name="MenuExport" xml:space="preserve">
    <value>Экспорт</value>
  </data>
  <data name="MenuExportPanorama" xml:space="preserve">
    <value>Панорама данных</value>
  </data>
  <data name="MenuExportTimePeriod" xml:space="preserve">
    <value>Данные за период</value>
  </data>
  <data name="MenuHistoricalDataQuery" xml:space="preserve">
    <value>Исторический анализ</value>
  </data>
  <data name="MenuSensorSettings" xml:space="preserve">
    <value>Датчики</value>
  </data>
  <data name="MenuProjectSettings" xml:space="preserve">
    <value>Настройки проекта</value>
  </data>
  <data name="MenuMeasurementUnitSettings" xml:space="preserve">
    <value>Системы единиц</value>
  </data>
  <data name="MenuUnitTypeListSettings" xml:space="preserve">
    <value>Настройки типов единиц</value>
  </data>
  <data name="MenuTCPNetworkDeviceSettings" xml:space="preserve">
    <value>Устройства TCP/IP</value>
  </data>
  <data name="MenuRedisServiceSettings" xml:space="preserve">
    <value>Сервис Redis</value>
  </data>
  <data name="MenuModBusDeviceSettings" xml:space="preserve">
    <value>Устройства ModBus</value>
  </data>
  <data name="MenuModBusTCPDataSettings" xml:space="preserve">
    <value>ModBus TCP</value>
  </data>
  <data name="MenuS7PointTableSettings" xml:space="preserve">
    <value>Параметры S7</value>
  </data>
  <data name="MenuInterlockRuleSettings" xml:space="preserve">
    <value>Правила безопасности</value>
  </data>
  <data name="MenuGBGasProductionParameterSettings" xml:space="preserve">
    <value>Параметры газа GB</value>
  </data>
  <data name="MenuUSGasProductionParameterSettings" xml:space="preserve">
    <value>Параметры газа US</value>
  </data>
  <data name="MenuSandVolumeParameterSettings" xml:space="preserve">
    <value>Расчет песка</value>
  </data>
  <data name="MenuLanguageSettings" xml:space="preserve">
    <value>Настройки языка</value>
  </data>
  <data name="MenuAlarmSettings" xml:space="preserve">
    <value>Настройка пороговых оповещений</value>
  </data>
  <data name="MenuAlarmInformation" xml:space="preserve">
    <value>Текущие оповещения</value>
  </data>
  <data name="MenuAlarmLog" xml:space="preserve">
    <value>Журнал оповещений</value>
  </data>
  <data name="MenuVerticalWindowArrangement" xml:space="preserve">
    <value>Вертикальная расположение</value>
  </data>
  <data name="MenuHorizontalWindowArrangement" xml:space="preserve">
    <value>Горизонтальное расположение</value>
  </data>
  <data name="MenuCloseAllWindows" xml:space="preserve">
    <value>Закрыть все</value>
  </data>
  <data name="MenuSetAsFloatingWindow" xml:space="preserve">
    <value>Плавающее окно</value>
  </data>
  <data name="MenuConfigureWebWindow" xml:space="preserve">
    <value>Веб-интерфейс</value>
  </data>
  <data name="MenuRegister" xml:space="preserve">
    <value>Регистрация</value>
  </data>
  <data name="ButtonStartCollection" xml:space="preserve">
    <value>Пуск</value>
  </data>
  <data name="ButtonStopCollection" xml:space="preserve">
    <value>Стоп</value>
  </data>
  <data name="ButtonSensorSettings" xml:space="preserve">
    <value>Датчики</value>
  </data>
  <data name="ButtonDataMonitor" xml:space="preserve">
    <value>Мониторинг</value>
  </data>
  <data name="Button3DFlowDiagram" xml:space="preserve">
    <value>3D вид</value>
  </data>
  <data name="Button2DFlowDiagram" xml:space="preserve">
    <value>2D вид</value>
  </data>
  <data name="ButtonLaserPlatform" xml:space="preserve">
    <value>Лазерная система</value>
  </data>
  <data name="LabelServiceNotStarted" xml:space="preserve">
    <value>Сервис неактивен</value>
  </data>
  <data name="LabelSoftwareNotRegistered" xml:space="preserve">
    <value>Программа не зарегистрирована</value>
  </data>
  <data name="ButtonStartService" xml:space="preserve">
    <value>Запустить сервис</value>
  </data>
  <data name="LabelCurrentProject" xml:space="preserve">
    <value>Скважина:</value>
  </data>
  <!-- Help Menu Items -->
  <data name="MenuLanguageEnglish" xml:space="preserve">
    <value>Английский</value>
  </data>
  <data name="MenuLanguageChinese" xml:space="preserve">
    <value>Китайский</value>
  </data>
  <data name="MenuLanguageRussian" xml:space="preserve">
    <value>Русский</value>
  </data>
  
  <!-- AlarmListControl Column Headers -->
  <data name="AlarmSensorName" xml:space="preserve">
    <value>Название датчика</value>
  </data>
  <data name="AlarmLevel" xml:space="preserve">
    <value>Уровень тревоги</value>
  </data>
  <data name="AlarmCondition" xml:space="preserve">
    <value>Условие</value>
  </data>
  <data name="AlarmValue" xml:space="preserve">
    <value>Значение тревоги</value>
  </data>
  <data name="AlarmRealTimeValue" xml:space="preserve">
    <value>Текущее значение</value>
  </data>
  <data name="AlarmContent" xml:space="preserve">
    <value>Содержание тревоги</value>
  </data>
  <data name="AlarmStatus" xml:space="preserve">
    <value>Статус</value>
  </data>
  <data name="AlarmStartTime" xml:space="preserve">
    <value>Время начала</value>
  </data>
  <data name="AlarmStopPlay" xml:space="preserve">
    <value>Остановить звук</value>
  </data>
  
  <!-- Alarm Level Names -->
  <data name="AlarmLevelCritical" xml:space="preserve">
    <value>Критический</value>
  </data>
  <data name="AlarmLevelHigh" xml:space="preserve">
    <value>Высокий</value>
  </data>
  <data name="AlarmLevelMedium" xml:space="preserve">
    <value>Средний</value>
  </data>
  <data name="AlarmLevelLow" xml:space="preserve">
    <value>Низкий</value>
  </data>
  
  <!-- Alarm Condition Symbols -->
  <data name="ConditionGreaterThan" xml:space="preserve">
    <value>&gt;</value>
  </data>
  <data name="ConditionGreaterEqual" xml:space="preserve">
    <value>&gt;=</value>
  </data>
  <data name="ConditionLessThan" xml:space="preserve">
    <value>&lt;</value>
  </data>
  <data name="ConditionLessEqual" xml:space="preserve">
    <value>&lt;=</value>
  </data>
  <data name="ConditionEqual" xml:space="preserve">
    <value>=</value>
  </data>
  <data name="ConditionNotEqual" xml:space="preserve">
    <value>&lt;&gt;</value>
  </data>
  
  <!-- Warning Type Descriptions -->
  <data name="WarningTypeLessThanLower" xml:space="preserve">
    <value>Меньше нижнего предела</value>
  </data>
  <data name="WarningTypeGreaterThanUpper" xml:space="preserve">
    <value>Больше верхнего предела</value>
  </data>
  <data name="WarningTypeLessEqualLower" xml:space="preserve">
    <value>&lt;= нижнего предела</value>
  </data>
  <data name="WarningTypeGreaterEqualUpper" xml:space="preserve">
    <value>&gt;= верхнего предела</value>
  </data>
  <data name="WarningTypeEqualValue" xml:space="preserve">
    <value>Равно значению</value>
  </data>
  <data name="WarningTypeUnknown" xml:space="preserve">
    <value>Неизвестный тип</value>
  </data>

  <!-- SensorAlarmConfigControl Strings -->
  <data name="SensorAlarmConfigList" xml:space="preserve">
    <value>Список конфигураций оповещений датчиков</value>
  </data>
  <data name="AddMenuItem" xml:space="preserve">
    <value>Добавить</value>
  </data>
  <data name="DeleteMenuItem" xml:space="preserve">
    <value>Удалить</value>
  </data>
  <data name="ColumnSN" xml:space="preserve">
    <value>Серийный №</value>
  </data>
  <data name="ColumnID" xml:space="preserve">
    <value>ИД</value>
  </data>
  <data name="ColumnName" xml:space="preserve">
    <value>Название</value>
  </data>
  <data name="SensorInfo" xml:space="preserve">
    <value>Информация о датчике</value>
  </data>
  <data name="LabelSN" xml:space="preserve">
    <value>Серийный №:</value>
  </data>
  <data name="LabelID" xml:space="preserve">
    <value>ИД:</value>
  </data>
  <data name="LabelName" xml:space="preserve">
    <value>Название:</value>
  </data>
  <data name="ThresholdAlarmRules" xml:space="preserve">
    <value>Пороговые правила оповещений</value>
  </data>
  <data name="ColumnSeverity" xml:space="preserve">
    <value>Серьезность</value>
  </data>
  <data name="ColumnOperator" xml:space="preserve">
    <value>Оператор</value>
  </data>
  <data name="ColumnThreshold" xml:space="preserve">
    <value>Порог</value>
  </data>
  <data name="ColumnDeadband" xml:space="preserve">
    <value>Зона нечувствительности</value>
  </data>
  <data name="ColumnMessage" xml:space="preserve">
    <value>Сообщение</value>
  </data>
  <data name="TrendAlarmRules" xml:space="preserve">
    <value>Правила трендовых оповещений</value>
  </data>
  <data name="ColumnRateOfChange" xml:space="preserve">
    <value>Скорость изменения</value>
  </data>
  <data name="ColumnTimeWindow" xml:space="preserve">
    <value>Временное окно (сек)</value>
  </data>
  <data name="ColumnDirection" xml:space="preserve">
    <value>Направление</value>
  </data>
  
  <!-- ThresholdAlarmRuleEditor Strings -->
  <data name="LabelAlarmLevel" xml:space="preserve">
    <value>Уровень оповещения:</value>
  </data>
  <data name="LabelComparisonOperator" xml:space="preserve">
    <value>Оператор:</value>
  </data>
  <data name="LabelThreshold" xml:space="preserve">
    <value>Порог:</value>
  </data>
  <data name="LabelDeadband" xml:space="preserve">
    <value>Зона нечувствительности:</value>
  </data>
  <data name="LabelAlarmMessage" xml:space="preserve">
    <value>Сообщение:</value>
  </data>
  <data name="OperatorGreaterThan" xml:space="preserve">
    <value>Больше чем (&gt;)</value>
  </data>
  <data name="OperatorGreaterEqual" xml:space="preserve">
    <value>Больше или равно (&gt;=)</value>
  </data>
  <data name="OperatorLessThan" xml:space="preserve">
    <value>Меньше чем (&lt;)</value>
  </data>
  <data name="OperatorLessEqual" xml:space="preserve">
    <value>Меньше или равно (&lt;=)</value>
  </data>
  <data name="OperatorEqual" xml:space="preserve">
    <value>Равно (=)</value>
  </data>
  <data name="OperatorNotEqual" xml:space="preserve">
    <value>Не равно (&lt;&gt;)</value>
  </data>
  
  <!-- TrendAlarmRuleEditor Strings -->
  <data name="LabelRateOfChange" xml:space="preserve">
    <value>Скорость изменения:</value>
  </data>
  <data name="LabelTimeWindow" xml:space="preserve">
    <value>Временное окно (сек):</value>
  </data>
  <data name="LabelTrendDirection" xml:space="preserve">
    <value>Направление тренда:</value>
  </data>
  <data name="DirectionRising" xml:space="preserve">
    <value>Повышение</value>
  </data>
  <data name="DirectionFalling" xml:space="preserve">
    <value>Падение</value>
  </data>
  <data name="DirectionBoth" xml:space="preserve">
    <value>Двунаправленный</value>
  </data>
  
  <!-- UnitTypeListSettingWindow Strings -->
  <data name="UnitTypeListSettingTitle" xml:space="preserve">
    <value>Настройки списка типов единиц</value>
  </data>
  <data name="UnitTypeListSettingSystemTemplate" xml:space="preserve">
    <value>Настройки списка типов единиц - Системный шаблон</value>
  </data>
  <data name="UnitTypeListSettingDescription" xml:space="preserve">
    <value>Управление списком типов единиц в системе, поддержка операций добавления, удаления и изменения</value>
  </data>
  <data name="UnitTypeListSettingIndex" xml:space="preserve">
    <value>Индекс</value>
  </data>
  <data name="UnitTypeListSettingName" xml:space="preserve">
    <value>Название типа единицы</value>
  </data>
  <data name="UnitTypeListSettingSaveAndExit" xml:space="preserve">
    <value>Сохранить и выйти</value>
  </data>
  <data name="ColumnIndex" xml:space="preserve">
    <value>Индекс</value>
  </data>
  <data name="ColumnUnitType" xml:space="preserve">
    <value>Тип единицы</value>
  </data>
  <data name="LabelIndex" xml:space="preserve">
    <value>Индекс:</value>
  </data>
  <data name="LabelUnitType" xml:space="preserve">
    <value>Тип единицы:</value>
  </data>
  <data name="ButtonAddModify" xml:space="preserve">
    <value>Добавить/Изменить</value>
  </data>
  <data name="ButtonDelete" xml:space="preserve">
    <value>Удалить</value>
  </data>
  <data name="ButtonSaveAndExit" xml:space="preserve">
    <value>Сохранить и выйти</value>
  </data>
  <data name="MessageUnitTypeNameRequired" xml:space="preserve">
    <value>Название типа единицы не может быть пустым!</value>
  </data>
  <data name="MessageSelectItemToDelete" xml:space="preserve">
    <value>Пожалуйста, сначала выберите элемент для удаления!</value>
  </data>
  <data name="MessageConfirmDelete" xml:space="preserve">
    <value>Вы уверены, что хотите удалить выбранный элемент?</value>
  </data>
  <data name="MessageConfirmDeleteUnitType" xml:space="preserve">
    <value>Вы уверены, что хотите удалить выбранный тип единицы?</value>
  </data>
  <data name="MessageUnitTypeAlreadyExists" xml:space="preserve">
    <value>Тип единицы уже существует, используйте другое название</value>
  </data>
  <data name="MessageUnitTypeListSaved" xml:space="preserve">
    <value>Список типов единиц сохранен</value>
  </data>
  <data name="MessagePrompt" xml:space="preserve">
    <value>Подсказка</value>
  </data>
</root>