# DrawSeriePointData 中使用 SeriesProperty.Unit 的实现指南

## 🎯 **目标**

在 `DrawSeriePointData` 方法中，数据单位不要取坐标单位，要取 `SeriesProperty` 对应的 `Unit` 字段。

## 📋 **实现步骤**

### 1. **RealTimeCurveChartSensorInfo 添加 Unit 字段**

```csharp
public partial class RealTimeCurveChartSensorInfo
{
    /// <summary>
    /// 传感器单位信息
    /// 用于在DrawSeriePointData时显示正确的单位，而不是使用坐标轴单位
    /// </summary>
    public string Unit { get; set; } = "";
}
```

### 2. **在名称更新时同时更新 Unit**

#### 方法1：在创建 RealTimeCurveChartSensorInfo 时设置 Unit

```csharp
// 在 MonitorWindowView 的初始化方法中
private void CreateSensorInfo(ProjectStruct.SensorInfo sensorInfo)
{
    var chartSensorInfo = new RealTimeCurveChartSensorInfo
    {
        SensorId = sensorInfo.Id,
        Name = sensorInfo.Name,
        Unit = sensorInfo.Unitstr ?? "",  // ✅ 直接从 sensorInfo.Unitstr 获取
        Color = ConvertColorToArgb(sensorColor),
        IsEnabled = true,
        YAxisIndex = yAxisIndex
    };
}
```

#### 方法2：使用扩展方法批量更新

```csharp
// 使用扩展方法更新传感器信息
private void UpdateSensorInfoUnits()
{
    var sensorInfoList = Global.Messages.TryGetValue("SensorInfoList", out var msg) ? msg as SensorInfoList : null;
    if (sensorInfoList == null) return;

    var currentProperties = CurveChart.GetAllProperty();
    if (currentProperties?.YAxes == null) return;

    foreach (var yAxis in currentProperties.YAxes)
    {
        if (yAxis.Sensors != null)
        {
            // 使用扩展方法批量更新单位
            int updatedCount = yAxis.Sensors.UpdateUnitsFromSensorInfoList(sensorInfoList);
            Logger.Write($"Updated {updatedCount} sensor units in Y-axis '{yAxis.Name}'");
        }
    }

    // 重新应用配置
    CurveChart.SetAllProperty(currentProperties);
}
```

### 3. **在 DrawSeriePointData 中使用 SeriesProperty.Unit**

#### 原来的方式（❌ 不推荐）
```csharp
private void DrawSeriePointData(Graphics graphics, PointInfo point, YAxisInfo yAxis)
{
    // ❌ 使用坐标轴单位
    string unit = yAxis.Unit;
    string displayText = $"{point.DataValue:F2} {unit}";
    
    // 绘制数据点标签
    graphics.DrawString(displayText, font, brush, point.PosX, point.PosY);
}
```

#### 新的方式（✅ 推荐）
```csharp
private void DrawSeriePointData(Graphics graphics, PointInfo point, RealTimeCurveChartSensorInfo sensorInfo)
{
    // ✅ 使用传感器自身的单位
    string unit = DrawSeriePointDataUnitHelper.GetDisplayUnit(sensorInfo, "");
    string displayText = DrawSeriePointDataUnitHelper.FormatDataPointText(
        point.DataValue, sensorInfo, 2);
    
    // 绘制数据点标签
    graphics.DrawString(displayText, font, brush, point.PosX, point.PosY);
}
```

#### 更完整的实现
```csharp
private void DrawSeriePointData(Graphics graphics, PointInfo point, RealTimeCurveChartSensorInfo sensorInfo, YAxisInfo yAxis)
{
    try
    {
        // 优先使用传感器的单位，如果为空则使用坐标轴单位作为备用
        string unit = DrawSeriePointDataUnitHelper.GetDisplayUnit(sensorInfo, yAxis?.Unit ?? "");
        
        // 根据传感器精度设置格式化数据
        int precision = sensorInfo.DataPrecision > 0 ? sensorInfo.DataPrecision : 2;
        
        // 创建显示文本
        string displayText = DrawSeriePointDataUnitHelper.FormatDataPointText(
            point.DataValue, sensorInfo, precision);
        
        // 创建工具提示文本（用于鼠标悬停）
        string tooltipText = DrawSeriePointDataUnitHelper.CreateTooltipText(
            sensorInfo, point.DataValue, point.DataTime, precision);
        
        // 绘制数据点
        DrawDataPoint(graphics, point, displayText);
        
        // 设置工具提示（如果支持）
        SetPointTooltip(point, tooltipText);
        
        Logger.Write($"Drew data point for sensor '{sensorInfo.Name}' with unit '{unit}': {displayText}");
    }
    catch (Exception ex)
    {
        Logger.Write($"Error in DrawSeriePointData: {ex.Message}");
        
        // 备用绘制方式
        string fallbackText = $"{point.DataValue:F2}";
        DrawDataPoint(graphics, point, fallbackText);
    }
}
```

### 4. **确保数据一致性的验证方法**

```csharp
/// <summary>
/// 验证所有传感器的单位字段是否与最新的SensorInfoList同步
/// 建议在调试时调用，确保数据一致性
/// </summary>
private void ValidateSensorUnitConsistency()
{
    try
    {
        var sensorInfoList = Global.Messages.TryGetValue("SensorInfoList", out var msg) ? msg as SensorInfoList : null;
        if (sensorInfoList == null) return;

        var currentProperties = CurveChart.GetAllProperty();
        if (currentProperties?.YAxes == null) return;

        int totalMismatches = 0;

        foreach (var yAxis in currentProperties.YAxes)
        {
            if (yAxis.Sensors != null)
            {
                int mismatches = yAxis.Sensors.ValidateUnitSynchronization(sensorInfoList);
                totalMismatches += mismatches;
                
                if (mismatches > 0)
                {
                    Logger.Write($"Found {mismatches} unit mismatches in Y-axis '{yAxis.Name}'");
                }
            }
        }

        if (totalMismatches == 0)
        {
            Logger.Write("All sensor units are synchronized with SensorInfoList");
        }
        else
        {
            Logger.Write($"Total unit mismatches found: {totalMismatches}");
        }
    }
    catch (Exception ex)
    {
        Logger.Write($"Error in ValidateSensorUnitConsistency: {ex.Message}");
    }
}
```

## 🔧 **集成到现有代码**

### 1. **在 MonitorWindowView 中集成**

```csharp
// 在 OnSetMessage 方法中添加
private void OnSetMessage(string msg)
{
    try
    {
        // 现有的处理逻辑
        HandleSensorInfoListUpdate(msg);
        
        // 验证数据一致性（调试时使用）
        #if DEBUG
        if (msg == "SensorInfoList")
        {
            ValidateSensorUnitConsistency();
        }
        #endif
    }
    catch (Exception ex)
    {
        Logger.Write(ex);
    }
}
```

### 2. **修改现有的 DrawSeriePointData 调用**

```csharp
// 找到现有的 DrawSeriePointData 调用位置，修改参数
foreach (var curveLine in chart.CurveLines)
{
    // 获取对应的 RealTimeCurveChartSensorInfo
    var sensorInfo = GetSensorInfoBySensorId(curveLine.SensorId);
    
    foreach (var point in curveLine.Points)
    {
        if (point.IsFocusPoint)
        {
            // ✅ 传递 sensorInfo 而不是 yAxis
            DrawSeriePointData(graphics, point, sensorInfo);
        }
    }
}
```

## 🎯 **预期效果**

实现后，您将获得：

1. **✅ 正确的单位显示**：数据点显示传感器实际单位，不是坐标轴单位
2. **✅ 实时单位更新**：当传感器单位修改时，显示会自动更新
3. **✅ 数据一致性**：SeriesProperty.Unit 与 sensorInfo.Unitstr 保持同步
4. **✅ 向后兼容**：如果传感器单位为空，会自动使用坐标轴单位作为备用

## 🔍 **测试验证**

1. **修改传感器单位**：在传感器设置中修改单位
2. **检查数据点显示**：确认数据点显示的是新单位
3. **验证工具提示**：鼠标悬停时显示正确的单位信息
4. **检查日志**：查看单位更新的日志记录

这样就完成了从坐标轴单位到 SeriesProperty.Unit 的切换！
