﻿using SDHD.DC.DataCollection.ApplicationCore.Domain.Entities;
using SDHD.DC.DataMonitor.Utils;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SDHD.DC.DataMonitor.Models.Setting.ModBus
{
    internal class ModBusTCPSettingViewModel : NotifyPropertyChangedModel
    {

        private ModBusTCP _CurModBusTCP;
        public ModBusTCP CurModBusTCP
        {
            get { return _CurModBusTCP; }
            set
            {
                _CurModBusTCP = value;
                OnPropertyChanged(nameof(CurModBusTCP));
            }
        }
        private ModBusTCPSetting _ModBusTCPSetting;
        public ModBusTCPSetting ModBusTCPSetting
        {
            get { return _ModBusTCPSetting; }
            set
            {
                _ModBusTCPSetting = value;
                OnPropertyChanged(nameof(ModBusTCPSetting));
            }
        }

        public ObservableCollection<ModBusTCP> ModBusTCPs { get; set; } = new();
        public ObservableCollection<ModBusTCP> ModBusTCPSelecteds { get; set; } = new();

        public List<SelectItem> DataFormats { get; set; } = new()
        {
            new(){ Text="ABCD",Value="0" },
            new(){ Text="CDAB",Value="1" },
            new(){ Text="BADC",Value="2" },
            new(){ Text="DCBA",Value="3" },
        };
    }
}
