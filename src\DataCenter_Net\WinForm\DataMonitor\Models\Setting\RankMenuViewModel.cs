﻿using SDHD.DC.DataCollection.ApplicationCore.Domain.Entities;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using Menu = SDHD.DC.DataCollection.ApplicationCore.Domain.Entities.Menu;

namespace SDHD.DC.DataMonitor.Models.Setting
{
    public class RankMenuViewModel : NotifyPropertyChangedModel
    {
        public string RankName { get; set; }
        public List<Menu> Menus { get; set; }
        public ObservableCollection<Rank> RankList { get; set; } = new();
        public ObservableCollection<MenuTreeViewItem> MenuList { get; set; } = new();

        public class MenuTreeViewItem : NotifyPropertyChangedModel
        {
            public Menu Menu { get; set; }
            public bool IsChecked { get; set; }
            public ObservableCollection<MenuTreeViewItem> Children { get; set; }

            public MenuTreeViewItem(Menu menu, bool isChecked = false, ObservableCollection<MenuTreeViewItem> children = null)
            {
                Menu = menu;
                IsChecked = isChecked;
                Children = children ?? new();
            }

        }

        //public ObservableCollection<MenuTreeViewItem> MenuTree { get; set; } = new();
    }
}
