# DrawSeriePointData 单位修复验证指南

## 🎯 **修复内容验证**

### **修复前的问题**
- 鼠标移动时显示的数据点单位使用的是Y轴单位，而不是传感器的实际单位
- 传感器设置更新后，数据点显示的单位没有同步更新

### **修复后的效果**
- 数据点显示使用传感器的实际单位（从Global.Messages实时获取）
- 传感器设置修改后，数据点显示的单位立即更新

## 🔍 **验证步骤**

### **步骤1：基本功能验证**
1. 启动应用程序
2. 打开监控视图，显示曲线图
3. 将鼠标移动到曲线上，观察数据点显示
4. 确认显示的信息包含：
   - 传感器名称
   - 时间戳
   - 数值和正确的单位

### **步骤2：单位更新验证**
1. 在传感器设置中修改某个传感器的单位
2. 保存设置
3. 观察曲线图上的数据点显示
4. 确认数据点显示的单位已经更新为新单位

### **步骤3：实时性验证**
1. 修改传感器单位后，立即将鼠标移动到曲线上
2. 确认数据点显示的是新单位，无需重新打开窗口

### **步骤4：日志验证**
1. 查看应用程序日志
2. 确认有以下日志信息：
   ```
   Updated Seriespropert: Sensor X, Unit: '新单位'
   DrawSeriePointData: Sensor=传感器名称, Time=时间, Value=数值 单位
   ```

## ✅ **预期结果**

### **修复前**
- 数据点显示：`传感器名称|时间|数值 Y轴名称`
- 单位来源：Y轴名称（可能不正确）
- 更新延迟：需要重新打开窗口

### **修复后**
- 数据点显示：`传感器名称|时间|数值 传感器单位`
- 单位来源：传感器的实际单位（从Global.Messages实时获取）
- 更新实时：传感器设置修改后立即生效

## 🐛 **故障排除**

### **如果单位仍然显示不正确**
1. 检查Global.Messages["SensorInfoList"]是否有数据
2. 确认传感器ID匹配正确
3. 查看日志中的错误信息

### **如果更新不及时**
1. 确认HandleSensorInfoListUpdate方法被正确调用
2. 检查Dispatcher.BeginInvoke是否正常工作
3. 验证反射访问是否成功

### **如果编译错误**
1. 确认所有必要的using语句已添加
2. 检查类型引用是否正确
3. 验证命名空间引用

## 📋 **测试清单**

- [ ] 基本数据点显示功能正常
- [ ] 传感器名称显示正确
- [ ] 传感器单位显示正确
- [ ] 单位更新实时生效
- [ ] 日志信息正确记录
- [ ] 无编译错误
- [ ] 无运行时异常

## 🎉 **成功标志**

当所有测试项目都通过时，说明修复成功：

1. **功能正确性**：数据点显示使用传感器的实际单位
2. **实时性**：传感器设置修改后立即生效
3. **稳定性**：无编译错误和运行时异常
4. **可维护性**：代码结构清晰，易于理解和维护 