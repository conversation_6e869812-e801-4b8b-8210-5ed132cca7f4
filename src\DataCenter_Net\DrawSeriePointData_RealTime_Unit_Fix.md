# DrawSeriePointData 实时单位获取修复方案

## 🎯 **问题描述**

在更改了传感器单位之后，在 `DrawSeriePointData` 的时候，没有按照传感器最新的单位显示。

## 🔍 **根本原因**

`DrawSeriePointData` 方法使用的是 `SeriesProperty.Unit` 字段，这个字段可能不是最新的：
1. **SeriesProperty.Unit** 是在创建时设置的，可能已经过时
2. **最新的单位信息** 存储在 `Global.Messages["SensorInfoList"]` 中
3. **需要实时获取** 而不是依赖缓存的值

## ✅ **解决方案**

### **方案1：在 DrawSeriePointData 中实时获取单位（推荐）**

```csharp
private void DrawSeriePointData(Graphics graphics, PointInfo point, int sensorId, YAxisInfo yAxis)
{
    try
    {
        // 🎯 关键修复：实时从Global.Messages获取最新单位
        string unit = DrawSeriePointDataUnitHelper.GetLatestSensorUnit(sensorId);
        
        // 如果获取失败，使用备用单位
        if (string.IsNullOrEmpty(unit))
            unit = yAxis?.Unit ?? "";

        // 格式化显示文本
        string displayText = DrawSeriePointDataUnitHelper.FormatDataPointText(
            point.DataValue, sensorId, 2, unit);
        
        // 绘制数据点标签
        graphics.DrawString(displayText, font, brush, point.PosX, point.PosY);
        
        // 调试日志
        System.Diagnostics.Debug.WriteLine($"Drew point for sensor {sensorId} with unit '{unit}': {displayText}");
    }
    catch (Exception ex)
    {
        // 备用绘制方式
        string fallbackText = $"{point.DataValue:F2}";
        graphics.DrawString(fallbackText, font, brush, point.PosX, point.PosY);
        System.Diagnostics.Debug.WriteLine($"Error in DrawSeriePointData: {ex.Message}");
    }
}
```

### **方案2：使用重载的格式化方法**

```csharp
private void DrawSeriePointData(Graphics graphics, PointInfo point, int sensorId)
{
    // 🎯 使用重载方法，直接传入sensorId，自动获取最新单位
    string displayText = DrawSeriePointDataUnitHelper.FormatDataPointText(
        point.DataValue, sensorId, 2);
    
    // 绘制数据点标签
    graphics.DrawString(displayText, font, brush, point.PosX, point.PosY);
}
```

### **方案3：创建工具提示时也使用最新信息**

```csharp
private void SetPointTooltip(PointInfo point, int sensorId)
{
    // 🎯 使用重载方法，直接传入sensorId，自动获取最新名称和单位
    string tooltipText = DrawSeriePointDataUnitHelper.CreateTooltipText(
        sensorId, point.DataValue, point.DataTime, 2);
    
    // 设置工具提示
    point.Label.Content = tooltipText;
}
```

## 🔧 **具体实现步骤**

### **第一步：找到 DrawSeriePointData 方法**

在您的代码中找到 `DrawSeriePointData` 方法，通常在以下位置：
- `RealTimeCurveChart.cs`
- `MonitorWindowChart.cs`
- 或其他绘制相关的类中

### **第二步：修改方法签名（如果需要）**

确保方法能够获取到 `sensorId`：

```csharp
// 原来的方法签名（可能是这样）
private void DrawSeriePointData(Graphics graphics, PointInfo point, YAxisInfo yAxis)

// 修改后的方法签名（推荐）
private void DrawSeriePointData(Graphics graphics, PointInfo point, int sensorId, YAxisInfo yAxis)
```

### **第三步：替换单位获取逻辑**

```csharp
// ❌ 原来的方式（使用坐标轴单位或缓存的单位）
string unit = yAxis.Unit;
// 或
string unit = sensorInfo.Unit;

// ✅ 新的方式（实时获取最新单位）
string unit = DrawSeriePointDataUnitHelper.GetLatestSensorUnit(sensorId);
if (string.IsNullOrEmpty(unit))
    unit = yAxis?.Unit ?? ""; // 备用方案
```

### **第四步：更新调用位置**

找到调用 `DrawSeriePointData` 的地方，确保传递正确的 `sensorId`：

```csharp
// 在绘制循环中
foreach (var curveLine in chart.CurveLines)
{
    if (int.TryParse(curveLine.SensorId, out int sensorId))
    {
        foreach (var point in curveLine.Points)
        {
            if (point.IsFocusPoint)
            {
                // ✅ 传递 sensorId
                DrawSeriePointData(graphics, point, sensorId, yAxis);
            }
        }
    }
}
```

## 🎯 **关键优势**

### **1. 实时性**
- ✅ 每次绘制都获取最新的传感器单位
- ✅ 不依赖可能过时的缓存数据
- ✅ 单位修改后立即生效

### **2. 可靠性**
- ✅ 有完整的错误处理和备用方案
- ✅ 不会因为获取失败而影响绘制
- ✅ 提供调试日志便于排查问题

### **3. 简单性**
- ✅ 只需要传入 `sensorId`
- ✅ 自动处理所有的获取逻辑
- ✅ 提供多种重载方法适应不同场景

## 🔍 **测试验证**

### **测试步骤**
1. **修改传感器单位**：在传感器设置中修改单位并保存
2. **观察数据点显示**：确认数据点显示的是新单位
3. **检查工具提示**：鼠标悬停时显示正确的单位信息
4. **查看调试日志**：确认获取到了最新的单位信息

### **预期结果**
- ✅ 数据点标签显示新单位
- ✅ 工具提示显示新单位
- ✅ 不需要重新打开窗口
- ✅ 实时更新，无延迟

## 🚨 **注意事项**

### **1. 性能考虑**
- 每次绘制都会访问 `Global.Messages`，但这是轻量级操作
- 如果性能有问题，可以考虑在绘制开始时批量获取所有传感器信息

### **2. 错误处理**
- 必须有备用方案，避免因为获取失败而影响绘制
- 使用 `try-catch` 包装获取逻辑

### **3. 调试支持**
- 添加调试日志，便于排查问题
- 可以通过日志确认是否获取到了最新的单位信息

## 📝 **完整示例**

```csharp
private void DrawSeriePointData(Graphics graphics, PointInfo point, int sensorId, YAxisInfo yAxis)
{
    try
    {
        // 🎯 实时获取最新单位
        string unit = DrawSeriePointDataUnitHelper.GetLatestSensorUnit(sensorId);
        string sensorName = DrawSeriePointDataUnitHelper.GetLatestSensorName(sensorId);
        
        // 备用方案
        if (string.IsNullOrEmpty(unit))
            unit = yAxis?.Unit ?? "";
        if (string.IsNullOrEmpty(sensorName))
            sensorName = $"Sensor {sensorId}";

        // 格式化显示文本
        string displayText = $"{point.DataValue:F2} {unit}";
        
        // 创建工具提示
        string tooltipText = $"{sensorName}: {point.DataValue:F2} {unit}";
        if (point.DataTime.HasValue)
            tooltipText += $"\n{point.DataTime.Value:yyyy-MM-dd HH:mm:ss}";

        // 绘制数据点
        graphics.DrawString(displayText, font, brush, point.PosX, point.PosY);
        point.Label.Content = tooltipText;
        
        // 调试日志
        System.Diagnostics.Debug.WriteLine($"Drew point: Sensor {sensorId}, Unit '{unit}', Value {point.DataValue:F2}");
    }
    catch (Exception ex)
    {
        // 备用绘制
        string fallbackText = $"{point.DataValue:F2}";
        graphics.DrawString(fallbackText, font, brush, point.PosX, point.PosY);
        System.Diagnostics.Debug.WriteLine($"Error in DrawSeriePointData for sensor {sensorId}: {ex.Message}");
    }
}
```

**这样修改后，DrawSeriePointData 就能实时显示最新的传感器单位了！** 🎉
