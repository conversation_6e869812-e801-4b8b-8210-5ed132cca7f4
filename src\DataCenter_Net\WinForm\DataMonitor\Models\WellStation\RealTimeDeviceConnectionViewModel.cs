﻿using SDHD.DC.DataCollection.ApplicationCore.Domain.Entities;
using SDHD.DC.DataMonitor.Resources.Localization;
using System.Collections.Generic;

namespace SDHD.DC.DataMonitor.Models.WellStation
{
    public class RealTimeDeviceConnectionViewModel : NotifyPropertyChangedModel
    {
        private List<DeviceConnectionInfo> _DeviceConnectionInfos = new();
        public List<DeviceConnectionInfo> DeviceConnectionInfos
        {
            get { return _DeviceConnectionInfos; }
            set
            {
                _DeviceConnectionInfos = value;
                OnPropertyChanged(nameof(DeviceConnectionInfos));
            }
        }

        public class DeviceConnectionInfo : NotifyPropertyChangedModel
        {

            private string _IPAddress;
            public string IPAddress
            {
                get { return _IPAddress; }
                set
                {
                    if (_IPAddress == value) return;
                    _IPAddress = value;
                    OnPropertyChanged(nameof(IPAddress));
                }
            }

            private bool _IsConnected;
            public bool IsConnected
            {
                get { return _IsConnected; }
                set
                {
                    if (_IsConnected == value) return;
                    _IsConnected = value;
                    OnPropertyChanged(nameof(IsConnected));
                    OnPropertyChanged(nameof(IsConnectedStr));
                }
            }

            public string IsConnectedStr { get { return IsConnected ? ResourceHelper.GetString("RealTimeDeviceConnection_Online") : ResourceHelper.GetString("RealTimeDeviceConnection_Offline"); } }

        }
    }
}
