[
{
  "name": "<PERSON>",
  "definition": "随机数函数",
  "description": "<PERSON>(最小值，最大值，小数位个数)",
  "example": "参数为整数，例:<PERSON>(0,10,2)",
},
{
  "name": "Acc",
  "definition": "累积函数",
  "description": "Acc(参数1,参数2)",
  "example": "参数1为传感器名称，例:Acc(1#瞬时量,0),参数2为时间单位:0:天,1:小时,2:分,3:秒,4:毫秒",
},
{
  "name": "FLUENT",
  "definition": "计算流量函数",
  "description": "FLUENT()",
  "example": "需要设置系数，值=设备值*60/系数*100",
},
{
  "name": "SQRT",
  "definition": "开平方(求平方根)",
  "description": "SQRT(n)",
  "example": "SQRT(a^2+b^2)",
},
{
  "name": "SQRT",
  "definition": "开n次方",
  "description": "SQRT(m,n)",
  "example": "SQRT(体积,3)",
},
{
  "name": "ABS",
  "definition": "取绝对值",
  "description": "ABS(n)",
  "example": "ABS(高度)",
},
{
  "name": "Power",
  "definition": "乘n次方",
  "description": "Power(正方形边长,3)",
  "example": "xxx",
},
{
  "name": "Trunc",
  "definition": "取数值的整数部分",
  "description": "Trunc(频率/60)",
  "example": "Trunc(pi)=3",
},
{
  "name": "Frac",
  "definition": "取数值的小数部分",
  "description": "Frac(n)",
  "example": "Frac(pi)=0.141592...",
},
{
  "name": "Round",
  "definition": "四舍五入",
  "description": "Round(n)",
  "example": "Round(pi)=3",
},
{
  "name": "Min",
  "definition": "求几个数的最小数",
  "description": "Min(n1,n2)最多4个参数",
  "example": "Min(n1,n2)",
},
{
  "name": "Max",
  "definition": "求几个数的最大数",
  "description": "Max(n1,n2)最多4个参数",
  "example": "Max(n1,n2)",
},
{
  "name": "GetRecTimeSpace",
  "definition": "获取上次计算以来的时间差(单位:天)",
  "description": "GetRecTimeSpace()",
  "example": "GetRecTimeSpace()*1#气产量+Last",
},
{
  "name": "IF",
  "definition": "分支函数，条件满足时取参数2,否则取参数3",
  "description": "IF(条件,n2,n3)",
  "example": "IF(出口温度<0,0,入口温度)",
}
]