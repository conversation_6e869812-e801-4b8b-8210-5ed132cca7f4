﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SDHD.DC.DataMonitor.Models.Setting
{
    public class RedisConfigSettingViewModel : NotifyPropertyChangedModel
    {
        public string IPAddr { set; get; }
        public string Port { set; get; }
        public string Timeout { set; get; }
        public string UserName { set; get; }

        private bool _IsShowPwd = false;
        public bool IsShowPwd
        {
            get { return _IsShowPwd; }
            set
            {
                if (_IsShowPwd == value) return;
                _IsShowPwd = value;
            }
        }
    }
}
