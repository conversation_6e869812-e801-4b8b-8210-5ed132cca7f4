﻿using SDHD.DC.CemSys.ApplicationCore.Domain.Entities;
using SDHD.DC.CemSys.ApplicationCore.Domain.Request;
using SDHD.DC.CemSys.ApplicationCore.Domain.Response;
using SDHD.DC.CemSys.ApplicationCore.Interfaces.Repository;
using SDHD.DC.CemSys.ApplicationCore.Interfaces.Service;
using SDHD.DC.Utilities;
using SDHD.DC.Utilities.Constants;
using SDHD.DC.Utilities.Logging;
using SDHD.DC.Utilities.ServiceExtensions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SDHD.DC.CemSys.ApplicationCore.Services
{
    public class SystemService : ISystemService
    {
        private readonly ISystemRepository _SystemRepository;
        public readonly IWellStationRepository _WellStationRepository;
        public readonly ISystemHandler _SystemHandler;
        public SystemService()
        {
            _SystemRepository = ServiceHelper.GetService<ISystemRepository>();
            _WellStationRepository = ServiceHelper.GetService<IWellStationRepository>();
            _SystemHandler = ServiceHelper.GetService<ISystemHandler>();
        }
        public async Task<SysConfigResponse> GetSysConfig()
        {
            var response = new SysConfigResponse();
            var sysConfig = await _SystemRepository.GetSysConfig();
            response.Content.UnitName = sysConfig.UnitName;
            response.Content.DefaultWellStationId = sysConfig.DefaultWellStationID;
            response.Success();
            return response;
        }
        public async Task<BizResponse> UpdateSysToDefaultWellStationId(string wellStationId)
        {
            var response = new BizResponse();
            var sysConfig = await _SystemRepository.GetRegisterData();
            sysConfig.lastPro = wellStationId;
            await _SystemRepository.UpdateSysConfig(sysConfig);
            response.Success();
            return response;
        }
        #region register auth
        public async Task<RegistryResponse> Register(RegistryRequest request)
        {
            RegistryResponse response = new RegistryResponse();
            
            var registerData = request.Content;
            //取消注册验证
            //await _SystemRepository.GetRegisterData();
            //if (string.IsNullOrWhiteSpace(registerData.UserName))
            //{
            //    response.Fail(Errors.UserNameRequired);
            //    return response;
            //}
            //if (string.IsNullOrWhiteSpace(registerData.VerifyCode))
            //{
            //    response.Fail(Errors.VerifyCodeInvalid);
            //    return response;
            //}
            //if (string.IsNullOrWhiteSpace(registerData.VerifyCode))
            //{
            //    response.Fail(Errors.AuthCodeRequired);
            //    return response;
            //}
            //取消注册验证
            //if (registerData.VerifyCode != GenerateVerifyCode())
            //{
            //    response.Fail(Errors.VerifyCodeInvalid);
            //    return response;
            //}
            //if (!VerifyAuthCode(registerData.UserName, registerData.VerifyCode, registerData.AuthCode, out PermissionType sysPermission))
            //{
            //    response.Fail(Errors.AuthCodeInvalid);
            //    return response;
            //}

            //save the register data
            //await _SystemRepository.SaveRegisterData(new Domain.Entities.RegisterData()
            //{
            //    UserName = registerData.UserName,
            //    AuthCode = registerData.AuthCode
            //});
            response.Success();
            response.Content.IsAuthed = true;
            response.Content.UserName = registerData.UserName;
            response.Content.AccessToken = registerData.AuthCode;
            return response;
        }

        public async Task<RegistryResponse> SetRegister(RegistryRequest request)
        {
            RegistryResponse response = new RegistryResponse();
            var registerData = request.Content;
            //await _SystemRepository.GetRegisterData();
            if (string.IsNullOrWhiteSpace(registerData.UserName))
            {
                response.Fail(Errors.UserNameRequired);
                return response;
            }
            if (string.IsNullOrWhiteSpace(registerData.VerifyCode))
            {
                response.Fail(Errors.VerifyCodeInvalid);
                return response;
            }
            if (string.IsNullOrWhiteSpace(registerData.VerifyCode))
            {
                response.Fail(Errors.AuthCodeRequired);
                return response;
            }
            

            //save the register data
            await _SystemRepository.SaveRegisterData(new Domain.Entities.RegisterData()
            {
                UserName = registerData.UserName,
                AuthCode = registerData.AuthCode
            });
            response.Success();
            response.Content.IsAuthed = true;
            response.Content.UserName = registerData.UserName;
            response.Content.AccessToken = registerData.AuthCode;
            return response;
        }
        private string GenerateVerifyCode()
        {
            //var hardDiskSerNo = _SystemHandler.GetOSHardDiskID();
            //var cpuSerNo = _SystemHandler.GetCPUSerialNo();
            //string content = cpuSerNo + hardDiskSerNo;
            //content = StringHandler.MD5Encrypt32(content).Substring(8, 16);//only return 16

            string content = _SystemHandler.GetMechineNum();
            return content;
        }
        private bool VerifyAuthCode(string userName, string verifyCode, string authCode, out PermissionType permissions)
        {
            permissions = 0;
            try
            {
                if (string.IsNullOrEmpty(authCode) || !authCode.StartsWith("REG-") || authCode.Length != ("REG-".Length + 50))
                {
                    return false;
                }
                string c = authCode.Substring("REG-".Length);
                c = string.Join("", c.Reverse());
                var sChars = new char[10];
                int sId = 2;
                for (int i = 0; i < sChars.Length; i++)
                {
                    sChars[i] = c[sId];
                    c = c.Remove(sId, 1);
                    sId = sId + 2;
                }
                var s = string.Join("", sChars.Reverse());
                byte[] dataBytes = StringHandler.ConvertToHexBytes(c);
                byte[] pBytes = new byte[4];
                var d = dataBytes.Reverse().ToList();
                pBytes[0] = d[2]; d.RemoveAt(2);
                pBytes[1] = d[6]; d.RemoveAt(6);
                pBytes[2] = d[10]; d.RemoveAt(10);
                pBytes[3] = d[14]; d.RemoveAt(14);
                pBytes = pBytes.Reverse().ToArray();
                uint cP = BitConverter.ToUInt32(pBytes);
                if ((cP & 0x80000001) != 0x80000001)
                {
                    return false;
                }
                uint aP = (cP ^ 0x80000001) >> 1;
                string checkMd5 = BitConverter.ToString(d.ToArray()).Replace("-", "");
                string strP = BitConverter.ToString(pBytes).Replace("-", "");
                string content = string.Join(';', userName, verifyCode, strP, s);
                string md5 = StringHandler.MD5Encrypt32(content);
                if (checkMd5 == md5)
                {
                    permissions = (PermissionType)aP;
                    return true;
                }
            }
            catch
            {
                return false;
            }
            return false;
        }
        public async Task<RegistryDetailResponse> GetRegisterData()
        {
            var response = new RegistryDetailResponse();
            var registerData = await _SystemRepository.GetRegisterData();
            if (registerData != null)
            {
                response.Content.UserName = registerData.UserName;
                response.Content.AuthCode = registerData.AuthCode;
                response.Content.VerifyCode = registerData.machineCode.ToString();
                response.Content.LastPro=registerData.lastPro;
            }
            //response.Content.VerifyCode = GenerateVerifyCode();
            response.Success();
            return response;
        }
        #endregion
        public async Task<ServerConnectTestResponse> TestServerConnection(ServerConnectTestRequest request)
        {
            ServerConnectTestResponse response = new ServerConnectTestResponse();
            var canConnect = await _SystemRepository.TestServerConnection(request.Content.ServerIP, request.Content.Port, request.Content.ConnectionTimeout, out string error);
            response.Content.ConnectEnabled = canConnect;
            if (!canConnect)
            {
                response.Content.ErrorMsg = error;
            }
            response.Success();
            return response;
        }
        public async Task CloseServerConnection()
        {
            await _SystemRepository.CloseServerConnection();
        }
        #region sys external
        public async Task<string> GetDefaultWellStationTemplateId()
        {
            var sysExternal = await _SystemRepository.GetSysExternal();

            string defaultWellStationTemplateId = null;
            if (sysExternal != null)
            {
                defaultWellStationTemplateId = sysExternal.DefaultWellStationTemplateId;
            }
            if (string.IsNullOrEmpty(defaultWellStationTemplateId))
            {
                defaultWellStationTemplateId = DefaultValues.WellStationSystemTemplateId;
            }
            return defaultWellStationTemplateId;
        }
        public async Task<BizResponse> UpdateDefaultWellStationTemplateId(string defaultWellStationTemplateId)
        {
            var response = new BizResponse();
            var sysExternal = await _SystemRepository.GetSysExternal();
            if (sysExternal == null)
            {
                sysExternal = new SysExternal();
            }
            sysExternal.DefaultWellStationTemplateId = defaultWellStationTemplateId;
            await _SystemRepository.UpdateSysExternal(sysExternal);
            response.Success();
            return response;
        }
        #endregion
    }
}
