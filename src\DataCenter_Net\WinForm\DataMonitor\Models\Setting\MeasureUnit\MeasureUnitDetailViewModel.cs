﻿using SDHD.DC.DataMonitor.Utils;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Media;

namespace SDHD.DC.DataMonitor.Models.Setting.MeasureUnit
{
    internal class MeasureUnitDetailViewModel : NotifyPropertyChangedModel
    {
        public string WellStationId { set; get; }
        public string WindowTitle { get; set; }
        public string MeasureUnitId { get; set; }

        private string _Name;
        public string Name
        {
            get { return _Name; }
            set
            {
                _Name = value;
                OnPropertyChanged(nameof(Name));
            }
        }
        public string CategoryId { set; get; }

        private string _BaseUnit;
        public string BaseUnit
        {
            get { return _BaseUnit; }
            set
            {
                if (_BaseUnit == value) return;
                _BaseUnit = value;
                OnPropertyChanged(nameof(BaseUnit));
                OnPropertyChanged(nameof(BaseUnits));
            }
        }

        private string _ExchangeRelationship;
        public string ExchangeRelationship
        {
            get { return _ExchangeRelationship; }
            set
            {
                if (_ExchangeRelationship == value) return;
                _ExchangeRelationship = value;
                OnPropertyChanged(nameof(ExchangeRelationship));
            }
        }

        private bool _IsNotBaseUnitExchangeRelationship = false;
        public bool IsNotBaseUnitExchangeRelationship
        {
            get { return _IsNotBaseUnitExchangeRelationship; }
            set
            {
                if (_IsNotBaseUnitExchangeRelationship == value) return;
                _IsNotBaseUnitExchangeRelationship = value;
                OnPropertyChanged(nameof(IsNotBaseUnitExchangeRelationship));
            }
        }

        private string _ExchangeMethod;
        public string ExchangeMethod
        {
            get { return _ExchangeMethod; }
            set
            {
                if (_ExchangeMethod == value) return;
                _ExchangeMethod = value;
                OnPropertyChanged(nameof(ExchangeMethod));
            }
        }


        private bool _IsCustomizeFormulaVisible = false;
        public bool IsCustomizeFormulaVisible
        {
            get { return _IsCustomizeFormulaVisible; }
            set
            {
                if (_IsCustomizeFormulaVisible == value) return;
                _IsCustomizeFormulaVisible = value;
                OnPropertyChanged(nameof(IsCustomizeFormulaVisible));
            }
        }

        private string _CustomizeFormula;
        /// <summary>
        /// 计算公式
        /// </summary>
        public string CustomizeFormula
        {
            get { return _CustomizeFormula; }
            set
            {
                if (_CustomizeFormula == value) return;
                _CustomizeFormula = value;
                OnPropertyChanged(nameof(CustomizeFormula));
            }
        }

        public string DefaultMinRange { set; get; }
        public string DefaultMaxRange { set; get; }
        public string DefaultDisplayMinRange { set; get; }
        public string DefaultDisplayMaxRange { set; get; }

        private string _Coefficient;
        public string Coefficient
        {
            get { return _Coefficient; }
            set
            {
                if (_Coefficient == value) return;
                _Coefficient = value;
                OnPropertyChanged(nameof(Coefficient));
            }
        }
        private bool _IsCoefficientVisible = false;
        public bool IsCoefficientVisible
        {
            get { return _IsCoefficientVisible; }
            set
            {
                if (_IsCoefficientVisible == value) return;
                _IsCoefficientVisible = value;
                OnPropertyChanged(nameof(IsCoefficientVisible));
            }
        }
        private string _DefaultColor;
        public string DefaultColor
        {
            get { return _DefaultColor; }
            set
            {
                if (value == _DefaultColor) return;
                _DefaultColor = value;
                OnPropertyChanged(nameof(DefaultColor));
                OnPropertyChanged(nameof(DefaultColorBrush));
            }
        }
        public Brush DefaultColorBrush
        {
            get
            {
                return ColorExtensionsWPF.ConvertToBrush(DefaultColor);
            }
        }
        public string DefaultIcon { set; get; }


        public List<SelectItem> Categories { set; get; } = new List<SelectItem>();
        public List<SelectItem> ExchangeRelationships { set; get; } = new List<SelectItem>();
        public List<SelectItem> ExchangeMethods { set; get; } = new List<SelectItem>();
        public List<SelectItem> Icons { set; get; } = new List<SelectItem>();
        public ObservableCollection<SelectItem> BaseUnits { set; get; } = new ObservableCollection<SelectItem>();
    }
}
