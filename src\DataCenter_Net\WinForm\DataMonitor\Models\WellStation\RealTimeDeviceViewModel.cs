﻿using SDHD.DC.DataCollection.ApplicationCore.Domain.Entities;
using SDHD.DC.DataMonitor.Utils;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Media;

namespace SDHD.DC.DataMonitor.Models.WellStation
{
    public class RealTimeDeviceViewModel : NotifyPropertyChangedModel
    {
        private bool _IsEnableRefresh = false;
        public bool IsEnableRefresh
        {
            get { return _IsEnableRefresh; }
            set
            {
                _IsEnableRefresh = value;
                OnPropertyChanged(nameof(IsEnableRefresh));
            }
        }
        private List<DeviceInfo> _Devices = new List<DeviceInfo>();
        public List<DeviceInfo> Devices
        {
            get { return _Devices; }
            set
            {
                _Devices = value;
                OnPropertyChanged(nameof(Devices));
            }
        }
        public class DeviceInfo : NotifyPropertyChangedModel
        {
            public string _ModuleSN;
            public string ModuleSN
            {
                get { return _ModuleSN; }
                set
                {
                    if (_ModuleSN == value) return;
                    _ModuleSN = value;
                    OnPropertyChanged(nameof(ModuleSN));
                }
            }

            private string _DeviceTypeDesc;
            public string DeviceTypeDesc
            {
                get { return _DeviceTypeDesc; }
                set
                {
                    if (_DeviceTypeDesc == value) return;
                    _DeviceTypeDesc = value;
                    OnPropertyChanged(nameof(DeviceTypeDesc));
                }
            }


            private int _DevValuesCount;
            public int DevValuesCount
            {
                get { return _DevValuesCount; }
                set
                {
                    if (_DevValuesCount == value) return;
                    _DevValuesCount = value;
                    OnPropertyChanged(nameof(DevValuesCount));
                }
            }

            private string _DevValue;
            public string DevValue
            {
                get { return _DevValue; }
                set
                {
                    if (_DevValue == value) return;
                    _DevValue = value;
                    OnPropertyChanged(nameof(DevValue));
                }
            }

            private string _Timeout;
            public string Timeout
            {
                get { return _Timeout; }
                set
                {
                    if (_Timeout == value) return;
                    _Timeout = value;
                    OnPropertyChanged(nameof(Timeout));
                }
            }

            private string _TimeSpan;
            public string TimeSpan
            {
                get { return _TimeSpan; }
                set
                {
                    if (_TimeSpan == value) return;
                    _TimeSpan = value;
                    OnPropertyChanged(nameof(TimeSpan));
                }
            }

            private string _IPAddress;
            public string IPAddress
            {
                get { return _IPAddress; }
                set
                {
                    if (_IPAddress == value) return;
                    _IPAddress = value;
                    OnPropertyChanged(nameof(IPAddress));
                }
            }

            private string _Remarks;
            public string Remarks
            {
                get { return _Remarks; }
                set
                {
                    if (_Remarks == value) return;
                    _Remarks = value;
                    OnPropertyChanged(nameof(Remarks));
                }
            }

            private string _IndexNo;
            public string IndexNo
            {
                get { return _IndexNo; }
                set
                {
                    if (_IndexNo == value) return;
                    _IndexNo = value;
                    OnPropertyChanged(nameof(IndexNo));
                }
            }
        }

        public List<DeviceItemInfo> DeviceItemInfos = new();
    }
}
