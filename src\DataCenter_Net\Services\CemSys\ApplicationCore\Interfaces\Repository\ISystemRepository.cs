﻿using SDHD.DC.CemSys.ApplicationCore.Domain.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SDHD.DC.CemSys.ApplicationCore.Interfaces.Repository
{
    public interface ISystemRepository
    {
        Task<SysConfig> GetSysConfig();
        Task<bool> UpdateSysConfig(RegisterData sysConfig);
        Task<RegisterData> GetRegisterData();
        Task<bool> SaveRegisterData(RegisterData entity);
        Task<bool> TestServerConnection(string serverIP, int port, int connectTimeout, out string errorMsg);
        Task CloseServerConnection();
        Task<SysExternal> GetSysExternal();
        Task<bool> UpdateSysExternal(SysExternal entity);
    }
}
