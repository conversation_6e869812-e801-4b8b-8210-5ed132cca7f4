﻿using SDHD.DC.DataCollection.ApplicationCore.Domain.Entities;
using SDHD.DC.DataCollection.ApplicationCore.Interfaces.Repository;
using SDHD.DC.DataCollection.Infrastructure.Json.Mapping;
using SDHD.DC.Utilities;
using SDHD.DC.Utilities.Constants;
using SDHD.DC.Utilities.DataStore;
using SDHD.DC.Utilities.Net;
using SDHD.DC.Utilities.ServiceExtensions;
using System;
using System.Threading.Tasks;

namespace SDHD.DC.DataCollection.Infrastructure.Json.Repository
{
    public class SystemRepository : BaseRepository, ISystemRepository
    {
        private IDataStore _DataStore;
        public SystemRepository()
        {
            _DataStore = ServiceHelper.GetService<IDataStore>();
        }
        public Task<bool> TestServerConnection(string serverIP, int port, int connectionTimeout, out string errorMsg)
        {
            errorMsg = null;
            if (TCPClientHelper.TryConnectTest(serverIP, port, connectionTimeout, out string error))
            {
                return Task.FromResult(true);
            }
            errorMsg = error;
            return Task.FromResult(false);
        }
        public Task CloseServerConnection()
        {
            if (_DataStore != null)
            {
                _DataStore.Close();
            }
            return Task.CompletedTask;
        }

        public async Task<SysConfig> GetSysConfig()
        {
            SysConfig result = new SysConfig();
            var dataEntity = await GetDataFromBytesArray<Entities.SysConfig>(StoreFiles.RegisterData, null, false);
            if (dataEntity != null)
            {
                result = dataEntity.ToDomainEntity(result);
            }
            return result;
        }
        public async Task<bool> UpdateSysConfig(SysConfig sysConfig)
        {
            var dataEntity = await GetDataFromBytesArray<Entities.SysConfig>(StoreFiles.RegisterData, null, false);
            if (dataEntity == null)
            {
                dataEntity = new Entities.SysConfig();
            }
            dataEntity = sysConfig.ToDataEntity(dataEntity);
            await SaveDataAsBytesArray(StoreFiles.RegisterData, dataEntity, null, false);
            return true;
        }
        public async Task<RegisterData> GetRegisterData()
        {
            RegisterData result = null;
            var dataEntity = await GetDataFromBytesArray<Entities.SysConfig>(StoreFiles.RegisterData, null, false);
            if (dataEntity != null)
            {
                result = new RegisterData();
                result.UserName = dataEntity.RegName;
                result.AuthCode = dataEntity.AuthCode;
            }
            return result;
        }

        public async Task<bool> SaveRegisterData(RegisterData entity)
        {
            var dataEntity = await GetDataFromBytesArray<Entities.SysConfig>(StoreFiles.RegisterData, null, false);
            if (dataEntity == null)
            {
                dataEntity = new Entities.SysConfig();
            }
            dataEntity.RegName = entity.UserName;
            dataEntity.AuthCode = entity.AuthCode;
            await SaveDataAsBytesArray(StoreFiles.RegisterData, dataEntity, null, false);
            return true;
        }
    }
}
