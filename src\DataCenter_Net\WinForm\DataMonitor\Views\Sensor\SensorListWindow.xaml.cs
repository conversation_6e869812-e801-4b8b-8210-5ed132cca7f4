﻿using Commiunication;
using Google.Protobuf;
using ProjectStruct;
using SDHD.DC.DataCollection.ApplicationCore.Domain.Entities;
using SDHD.DC.DataMonitor.Models.Sensor;
using SDHD.DC.DataMonitor.Resources.Localization;
using SDHD.DC.DataMonitor.Utils;
using SDHD.DC.DataMonitor.Views.WellStation;
using SDHD.DC.Utilities.Constants;
using SDHD.DC.Utilities.Extensions;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Threading;
using System.Windows;
using System.Windows.Controls;

namespace SDHD.DC.DataMonitor.Views.Sensor
{
    public partial class SensorListWindow : Window
    {
        SensorListViewModel _Model;
        public Action SensorAddOrUpdated;
        private SysBase.Types.UnitInfoList _UnitInfos;
        private SysBase.Types.UnitTypeList _UnitTypes;
        private SysBase.Types.ProBase.Types.SensorInfoList _SensorInfoList;
        public SensorListWindow(string wellStationId)
        {
            InitializeComponent();
            SetWindowSizeToScreenPercentage(0.8);
            _Model = new() { WellStationId = wellStationId };
            Commiunication.Global.OnSetMessage += OnSetMessage;
            WindowStartupLocation = WindowStartupLocation.CenterScreen;
            Init();
            this.DataContext = _Model;
            this.Closing += (s, e) => { Commiunication.Global.OnSetMessage -= OnSetMessage; };
        }


        private void Init()
        {
            try
            {
                RefreshGlobalMessages();
                InitSensorList();
            }
            catch (Exception ex)
            {
                this.ShowError(ex);
            }
        }
        private void InitSensorList()
        {
            int unitId;
            int categoryId;
            int sensorId = 0;
            _Model.Sensors.Clear();
            SensorListViewModel.SensorInfo sensor;
            foreach (var sensorItem in _SensorInfoList.Items)
            {
                unitId = (int)sensorItem.UnitId;
                categoryId = (int)sensorItem.TypeId;
                sensor = new()
                {
                    SensorId = $"{sensorId}",
                    CalFormula = sensorItem.Exp,
                    SensorName = sensorItem.Name,
                    IsEnabled = sensorItem.Enabled,
                    IsEnabledDesc = sensorItem.Enabled ? LanguageManager.Instance.GetString("TextYes") : LanguageManager.Instance.GetString("TextNo"),
                    LineColor = ColorExtensions.ConvertToHexFromArgb((int)sensorItem.LineColor),
                    MeasureUnit = $"{(_UnitInfos.Items.Count > unitId ? _UnitInfos.Items[unitId].Name : "")}",
                    SensorCategory = $"{(_UnitTypes.Items.Count > categoryId ? _UnitTypes.Items[categoryId] : "")}",
                };
                if (IsSimulateSensor((int)sensorItem.DataType))
                {
                    sensor.MinRange = $"{sensorItem.RanMin}";
                    sensor.MaxRange = $"{sensorItem.RanMax}";
                }
                if (IsSimulateSensor((int)sensorItem.DataType) || IsDataSensor((int)sensorItem.DataType))
                {
                    sensor.ModuleSN = string.Format("{0}:{1}:{2}", sensorId, sensorItem.Sn, sensorItem.ValueId);
                }
                else
                {
                    sensor.ModuleSN = string.Format("{0}:", sensorId);
                }
                _Model.Sensors.Add(sensor);
                sensorId++;
            }
        }
        private void OnSetMessage(string msg)
        {
            Init();
        }
        private void SetMessage(IMessage message, int idx = -1)
        {
            Commiunication.ThreadedWorker.SetMessage(message, idx);
        }
        private static bool IsDataSensor(int moduleType)
        {
            return moduleType == CodeNames.SensorModuleType.Data;
        }
        private static bool IsSimulateSensor(int moduleType)
        {
            return moduleType == CodeNames.SensorModuleType.Simulate;
        }


        private void RefreshGlobalMessages()
        {
            _UnitTypes = Global.Messages.GetValueOrDefault("UnitTypeList") as SysBase.Types.UnitTypeList;
            _UnitInfos = Global.Messages.GetValueOrDefault("UnitInfoList") as SysBase.Types.UnitInfoList;
            _SensorInfoList = Global.Messages.GetValueOrDefault("SensorInfoList") as SysBase.Types.ProBase.Types.SensorInfoList;
            Debug.WriteLine($"sensorList Count:{_SensorInfoList.Items.Count}");
        }
        private void SetWindowSizeToScreenPercentage(double percentage)
        {
            // 获取屏幕的宽度和高度
            double screenWidth = SystemParameters.PrimaryScreenWidth;
            double screenHeight = SystemParameters.PrimaryScreenHeight;

            // 计算窗口的宽度和高度
            Width = screenWidth * percentage;
            Height = screenHeight * percentage;

            // 确保窗口大小不超过屏幕大小
            if (Width > screenWidth)
                Width = screenWidth;
            if (Height > screenHeight)
                Height = screenHeight;
        }
        protected void dataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                DataGrid dataGrid = sender as DataGrid;
                if (dataGrid.SelectedItem != null)
                {
                    this._Model.IsEditEnabled = true;
                }
            }
            catch (Exception ex)
            {
                this.ShowError(ex);
            }
        }
        protected void dataGridRow_MouseDoubleClick(object sender, EventArgs e)
        {
            try
            {
                if (dataGrid.SelectedItem == null || dataGrid.SelectedItems.Count == 0)
                {
                    return;
                }
                var sensor = dataGrid.SelectedItem as SensorListViewModel.SensorInfo;
                var win = new SensorDetailConfigWindow(_Model.WellStationId, sensor.SensorId);
                win.SensorAddOrUpdated += OnSensorAddOrUpdated;
                this.ShowWindowAsDialog(win);
            }
            catch (Exception ex)
            {
                this.ShowError(ex);
            }
        }
        protected void btnAddSensor_Click(object sender, EventArgs e)
        {
            try
            {
                var win = new SensorDetailConfigWindow(_Model.WellStationId, null);
                win.SensorAddOrUpdated += OnSensorAddOrUpdated;
                this.ShowWindowAsDialog(win);
            }
            catch (Exception ex)
            {
                this.ShowError(ex);
            }
        }
        protected void btnEditSensor_Click(object sender, EventArgs e)
        {
            try
            {
                if (dataGrid.SelectedItem == null)
                {
                    this.ShowMessage(LanguageManager.Instance.GetString("MessagePleaseSelectSensorToEdit"));
                    return;
                }
                var sensor = dataGrid.SelectedItem as SensorListViewModel.SensorInfo;
                var win = new SensorDetailConfigWindow(_Model.WellStationId, sensor.SensorId);
                win.SensorAddOrUpdated += OnSensorAddOrUpdated;
                this.ShowWindowAsDialog(win);
            }
            catch (Exception ex)
            {
                this.ShowError(ex);
            }
        }
        protected async void btnDeleteSensor_Click(object sender, EventArgs e)
        {
            try
            {
                if (dataGrid.SelectedItem == null || dataGrid.SelectedItem is not SensorListViewModel.SensorInfo record || !int.TryParse(record.SensorId, out int sensorId))
                {
                    this.ShowMessage(LanguageManager.Instance.GetString("MessageNoItemSelectedToDelete"));
                    return;
                }
                var dialResult = this.ShowMessage(string.Format(LanguageManager.Instance.GetString("MessageConfirmDeleteSensor"), record.SensorName), LanguageManager.Instance.GetString("TitlePrompt"), MessageBoxButton.OKCancel);
                if (dialResult == MessageBoxResult.OK && _SensorInfoList.Items.Count > sensorId)
                {
                    _SensorInfoList.Items.RemoveAt(sensorId);
                    SetMessage(_SensorInfoList);
                    await UtilityHandler.LogOperationForWellStation(string.Format(LanguageManager.Instance.GetString("LogDeleteSensor"), record.SensorId, record.SensorName), _Model.WellStationId);
                    this._Model.IsEditEnabled = false;
                }
            }
            catch (Exception ex)
            {
                this.ShowError(ex);
            }
        }

        public void OnSensorAddOrUpdated(string sensorId)
        {
        }
        protected void btnViewDevList_Click(object sender, EventArgs e)
        {
            try
            {
                var win = new RealTimeDeviceListWindow(_Model.WellStationId);
                win.DeviceSelectedEvent += OnDeviceSelectedEvent;
                this.ShowWindowAsDialog(win);
            }
            catch (Exception ex)
            {
                this.ShowError(ex);
            }
        }
        private void OnDeviceSelectedEvent(string moduleSN, string deviceTypeDesc, string indexNo, DeviceItemInfo deviceItemInfo)
        {
            try
            {
                var win = new SensorDetailConfigWindow(_Model.WellStationId, null, moduleSN, indexNo, deviceItemInfo);
                win.SensorAddOrUpdated += OnSensorAddOrUpdated;
                this.ShowWindowAsDialog(win);
            }
            catch (Exception ex)
            {
                this.ShowError(ex);
            }
        }

        protected void btnViewDevConnectionList_Click(object sender, EventArgs e)
        {
            try
            {
                var win = new RealTimeDeviceConnectionListWindow(_Model.WellStationId);
                this.ShowWindowAsDialog(win);
            }
            catch (Exception ex)
            {
                this.ShowError(ex);
            }
        }

    }
}
