# OnMouseMove 函数单位获取修复方案

## 🎯 **问题描述**

您发现在 `OnMouseMove` 函数中有这样的代码：

```csharp
SelectedSenStr += $" {YAxisproperts[SelectedYAxis].Name}"
```

这行代码是从坐标轴属性（YAxisproperts）获取单位，而不是从 SeriesProperty 获取，这是不对的。

## 🔍 **问题分析**

### **当前的错误方式**
```csharp
// ❌ 错误：从坐标轴获取单位
SelectedSenStr += $" {YAxisproperts[SelectedYAxis].Name}"
```

### **问题所在**
1. **YAxisproperts[SelectedYAxis].Name** 是坐标轴的单位
2. **不是传感器的实际单位**
3. **当传感器单位修改时，坐标轴单位可能没有更新**
4. **应该从 SeriesProperty.Unit 获取**

## ✅ **解决方案**

### **方案1：从 SeriesProperty 获取单位（推荐）**

```csharp
// ✅ 正确：从传感器属性获取单位
private void OnMouseMove(MouseEventArgs e)
{
    try
    {
        // 获取当前鼠标位置对应的传感器信息
        var currentSensor = GetCurrentFocusedSensor(e.X, e.Y);
        if (currentSensor != null)
        {
            // 从传感器的 SeriesProperty 获取单位，而不是从坐标轴获取
            string sensorUnit = GetSensorUnit(currentSensor.SensorId);
            
            // 构建显示字符串
            SelectedSenStr = $"{currentSensor.SensorName}: {currentSensor.Value:F2}";
            
            if (!string.IsNullOrEmpty(sensorUnit))
            {
                SelectedSenStr += $" {sensorUnit}";
            }
        }
    }
    catch (Exception ex)
    {
        Logger.Write($"Error in OnMouseMove: {ex.Message}");
    }
}

/// <summary>
/// 获取传感器的实际单位
/// 优先从 SeriesProperty.Unit 获取，备用从 Global.Messages 获取
/// </summary>
/// <param name="sensorId">传感器ID</param>
/// <returns>传感器单位</returns>
private string GetSensorUnit(int sensorId)
{
    try
    {
        // 方法1：从 SeriesProperty 获取（如果有的话）
        var sensorInfo = GetSensorInfoBySensorId(sensorId);
        if (sensorInfo != null && !string.IsNullOrEmpty(sensorInfo.Unit))
        {
            return sensorInfo.Unit;
        }
        
        // 方法2：实时从 Global.Messages 获取最新单位
        string latestUnit = DrawSeriePointDataUnitHelper.GetLatestSensorUnit(sensorId);
        if (!string.IsNullOrEmpty(latestUnit))
        {
            return latestUnit;
        }
        
        // 方法3：备用方案，从坐标轴获取（保持兼容性）
        if (SelectedYAxis >= 0 && SelectedYAxis < YAxisproperts.Count)
        {
            return YAxisproperts[SelectedYAxis].Name;
        }
    }
    catch (Exception ex)
    {
        Logger.Write($"Error getting sensor unit for sensor {sensorId}: {ex.Message}");
    }
    
    return "";
}
```

### **方案2：直接使用实时获取方法（最简单）**

```csharp
// ✅ 最简单的修复方式
private void OnMouseMove(MouseEventArgs e)
{
    try
    {
        // 获取当前鼠标位置对应的传感器信息
        var currentSensor = GetCurrentFocusedSensor(e.X, e.Y);
        if (currentSensor != null)
        {
            // 🎯 直接使用实时获取方法
            string sensorUnit = DrawSeriePointDataUnitHelper.GetLatestSensorUnit(currentSensor.SensorId);
            
            // 如果获取失败，使用备用方案
            if (string.IsNullOrEmpty(sensorUnit) && SelectedYAxis >= 0 && SelectedYAxis < YAxisproperts.Count)
            {
                sensorUnit = YAxisproperts[SelectedYAxis].Name;
            }
            
            // 构建显示字符串
            SelectedSenStr = $"{currentSensor.SensorName}: {currentSensor.Value:F2}";
            
            if (!string.IsNullOrEmpty(sensorUnit))
            {
                SelectedSenStr += $" {sensorUnit}";
            }
        }
    }
    catch (Exception ex)
    {
        Logger.Write($"Error in OnMouseMove: {ex.Message}");
    }
}
```

### **方案3：完整的重构版本**

```csharp
// ✅ 完整的重构版本，包含错误处理和日志
private void OnMouseMove(MouseEventArgs e)
{
    try
    {
        // 获取当前鼠标位置对应的传感器信息
        var currentSensor = GetCurrentFocusedSensor(e.X, e.Y);
        if (currentSensor == null)
        {
            SelectedSenStr = "";
            return;
        }

        // 获取传感器名称（优先使用最新的）
        string sensorName = DrawSeriePointDataUnitHelper.GetLatestSensorName(currentSensor.SensorId);
        if (string.IsNullOrEmpty(sensorName))
        {
            sensorName = currentSensor.SensorName ?? $"Sensor {currentSensor.SensorId}";
        }

        // 获取传感器单位（优先使用最新的）
        string sensorUnit = DrawSeriePointDataUnitHelper.GetLatestSensorUnit(currentSensor.SensorId);
        
        // 如果获取失败，尝试从 SeriesProperty 获取
        if (string.IsNullOrEmpty(sensorUnit))
        {
            var sensorInfo = GetSensorInfoBySensorId(currentSensor.SensorId);
            if (sensorInfo != null)
            {
                sensorUnit = sensorInfo.Unit;
            }
        }
        
        // 最后的备用方案：从坐标轴获取
        if (string.IsNullOrEmpty(sensorUnit) && SelectedYAxis >= 0 && SelectedYAxis < YAxisproperts.Count)
        {
            sensorUnit = YAxisproperts[SelectedYAxis].Name;
        }

        // 构建显示字符串
        SelectedSenStr = $"{sensorName}: {currentSensor.Value:F2}";
        
        if (!string.IsNullOrEmpty(sensorUnit))
        {
            SelectedSenStr += $" {sensorUnit}";
        }

        // 调试日志
        Logger.Write($"OnMouseMove: Sensor {currentSensor.SensorId}, Name: '{sensorName}', Unit: '{sensorUnit}', Value: {currentSensor.Value:F2}");
    }
    catch (Exception ex)
    {
        Logger.Write($"Error in OnMouseMove: {ex.Message}");
        SelectedSenStr = "Error displaying sensor info";
    }
}
```

## 🔧 **具体修改步骤**

### **第一步：找到 OnMouseMove 方法**

在您的代码中找到包含以下代码的 OnMouseMove 方法：
```csharp
SelectedSenStr += $" {YAxisproperts[SelectedYAxis].Name}"
```

### **第二步：替换单位获取逻辑**

将上面的代码替换为：
```csharp
// ❌ 原来的代码
SelectedSenStr += $" {YAxisproperts[SelectedYAxis].Name}"

// ✅ 新的代码
string sensorUnit = DrawSeriePointDataUnitHelper.GetLatestSensorUnit(currentSensorId);
if (string.IsNullOrEmpty(sensorUnit) && SelectedYAxis >= 0 && SelectedYAxis < YAxisproperts.Count)
{
    sensorUnit = YAxisproperts[SelectedYAxis].Name; // 备用方案
}
SelectedSenStr += $" {sensorUnit}";
```

### **第三步：确保能获取到 currentSensorId**

确保在 OnMouseMove 方法中能够获取到当前传感器的 ID：
```csharp
// 如果没有 currentSensorId，需要从当前聚焦的传感器获取
int currentSensorId = GetCurrentFocusedSensorId(e.X, e.Y);
```

## 🎯 **预期效果**

修改后，您将获得：

1. **✅ 正确的单位显示**：鼠标悬停显示传感器实际单位，不是坐标轴单位
2. **✅ 实时单位更新**：当传感器单位修改时，鼠标悬停显示会立即更新
3. **✅ 向后兼容**：如果获取传感器单位失败，会自动使用坐标轴单位作为备用
4. **✅ 完整的错误处理**：不会因为获取失败而影响显示

## 🔍 **测试验证**

### **测试步骤**
1. **修改传感器单位**：在传感器设置中修改单位并保存
2. **鼠标悬停测试**：将鼠标移动到曲线上，观察显示的单位
3. **确认单位正确**：确认显示的是新单位，不是旧单位
4. **检查日志**：查看调试日志，确认获取到了正确的单位

### **预期结果**
- ✅ 鼠标悬停显示新单位
- ✅ 不需要重新打开窗口
- ✅ 实时更新，无延迟
- ✅ 有完整的错误处理和备用方案

## 📝 **关键要点**

1. **优先级顺序**：
   - 第一优先：`DrawSeriePointDataUnitHelper.GetLatestSensorUnit()` （实时获取）
   - 第二优先：`SeriesProperty.Unit` （缓存的传感器单位）
   - 第三优先：`YAxisproperts[SelectedYAxis].Name` （坐标轴单位，备用）

2. **错误处理**：
   - 每个获取步骤都有错误处理
   - 有多层备用方案
   - 不会因为获取失败而影响显示

3. **性能考虑**：
   - 实时获取是轻量级操作
   - 有缓存机制避免重复获取
   - 只在鼠标移动时触发

**这样修改后，OnMouseMove 就能正确显示传感器的实际单位了！** 🎉
