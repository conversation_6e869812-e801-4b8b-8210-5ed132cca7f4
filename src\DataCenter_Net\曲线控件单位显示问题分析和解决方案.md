# 曲线控件单位显示问题分析和解决方案

## 🎯 **问题分析**

### **问题现象**
- ✅ 传感器名称能正确更新
- ❌ 单位信息没能正确更新

### **根本原因**
通过代码分析发现，曲线控件中鼠标悬停显示的传感器信息来自 `CurveLineInfo` 对象：

```csharp
public class CurveLineInfo
{
    public string SensorFullName { set; get; }  // 传感器名称 ✅ 直接使用
    public string DataUnit { set; get; }        // 单位信息 ❌ 通过查询获取
}
```

### **数据流程对比**

#### **传感器名称（能正确更新）**
```
SensorInfo.Name → CurveLineInfo.SensorFullName → 鼠标悬停显示
```

#### **单位信息（不能正确更新）**
```
SensorInfo.Unitstr ❌ 被忽略
↓
MeasureUnitRepository.Get(MeasureUnitId) → CurveLineInfo.DataUnit → 鼠标悬停显示
```

### **问题位置**

1. **SensorDataLisenser.cs** (第90行)：
   ```csharp
   DataUnit = measureUnitItem.Name,  // ❌ 应该使用 sensorInfo.Unitstr
   ```

2. **MonitorViewService.cs** (第276行)：
   ```csharp
   DataUnit = _MeasureUnitRepository.Getay($"{measureUnitEntity.MeasureUnitId}", wellStationId)?.Name
   // ❌ 应该使用 sensorInfo.Unitstr
   ```

## 🔧 **解决方案**

### **方案1：修改数据源（推荐）**

修改创建 `CurveLineInfo` 的地方，直接使用 `sensorInfo.Unitstr`：

#### **修改 SensorDataLisenser.cs**
```csharp
// 原代码（第83-90行）
measureUnitItem = UtilityHandler.GetMeasureUnitay(sensor.MeasureUnitId, AppInfo.CurrentWellStationId);
listSensors.Add(new SensorInfo
{
    SensorId = sensor.SensorId,
    LineColor = sensor.LineColor,
    IsDisplay = !sensor.IsHidden,
    SensorName = sensor.SensorName,
    DataUnit = measureUnitItem.Name,  // ❌ 旧方式
    DataPrecision = sensor.DataPrecision,
    SensorFullName = sensor.SensorFullName,
    // ... 其他属性
});

// 修改后
listSensors.Add(new SensorInfo
{
    SensorId = sensor.SensorId,
    LineColor = sensor.LineColor,
    IsDisplay = !sensor.IsHidden,
    SensorName = sensor.SensorName,
    DataUnit = sensor.Unitstr ?? "",  // ✅ 直接使用 unitstr
    DataPrecision = sensor.DataPrecision,
    SensorFullName = sensor.SensorFullName,
    // ... 其他属性
});
```

#### **修改 MonitorViewService.cs**
```csharp
// 原代码（第276行）
DataUnit = _MeasureUnitRepository.Getay($"{measureUnitEntity.MeasureUnitId}", wellStationId)?.Name

// 修改后
DataUnit = sensorInfo.Unitstr ?? ""  // ✅ 直接使用 unitstr
```

### **方案2：更新现有的CurveLineInfo对象**

如果不想修改创建逻辑，可以在SensorInfoList更新时直接更新现有的CurveLineInfo对象：

```csharp
private void UpdateCurveLineInfoUnits()
{
    try
    {
        // 获取最新的SensorInfoList
        var sensorInfoList = Global.Messages.TryGetValue("SensorInfoList", out var sensorMsg) ? sensorMsg as SensorInfoList : null;
        if (sensorInfoList == null) return;

        // 获取曲线控件的引用
        var curveChart = this.FindName("CurveChart") as RealTimeCurveChart;
        if (curveChart == null) return;

        // 通过反射获取MonitorWindowChart对象
        var chartField = curveChart.GetType().GetField("_chart", BindingFlags.NonPublic | BindingFlags.Instance);
        if (chartField?.GetValue(curveChart) is MonitorWindowChart chart)
        {
            // 更新每条曲线的单位信息
            foreach (var curveLine in chart.CurveLines)
            {
                if (int.TryParse(curveLine.SensorId, out int sensorId) && 
                    sensorId >= 0 && sensorId < sensorInfoList.Items.Count)
                {
                    var sensorInfo = sensorInfoList.Items[sensorId];
                    curveLine.DataUnit = sensorInfo.Unitstr ?? "";
                    curveLine.SensorFullName = sensorInfo.Name;
                    
                    Logger.Write($"Updated curve line: Sensor {sensorId}, Unit: {sensorInfo.Unitstr}");
                }
            }
        }
    }
    catch (Exception ex)
    {
        Logger.Write($"Error in UpdateCurveLineInfoUnits: {ex.Message}");
    }
}
```

## 🎯 **推荐实施步骤**

### **第一步：验证问题**
1. 在鼠标悬停时查看显示的单位信息
2. 确认显示的是旧单位而不是新单位

### **第二步：实施方案2（快速验证）**
1. 将上面的 `UpdateCurveLineInfoUnits` 方法添加到 MonitorWindowView
2. 在 `UpdateCurveChartSensorNamesAndUnits` 中调用此方法
3. 测试验证单位是否能正确更新

### **第三步：如果方案2有效，考虑实施方案1（长期解决）**
1. 修改 SensorDataLisenser.cs 中的数据源
2. 修改 MonitorViewService.cs 中的数据源
3. 确保所有地方都使用 `sensorInfo.Unitstr`

## 🔍 **测试验证**

### **测试步骤**
1. 打开 MonitorWindowView 窗口
2. 将鼠标悬停在曲线上，记录显示的单位
3. 修改传感器的单位设置并保存
4. 再次将鼠标悬停在曲线上，确认单位是否更新

### **预期结果**
- 鼠标悬停显示的单位应该与传感器设置中的新单位一致
- 不需要重新打开窗口，单位应该自动更新

## 🎉 **总结**

问题的根本原因是：
- **传感器名称**：直接使用 `sensorInfo.Name` ✅
- **单位信息**：通过 `MeasureUnitRepository` 查询而不是使用 `sensorInfo.Unitstr` ❌

解决方案是让单位信息也直接使用 `sensorInfo.Unitstr`，这样当 SensorInfoList 更新时，单位信息就能正确更新了。
