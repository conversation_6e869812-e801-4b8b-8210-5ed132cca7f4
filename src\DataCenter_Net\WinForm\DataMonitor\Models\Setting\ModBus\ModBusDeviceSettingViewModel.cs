﻿using SDHD.DC.DataCollection.ApplicationCore.Domain.Entities;
using SDHD.DC.DataMonitor.Utils;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SDHD.DC.DataMonitor.Models.Setting.ModBus
{
    internal class ModBusDeviceSettingViewModel : NotifyPropertyChangedModel
    {
        private DeviceInfo _Device = new();
        public DeviceInfo Device
        {
            get { return _Device; }
            set
            {
                _Device = value;
                OnPropertyChanged(nameof(Device));
            }
        }
        private DeviceItemInfo _DeviceItem = new();
        public DeviceItemInfo DeviceItem
        {
            get { return _DeviceItem; }
            set
            {
                _DeviceItem = value;
                OnPropertyChanged(nameof(DeviceItem));
            }
        }
        private DeviceItemInfo _CopyDeviceItem;
        public DeviceItemInfo CopyDeviceItem
        {
            get { return _CopyDeviceItem; }
            set
            {
                _CopyDeviceItem = value;
                OnPropertyChanged(nameof(IsPasteEnabled));
            }
        }
        public bool IsPasteEnabled
        {
            get { return CopyDeviceItem != null; }
        }

        public bool IsSuperAdministrator
        {
            // 架构简化：移除UserHandler依赖，直接返回true（超级管理员权限）
            get { return true; }
        }
        public ObservableCollection<DeviceInfo> Devices { get; set; } = new();
        public class DeviceInfo : NotifyPropertyChangedModel
        {
           
            private bool _IsRTU;
            public bool IsRTU
            {
                get { return _IsRTU; }
                set
                {
                    if (_IsRTU == value) return;
                    _IsRTU = value;
                    OnPropertyChanged(nameof(IsRTU));
                }
            }

            private string _DeviceId;
            public string DeviceId
            {
                get { return _DeviceId; }
                set
                {
                    if (_DeviceId == value) return;
                    _DeviceId = value;
                    OnPropertyChanged(nameof(DeviceId));
                }
            }

            private string _DeviceSN;
            public string DeviceSN
            {
                get { return _DeviceSN; }
                set
                {
                    if (_DeviceSN == value) return;
                    _DeviceSN = value;
                    OnPropertyChanged(nameof(DeviceSN));
                }
            }

            private string _DeviceName;
            public string DeviceName
            {
                get { return _DeviceName; }
                set
                {
                    if (_DeviceName == value) return;
                    _DeviceName = value;
                    OnPropertyChanged(nameof(DeviceName));
                }
            }

            public ObservableCollection<DeviceItemInfo> Items { get; set; } = new();
        }

        public class DeviceItemInfo : NotifyPropertyChangedModel
        {
            public string IndexNo { set; get; }
            private string _Name;
            public string Name
            {
                get { return _Name; }
                set
                {
                    if (_Name == value) return;
                    _Name = value;
                    OnPropertyChanged(nameof(Name));
                }
            }
            private string _Unit;
            public string Unit
            {
                get { return _Unit; }
                set
                {
                    if (_Unit == value) return;
                    _Unit = value;
                    OnPropertyChanged(nameof(Unit));
                }
            }

            private string _DevID;
            public string DevID
            {
                get { return _DevID; }
                set
                {
                    if (_DevID == value) return;
                    _DevID = value;
                    OnPropertyChanged(nameof(DevID));
                }
            }

            private string _Address;
            public string Address
            {
                get { return _Address; }
                set
                {
                    if (_Address == value) return;
                    _Address = value;
                    OnPropertyChanged(nameof(Address));
                }
            }
            private string _AddressHex;
            public string AddressHex
            {
                get { return _AddressHex; }
                set
                {
                    if (_AddressHex == value) return;
                    _AddressHex = value;
                    OnPropertyChanged(nameof(AddressHex));
                }
            }
            private string _AddressInt;
            public string AddressInt
            {
                get { return _AddressInt; }
                set
                {
                    if (_AddressInt == value) return;
                    _AddressInt = value;
                    OnPropertyChanged(nameof(AddressInt));
                }
            }
            private bool _IsOffset;
            public bool IsOffset
            {
                get { return _IsOffset; }
                set
                {
                    if (_IsOffset == value) return;
                    _IsOffset = value;
                    OnPropertyChanged(nameof(IsOffset));
                }
            }
            private string _FunID;
            public string FunID
            {
                get { return _FunID; }
                set
                {
                    if (_FunID == value) return;
                    _FunID = value;
                    OnPropertyChanged(nameof(FunID));
                }
            }

            private string _DataType;
            public string DataType
            {
                get { return _DataType; }
                set
                {
                    if (_DataType == value) return;
                    _DataType = value;
                    OnPropertyChanged(nameof(DataType));
                }
            }
            public string DataTypeDesc { set; get; }
            private string _ShowType;
            public string ShowType
            {
                get { return _ShowType; }
                set
                {
                    if (_ShowType == value) return;
                    _ShowType = value;
                    OnPropertyChanged(nameof(ShowType));
                }
            }
            public string ShowTypeDesc { set; get; }
            private string _ShowLevel;
            public string ShowLevel
            {
                get { return _ShowLevel; }
                set
                {
                    if (_ShowLevel == value) return;
                    _ShowLevel = value;
                    OnPropertyChanged(nameof(ShowLevel));
                }
            }
            public string ShowLevelDesc { set; get; }
            private string _DataFormat;
            public string DataFormat
            {
                get { return _DataFormat; }
                set
                {
                    if (_DataFormat == value) return;
                    _DataFormat = value;
                    OnPropertyChanged(nameof(DataFormat));
                }
            }
            public string DataFormatDesc { set; get; }
            private string _DataPrecision;
            public string DataPrecision
            {
                get { return _DataPrecision; }
                set
                {
                    if (_DataPrecision == value) return;
                    _DataPrecision = value;
                    OnPropertyChanged(nameof(DataPrecision));
                }
            }
            private string _GroupNo;
            public string GroupNo
            {
                get { return _GroupNo; }
                set
                {
                    if (_GroupNo == value) return;
                    _GroupNo = value;
                    OnPropertyChanged(nameof(GroupNo));
                }
            }

            private string _GroupSubIndexNo;
            public string GroupSubIndexNo
            {
                get { return _GroupSubIndexNo; }
                set
                {
                    if (_GroupSubIndexNo == value) return;
                    _GroupSubIndexNo = value;
                    OnPropertyChanged(nameof(GroupSubIndexNo));
                }
            }


            private string _OperationRank;
            public string OperationRank
            {
                get { return _OperationRank; }
                set
                {
                    if (_OperationRank == value) return;
                    _OperationRank = value;
                    OnPropertyChanged(nameof(OperationRank));
                }
            }
            public string OperationRankDesc { set; get; }

            private string _InterlockingRelationship;

            public string InterlockingRelationship
            {
                get { return _InterlockingRelationship; }
                set
                {
                    if (_InterlockingRelationship == value) return;
                    _InterlockingRelationship = value;
                    OnPropertyChanged(nameof(InterlockingRelationship));
                }
            }
            private List<WarningInfo> _WarningInfosField = new();
            public List<WarningInfo> WarningInfosField
            {
                get { return _WarningInfosField; }
                set
                {
                    if (_WarningInfosField == value) return;
                    _WarningInfosField = value;
                    OnPropertyChanged(nameof(WarningInfosField));
                    OnPropertyChanged(nameof(WarningInfosFieldCount));
                }
            }

            public string WarningInfosFieldCount { get { return $"{(WarningInfosField.Count > 0 ? WarningInfosField.Count : "")}"; } }
        }
        public List<SelectItem> DataTypes { get; set; } = new()
        {
            new(){ Text="float",Value="0" },
            new(){ Text="long",Value="1" },
            new(){ Text="dword",Value="2" },
            new(){ Text="word",Value="3" },
            new(){ Text="short",Value="4" },
            new(){ Text="bool",Value="5" },
            new(){ Text="bit",Value="6" },
        };
        public List<SelectItem> ShowTypes { get; set; } = new()
        {
            new(){ Text="实时数据",Value="0" },
            new(){ Text="状态",Value="1" },
            new(){ Text="延时置零开关",Value="2" },
            new(){ Text="手动置零开关",Value="3" },
            new(){ Text="不置零开关",Value="4" },
            new(){ Text="参数设置",Value="5" },
            new(){ Text="置零开关",Value="6" },
            new(){ Text="不显示",Value="7" },
            new(){ Text="按下弹起切换开关",Value="8" },
            new(){ Text="切换开关",Value="9" },
        };
        public List<SelectItem> ShowLevels { get; set; } = new()
        {
            new(){ Text="弹出展示",Value="0" },
            new(){ Text="展示图形",Value="1" },
            new(){ Text="展示数据",Value="2" },
            new(){ Text="展示状态",Value="3" },
            new(){ Text="展示名称数据",Value="4" },
        };
        public List<SelectItem> DataFormats { get; set; } = new()
        {
            new(){ Text="ABCD",Value="0" },
            new(){ Text="CDAB",Value="1" },
            new(){ Text="BADC",Value="2" },
            new(){ Text="DCBA",Value="3" },
        };
        public List<SelectItem> DataPrecisions { get; set; } = new()
        {
            new(){ Text="0",Value="0" },
            new(){ Text="1",Value="1" },
            new(){ Text="2",Value="2" },
            new(){ Text="3",Value="3" },
            new(){ Text="4",Value="4" },
            new(){ Text="5",Value="5" },
            new(){ Text="6",Value="6" },
            new(){ Text="7",Value="7" },
            new(){ Text="8",Value="8" },
            new(){ Text="9",Value="9" },
            new(){ Text="10",Value="10" },
            new(){ Text="11",Value="11" },
            new(){ Text="12",Value="12" },
            new(){ Text="13",Value="13" },
            new(){ Text="14",Value="14" },
            new(){ Text="15",Value="15" },
        };

        public List<SelectItem> FunctionCodes { get; set; } = new()
        {
            new(){ Text="01",Value="1" },
            new(){ Text="02",Value="2" },
            new(){ Text="03",Value="3" },
            new(){ Text="04",Value="4" },
            new(){ Text="05",Value="5" },
            new(){ Text="06",Value="6" },
            new(){ Text="15",Value="15" },
            new(){ Text="16",Value="16" },
        };

        public List<SelectItem> OperationRanks { get; set; } = new()
        {
            new(){ Text="一级权限",Value="1" },
            new(){ Text="二级权限",Value="2" },
            new(){ Text="三级权限",Value="3" },
        };
    }
}
