﻿using Commiunication;
using Google.Protobuf;
using NPOI.SS.Formula.Functions;
using ProjectStruct;
using SDHD.DC.DataCollection.ApplicationCore.Domain.Entities;
using SDHD.DC.DataCollection.ApplicationCore.Domain.Request;
using SDHD.DC.DataCollection.ApplicationCore.Interfaces.Service;
using SDHD.DC.DataMonitor.Models;
using SDHD.DC.DataMonitor.Models.Common;
using SDHD.DC.DataMonitor.Models.Sensor;
using SDHD.DC.DataMonitor.Resources.Localization;
using SDHD.DC.DataMonitor.Utils;
using SDHD.DC.DataMonitor.Utils.Extensions;
using SDHD.DC.DataMonitor.Views.Common;
using SDHD.DC.DataMonitor.Views.MonitorView;
using SDHD.DC.DataMonitor.Views.Setting;
using SDHD.DC.Utilities;
using SDHD.DC.Utilities.Constants;
using SDHD.DC.Utilities.Extensions;
using SDHD.DC.Utilities.ServiceExtensions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using static ProjectStruct.SysBase.Types;
using static ProjectStruct.SysBase.Types.deviceBase.Types.TCPINFO.Types;
using static ProjectStruct.SysBase.Types.ProBase.Types;
using static ProjectStruct.SysBase.Types.ProBase.Types.SensorInfo.Types.SensorAdditionalProperties.Types;
using static ProjectStruct.U3DSENCE.Types.Equipment.Types;
using static SDHD.DC.Utilities.Constants.CodeNames;
using Separator = ProjectStruct.SysBase.Types.ProBase.Types.SensorInfo.Types.SensorAdditionalProperties.Types.Separator;


namespace SDHD.DC.DataMonitor.Views.Sensor
{
    public partial class SensorDetailConfigWindow : Window
    {

        const int defaultTimeUnit = 1000;
        SensorDetailSettingViewModel _Model;
        public Action<string> SensorAddOrUpdated;
        private SysBase.Types.UnitInfoList _UnitInfos;
        private SysBase.Types.UnitTypeList _UnitTypes;
        //IEnvironmentVariableService _EnvironmentVariableService;
        private SysBase.Types.ProBase.Types.SensorInfo _SensorInfo;
        private SysBase.Types.ProBase.Types.SensorInfoList _SensorInfoList;
        private SysBase.Types.RunTimeBase.Types.FunctionInfoList _FunctionInfoList;
        private SysBase.Types.ProBase.Types.SensorRealDataList _SensorRealDataList;
        public SensorDetailConfigWindow(string wellStationId, string sensorId, string moduleSN = null, string indexNo = null, DeviceItemInfo deviceItemInfo = null)
        {
            InitializeComponent();
            _Model = new()
            {
                SensorId = sensorId,
                ModuleSN = moduleSN,
                DevIndexID = indexNo,
                WellStationId = wellStationId,
                DeviceItemInfo = deviceItemInfo
            };
            Commiunication.Global.OnMessageReceived += OnReceivedMessage;
            //_EnvironmentVariableService = ServiceHelper.GetService<IEnvironmentVariableService>();
            Init();
            this.DataContext = _Model;
            this.Closing += (s, e) => { Commiunication.Global.OnMessageReceived -= OnReceivedMessage; };
        }
        private async void Init()
        {
            try
            {

                _Model.CurrentStepNo = 1;
                _Model.Icons = await UtilityHandler.GetSelectItems(CategoryNames.SensorIcon, false);
                var listUserGroups = await UtilityHandler.GetSelectItems(CategoryNames.SensorUserGroup, false);
                _Model.SensorNameGroups = await UtilityHandler.GetSelectItems(CategoryNames.SensorNameGroup, false);
                _Model.DecimalPrecisions = await UtilityHandler.GetSelectItems(CategoryNames.DecimalPrecision, false);
                _Model.SensorModuleTypes = await UtilityHandler.GetSelectItems(CategoryNames.SensorModuleType, false);
                _Model.AccumulationTimeUnits = await UtilityHandler.GetSelectItems(CategoryNames.AccumulationTimeUnit, false);
                _Model.CollectIntervalUnits = await UtilityHandler.GetSelectItems(CategoryNames.CollectIntervalUnit, false);
                if (listUserGroups.Any()) { listUserGroups[0].Text = string.Empty; }
                _Model.UserGroups = listUserGroups;
                FillDeviceItemInfo();
                RefreshGlobalMessages();
                FillCategories();
                BindSensorInfo();
                SetControlPrevNextButtonVisible();
            }
            catch (Exception ex)
            {
                this.ShowError(ex);
            }
        }
        private static void SetMessage(IMessage message, int idx = -1)
        {
            Commiunication.ThreadedWorker.SetMessage(message, idx);
        }
        private void OnReceivedMessage(IMessage msg)
        {
            if (msg is ProjectStruct.SysBase.Types.ProBase.Types.SensorRealDataList)
            {
                _SensorRealDataList = msg as ProjectStruct.SysBase.Types.ProBase.Types.SensorRealDataList;
            }
            else if (msg is comBase.Types.comInfo.Types.comRespond)
            {
                var res = msg as comBase.Types.comInfo.Types.comRespond;
                if (res.Cmdid == 16) _Model.CalFormulaVerifyResult = res.Succeed ? LanguageManager.Instance.GetString("TextCorrect") : res.Error;

            }
        }
        private void RefreshGlobalMessages()
        {
            _UnitTypes = Global.Messages.GetValueOrDefault("UnitTypeList") as SysBase.Types.UnitTypeList;
            _UnitInfos = Global.Messages.GetValueOrDefault("UnitInfoList") as SysBase.Types.UnitInfoList;
            _SensorInfoList = Global.Messages.GetValueOrDefault("SensorInfoList") as SysBase.Types.ProBase.Types.SensorInfoList;
            _FunctionInfoList = Global.Messages.GetValueOrDefault("FunctionInfoList") as SysBase.Types.RunTimeBase.Types.FunctionInfoList;
            if (int.TryParse(_Model.SensorId, out int sensorId) && _SensorInfoList != null && _SensorInfoList.Items != null && _SensorInfoList.Items.Count > sensorId)
            {
                _SensorInfo = _SensorInfoList.Items[sensorId];
            }
        }

        private List<SelectItem> FillFunctionInfos()
        {
            List<SelectItem> result = new() { new() { Value = string.Empty, Text = LanguageManager.Instance.GetString("TextPleaseSelect") } };
            if (_FunctionInfoList != null && _FunctionInfoList.Items != null)
            {
                foreach (var functionInfo in _FunctionInfoList.Items)
                {
                    result.Add(new()
                    {
                        Value = functionInfo.Name,
                        Text = $"{functionInfo.Name}; {functionInfo.Description}",
                    });
                }
            }
            return result;
        }



        private void SetControlPrevNextButtonVisible()
        {
            // 显示所有面板，不再分页
            panel1.Visibility = Visibility.Visible;
            panel2.Visibility = Visibility.Visible;
            panel3.Visibility = Visibility.Visible;

            // panel4包含校准功能，只有模拟量传感器才显示
            UpdateCalibrationVisibility();

            // panel5包含计算公式功能，对所有传感器类型都显示
            panel5.Visibility = Visibility.Visible;

            // 隐藏分页按钮，只保留完成和取消按钮
            btnPrevStep.Visibility = Visibility.Collapsed;
            btnNextStep.Visibility = Visibility.Collapsed;
        }

        private void UpdateCalibrationVisibility()
        {
            // 只有模拟量传感器才显示校准功能
            // 使用现有的IsSimulateSensor()方法来判断
            bool showCalibration = IsSimulateSensor();
            panel4.Visibility = showCalibration ? Visibility.Visible : Visibility.Collapsed;
        }
        private void FillCategories()
        {
            int unitTypeId = 0;
            _Model.SensorCategories.Clear();
            foreach (var unitType in _UnitTypes.Items)
            {
                _Model.SensorCategories.Add(new() { Value = $"{unitTypeId}", Text = unitType });
                unitTypeId++;
            }
        }
        private void FillMeasureUnits(string categoryId = null)
        {
            int indexId = 0;
            List<SelectItem> temp = new();
            foreach (var unitInfo in _UnitInfos.Items)
            {
                if (!unitInfo.Deleted && $"{unitInfo.UnitType}" == categoryId)
                {
                    // 使用全局索引 indexId 作为 Value，这是在 _UnitInfos.Items 中的实际位置
                    temp.Add(new() { Value = $"{indexId}", Text = unitInfo.Name });
                }
                indexId++;
            }
            _Model.MeasureUnits = temp;
        }

        private void SetModuleTypeControlVisible()
        {
            if (IsSimulateSensor() || IsDataSensor())
            {
                _Model.SensorTypeTitle = LanguageManager.Instance.GetString("LabelSetupSN");
                _Model.SensorNameTitle = _Model.ModuleSN?.Trim();
                _Model.IsModuleSNEnabled = true;
            }
            else
            {
                _Model.SensorTypeTitle = LanguageManager.Instance.GetString("LabelSetupVirtualSensor");
                _Model.SensorNameTitle = _Model.SensorName?.Trim();
                _Model.IsModuleSNEnabled = false;
            }
            if (IsSimulateSensor())
            {
                _Model.IsRangeEnabled = true;
            }
            else
            {
                _Model.IsRangeEnabled = false;
                _Model.ADCMinRange = null;
                _Model.ADCMaxRange = null;
                _Model.MinRange = null;
                _Model.MaxRange = null;
            }

            // 控制校准面板的可见性 - 只有模拟量传感器才显示校准功能
            UpdateCalibrationVisibility();
        }
        protected void btnPrevStep_Click(object sender, EventArgs e)
        {
            try
            {
                if (this._Model.CurrentStepNo == 4)
                {
                    if (IsSimulateSensor() || IsDataSensor())
                    {
                        this._Model.CurrentStepNo--;
                    }
                    else
                    {
                        this._Model.CurrentStepNo = this._Model.CurrentStepNo - 2;
                    }
                }
                else
                {
                    this._Model.CurrentStepNo--;
                }
                SetControlPrevNextButtonVisible();
            }
            catch (Exception ex)
            {
                this.ShowError(ex);
            }
        }
        protected void btnNextStep_Click(object sender, EventArgs e)
        {
            try
            {
                if (_Model.CurrentStepNo == 1)
                {
                    if (!ValidatePageStep1())
                    {
                        return;
                    }
                }
                if (_Model.CurrentStepNo == 2)
                {
                    if (!ValidatePageStep2())
                    {
                        return;
                    }
                }
                if (_Model.CurrentStepNo == 3)
                {
                    if (!ValidatePageStep3())
                    {
                        return;
                    }
                }
                if (_Model.CurrentStepNo == 4)
                {
                    if (!ValidatePageStep4())
                    {
                        return;
                    }
                }

                if (this._Model.CurrentStepNo == 2)
                {
                    if (IsSimulateSensor() || IsDataSensor())
                    {
                        this._Model.CurrentStepNo++;
                    }
                    else
                    {
                        this._Model.CurrentStepNo = this._Model.CurrentStepNo + 2;
                    }
                }
                else
                {
                    this._Model.CurrentStepNo++;
                }
                SetControlPrevNextButtonVisible();
            }
            catch (Exception ex)
            {
                this.ShowError(ex);
            }
        }
        protected void chkLightWarning_Checked(object sender, EventArgs e)
        {
            try
            {
                if (string.IsNullOrEmpty(_Model.LightWarningType))
                {
                    _Model.LightWarningType = _Model.LightWarningTypes.FirstOrDefault()?.Value;
                }
            }
            catch (Exception ex)
            {
                this.ShowError(ex);
            }
        }
        protected void lineColor_Click(object sender, EventArgs e)
        {
            try
            {
                var win = new ColorPickerWindow(_Model.LineColor);
                if (win.ShowDialog() == true)
                    OnColorSelected(win.SelectedColorHex, "");
            }
            catch (Exception ex)
            {
                this.ShowError(ex);
            }
        }
        public void OnColorSelected(string selectedColorHex, string transId)
        {
            _Model.LineColor = selectedColorHex;
        }
        private bool IsSimulateSensor()
        {
            if (_Model.SensorModuleType == CodeNames.SensorModuleType.Simulate.ToString())
            {
                return true;
            }
            return false;
        }
        private bool IsDataSensor()
        {
            if (_Model.SensorModuleType == CodeNames.SensorModuleType.Data.ToString())
            {
                return true;
            }
            return false;
        }
        private bool ValidatePageStep1()
        {
            if (string.IsNullOrWhiteSpace(_Model.SensorName))
            {
                this.ShowMessage(Errors.SensorNameRequired.Message);
                return false;
            }
            int index = 0;
            string curName = $"{(string.IsNullOrEmpty(_Model.SensorNameGroup) ? "" : _Model.SensorNameGroup + "#")}{_Model.SensorName}";
            if (_SensorInfoList != null && _SensorInfoList.Items?.Count > 0)
            {
                foreach (var sensorInfo in _SensorInfoList.Items)
                {
                    if (int.TryParse(_Model.SensorId, out int sensorId) && index == sensorId) continue;


                    if (curName == sensorInfo.Name)
                    {
                        this.ShowMessage(LanguageManager.Instance.GetString("MessageSensorNameCannotDuplicate"));
                        return false;
                    }
                    index++;
                }
            }


            if (IsSimulateSensor() || IsDataSensor())
            {
                if (string.IsNullOrWhiteSpace(_Model.ModuleSN))
                {
                    this.ShowMessage(Errors.ModuleSNRequired.Message);
                    return false;
                }
            }
            if (IsSimulateSensor())
            {
                if (string.IsNullOrWhiteSpace(_Model.MinRange))
                {
                    this.ShowMessage(Errors.MinRangeRequired.Message);
                    return false;
                }
                if (!decimal.TryParse(_Model.MinRange.Trim(), out decimal minRange))
                {
                    this.ShowMessage(Errors.MinRangeInvalid.Message);
                    return false;
                }
                if (string.IsNullOrWhiteSpace(_Model.MaxRange))
                {
                    this.ShowMessage(Errors.MaxRangeRequired.Message);
                    return false;
                }
                if (!decimal.TryParse(_Model.MaxRange.Trim(), out decimal maxRange))
                {
                    this.ShowMessage(Errors.MaxRangeInvalid.Message);
                    return false;
                }
                if (minRange > maxRange)
                {
                    this.ShowMessage(Errors.MinRangeGreateThanMaxRange.Message);
                    return false;
                }
                if (string.IsNullOrWhiteSpace(_Model.ADCMinRange))
                {
                    this.ShowMessage(Errors.ADCMinRangeRequired.Message);
                    return false;
                }
                if (!decimal.TryParse(_Model.ADCMinRange.Trim(), out decimal adcMinRange))
                {
                    this.ShowMessage(Errors.ADCMinRangeInvalid.Message);
                    return false;
                }
                if (string.IsNullOrWhiteSpace(_Model.ADCMaxRange))
                {
                    this.ShowMessage(Errors.ADCMaxRangeRequired.Message);
                    return false;
                }
                if (!decimal.TryParse(_Model.ADCMaxRange.Trim(), out decimal adcMaxRange))
                {
                    this.ShowMessage(Errors.ADCMaxRangeInvalid.Message);
                    return false;
                }
                if (adcMinRange > adcMaxRange)
                {
                    this.ShowMessage(Errors.ADCMinRangeGreateThanMaxRange.Message);
                    return false;
                }
            }
            if (_Model.IsEnabledValidDataValue)
            {
                if (string.IsNullOrWhiteSpace(_Model.ValidDataValue))
                {
                    this.ShowMessage(LanguageManager.Instance.GetString("MessageValidThresholdCannotBeEmpty"));
                    return false;
                }
                if (!decimal.TryParse(_Model.ValidDataValue.Trim(), out var startDataValue))
                {
                    this.ShowMessage(LanguageManager.Instance.GetString("MessageValidThresholdFormatIncorrect"));
                    return false;
                }
            }
            return true;
        }
        private bool ValidatePageStep2()
        {
            decimal maxExceedWarning = 0;
            if (_Model.IsEnableMaxWarning)
            {
                if (string.IsNullOrWhiteSpace(_Model.MaxExceedWarning))
                {
                    this.ShowMessage(Errors.SensorMaxRangeWarningRequired.Message);
                    return false;
                }
                if (!decimal.TryParse(_Model.MaxExceedWarning.Trim(), out maxExceedWarning))
                {
                    this.ShowMessage(Errors.SensorMaxRangeWarningInvalid.Message);
                    return false;
                }
                if (IsSimulateSensor())
                {
                    if (maxExceedWarning > decimal.Parse(_Model.MaxRange.Trim()))
                    {
                        this.ShowMessage(Errors.SensorMaxRangeWarningExceed.Message);
                        return false;
                    }
                }
            }
            if (_Model.IsEnableMinWarning)
            {
                decimal minExceedWarning = 0;
                if (string.IsNullOrWhiteSpace(_Model.MinExceedWarning))
                {
                    this.ShowMessage(Errors.SensorMinRangeWarningRequired.Message);
                    return false;
                }
                if (!decimal.TryParse(_Model.MinExceedWarning.Trim(), out minExceedWarning))
                {
                    this.ShowMessage(Errors.SensorMinRangeWarningInvalid.Message);
                    return false;
                }
                if (IsSimulateSensor())
                {
                    if (minExceedWarning < decimal.Parse(_Model.MinRange.Trim()))
                    {
                        this.ShowMessage(Errors.SensorMinRangeWarningExceed.Message);
                        return false;
                    }
                }
                if (_Model.IsEnableMaxWarning)
                {
                    if (minExceedWarning > maxExceedWarning)
                    {
                        this.ShowMessage(Errors.SensorMinRangeWarningGreaterThanMax.Message);
                        return false;
                    }
                }
            }
            if (_Model.IsModuleSNEnabled)
            {
                if (string.IsNullOrWhiteSpace(_Model.DevIndexID))
                {
                    this.ShowMessage(Errors.SensorDevIndexIDRequired.Message);
                    return false;
                }
                if (!int.TryParse(_Model.DevIndexID.Trim(), out int devIndexID))
                {
                    this.ShowMessage(Errors.SensorDevIndexIDInvalid.Message);
                    return false;
                }
                if (devIndexID < 0)
                {
                    this.ShowMessage(Errors.SensorDevIndexIDInvalid.Message);
                    return false;
                }
            }
            return true;
        }
        #region 标定值验证
        private bool ValidatePageStep3()
        {
            if (!(IsSimulateSensor() || IsDataSensor()))
            {
                return true;
            }
            //correct 1
            if (!string.IsNullOrWhiteSpace(_Model.CorrectItem1.ReadValue))
            {
                if (!decimal.TryParse(_Model.CorrectItem1.ReadValue.Trim(), out decimal readValue))
                {
                    this.ShowMessage(string.Format(Errors.SensorCorrectReadValueInvalid.Message, 1));
                    return false;
                }
                if (string.IsNullOrWhiteSpace(_Model.CorrectItem1.ActualValue))
                {
                    this.ShowMessage(string.Format(Errors.SensorCorrectActualValueRequired.Message, 1));
                    return false;
                }
                if (!decimal.TryParse(_Model.CorrectItem1.ActualValue.Trim(), out decimal actualValue))
                {
                    this.ShowMessage(string.Format(Errors.SensorCorrectActualValueInvalid.Message, 1));
                    return false;
                }
            }
            if (!string.IsNullOrWhiteSpace(_Model.CorrectItem1.ActualValue))
            {
                if (!decimal.TryParse(_Model.CorrectItem1.ActualValue.Trim(), out decimal actualValue))
                {
                    this.ShowMessage(string.Format(Errors.SensorCorrectActualValueInvalid.Message, 1));
                    return false;
                }
                if (string.IsNullOrWhiteSpace(_Model.CorrectItem1.ReadValue))
                {
                    this.ShowMessage(string.Format(Errors.SensorCorrectReadValueRequired.Message, 1));
                    return false;
                }
                if (!decimal.TryParse(_Model.CorrectItem1.ReadValue.Trim(), out decimal readValue))
                {
                    this.ShowMessage(string.Format(Errors.SensorCorrectReadValueInvalid.Message, 1));
                    return false;
                }
            }
            //correct 2
            if (!string.IsNullOrWhiteSpace(_Model.CorrectItem2.ReadValue))
            {
                if (!decimal.TryParse(_Model.CorrectItem2.ReadValue.Trim(), out decimal readValue))
                {
                    this.ShowMessage(string.Format(Errors.SensorCorrectReadValueInvalid.Message, 2));
                    return false;
                }
                if (string.IsNullOrWhiteSpace(_Model.CorrectItem2.ActualValue))
                {
                    this.ShowMessage(string.Format(Errors.SensorCorrectActualValueRequired.Message, 2));
                    return false;
                }
                if (!decimal.TryParse(_Model.CorrectItem2.ActualValue.Trim(), out decimal actualValue))
                {
                    this.ShowMessage(string.Format(Errors.SensorCorrectActualValueInvalid.Message, 2));
                    return false;
                }
            }
            if (!string.IsNullOrWhiteSpace(_Model.CorrectItem2.ActualValue))
            {
                if (!decimal.TryParse(_Model.CorrectItem2.ActualValue.Trim(), out decimal actualValue))
                {
                    this.ShowMessage(string.Format(Errors.SensorCorrectActualValueInvalid.Message, 2));
                    return false;
                }
                if (string.IsNullOrWhiteSpace(_Model.CorrectItem2.ReadValue))
                {
                    this.ShowMessage(string.Format(Errors.SensorCorrectReadValueRequired.Message, 2));
                    return false;
                }
                if (!decimal.TryParse(_Model.CorrectItem2.ReadValue.Trim(), out decimal readValue))
                {
                    this.ShowMessage(string.Format(Errors.SensorCorrectReadValueInvalid.Message, 2));
                    return false;
                }
            }
            //correct 3
            if (!string.IsNullOrWhiteSpace(_Model.CorrectItem3.ReadValue))
            {
                if (!decimal.TryParse(_Model.CorrectItem3.ReadValue.Trim(), out decimal readValue))
                {
                    this.ShowMessage(string.Format(Errors.SensorCorrectReadValueInvalid.Message, 3));
                    return false;
                }
                if (string.IsNullOrWhiteSpace(_Model.CorrectItem3.ActualValue))
                {
                    this.ShowMessage(string.Format(Errors.SensorCorrectActualValueRequired.Message, 3));
                    return false;
                }
                if (!decimal.TryParse(_Model.CorrectItem3.ActualValue.Trim(), out decimal actualValue))
                {
                    this.ShowMessage(string.Format(Errors.SensorCorrectActualValueInvalid.Message, 3));
                    return false;
                }
            }
            if (!string.IsNullOrWhiteSpace(_Model.CorrectItem3.ActualValue))
            {
                if (!decimal.TryParse(_Model.CorrectItem3.ActualValue.Trim(), out decimal actualValue))
                {
                    this.ShowMessage(string.Format(Errors.SensorCorrectActualValueInvalid.Message, 3));
                    return false;
                }
                if (string.IsNullOrWhiteSpace(_Model.CorrectItem3.ReadValue))
                {
                    this.ShowMessage(string.Format(Errors.SensorCorrectReadValueRequired.Message, 3));
                    return false;
                }
                if (!decimal.TryParse(_Model.CorrectItem3.ReadValue.Trim(), out decimal readValue))
                {
                    this.ShowMessage(string.Format(Errors.SensorCorrectReadValueInvalid.Message, 3));
                    return false;
                }
            }
            //correct 4
            if (!string.IsNullOrWhiteSpace(_Model.CorrectItem4.ReadValue))
            {
                if (!decimal.TryParse(_Model.CorrectItem4.ReadValue.Trim(), out decimal readValue))
                {
                    this.ShowMessage(string.Format(Errors.SensorCorrectReadValueInvalid.Message, 4));
                    return false;
                }
                if (string.IsNullOrWhiteSpace(_Model.CorrectItem4.ActualValue))
                {
                    this.ShowMessage(string.Format(Errors.SensorCorrectActualValueRequired.Message, 4));
                    return false;
                }
                if (!decimal.TryParse(_Model.CorrectItem4.ActualValue.Trim(), out decimal actualValue))
                {
                    this.ShowMessage(string.Format(Errors.SensorCorrectActualValueInvalid.Message, 4));
                    return false;
                }
            }
            if (!string.IsNullOrWhiteSpace(_Model.CorrectItem4.ActualValue))
            {
                if (!decimal.TryParse(_Model.CorrectItem4.ActualValue.Trim(), out decimal actualValue))
                {
                    this.ShowMessage(string.Format(Errors.SensorCorrectActualValueInvalid.Message, 4));
                    return false;
                }
                if (string.IsNullOrWhiteSpace(_Model.CorrectItem4.ReadValue))
                {
                    this.ShowMessage(string.Format(Errors.SensorCorrectReadValueRequired.Message, 4));
                    return false;
                }
                if (!decimal.TryParse(_Model.CorrectItem4.ReadValue.Trim(), out decimal readValue))
                {
                    this.ShowMessage(string.Format(Errors.SensorCorrectReadValueInvalid.Message, 4));
                    return false;
                }
            }
            //correct 5
            if (!string.IsNullOrWhiteSpace(_Model.CorrectItem5.ReadValue))
            {
                if (!decimal.TryParse(_Model.CorrectItem5.ReadValue.Trim(), out decimal readValue))
                {
                    this.ShowMessage(string.Format(Errors.SensorCorrectReadValueInvalid.Message, 5));
                    return false;
                }
                if (string.IsNullOrWhiteSpace(_Model.CorrectItem5.ActualValue))
                {
                    this.ShowMessage(string.Format(Errors.SensorCorrectActualValueRequired.Message, 5));
                    return false;
                }
                if (!decimal.TryParse(_Model.CorrectItem5.ActualValue.Trim(), out decimal actualValue))
                {
                    this.ShowMessage(string.Format(Errors.SensorCorrectActualValueInvalid.Message, 5));
                    return false;
                }
            }
            if (!string.IsNullOrWhiteSpace(_Model.CorrectItem5.ActualValue))
            {
                if (!decimal.TryParse(_Model.CorrectItem5.ActualValue.Trim(), out decimal actualValue))
                {
                    this.ShowMessage(string.Format(Errors.SensorCorrectActualValueInvalid.Message, 5));
                    return false;
                }
                if (string.IsNullOrWhiteSpace(_Model.CorrectItem5.ReadValue))
                {
                    this.ShowMessage(string.Format(Errors.SensorCorrectReadValueRequired.Message, 5));
                    return false;
                }
                if (!decimal.TryParse(_Model.CorrectItem5.ReadValue.Trim(), out decimal readValue))
                {
                    this.ShowMessage(string.Format(Errors.SensorCorrectReadValueInvalid.Message, 5));
                    return false;
                }
            }

            //correct 6
            if (!string.IsNullOrWhiteSpace(_Model.CorrectItem6.ReadValue))
            {
                if (!decimal.TryParse(_Model.CorrectItem6.ReadValue.Trim(), out decimal readValue))
                {
                    this.ShowMessage(string.Format(Errors.SensorCorrectReadValueInvalid.Message, 6));
                    return false;
                }
                if (string.IsNullOrWhiteSpace(_Model.CorrectItem6.ActualValue))
                {
                    this.ShowMessage(string.Format(Errors.SensorCorrectActualValueRequired.Message, 6));
                    return false;
                }
                if (!decimal.TryParse(_Model.CorrectItem6.ActualValue.Trim(), out decimal actualValue))
                {
                    this.ShowMessage(string.Format(Errors.SensorCorrectActualValueInvalid.Message, 6));
                    return false;
                }
            }
            if (!string.IsNullOrWhiteSpace(_Model.CorrectItem6.ActualValue))
            {
                if (!decimal.TryParse(_Model.CorrectItem6.ActualValue.Trim(), out decimal actualValue))
                {
                    this.ShowMessage(string.Format(Errors.SensorCorrectActualValueInvalid.Message, 6));
                    return false;
                }
                if (string.IsNullOrWhiteSpace(_Model.CorrectItem6.ReadValue))
                {
                    this.ShowMessage(string.Format(Errors.SensorCorrectReadValueRequired.Message, 6));
                    return false;
                }
                if (!decimal.TryParse(_Model.CorrectItem6.ReadValue.Trim(), out decimal readValue))
                {
                    this.ShowMessage(string.Format(Errors.SensorCorrectReadValueInvalid.Message, 6));
                    return false;
                }
            }

            //correct 7
            if (!string.IsNullOrWhiteSpace(_Model.CorrectItem7.ReadValue))
            {
                if (!decimal.TryParse(_Model.CorrectItem7.ReadValue.Trim(), out decimal readValue))
                {
                    this.ShowMessage(string.Format(Errors.SensorCorrectReadValueInvalid.Message, 7));
                    return false;
                }
                if (string.IsNullOrWhiteSpace(_Model.CorrectItem7.ActualValue))
                {
                    this.ShowMessage(string.Format(Errors.SensorCorrectActualValueRequired.Message, 7));
                    return false;
                }
                if (!decimal.TryParse(_Model.CorrectItem7.ActualValue.Trim(), out decimal actualValue))
                {
                    this.ShowMessage(string.Format(Errors.SensorCorrectActualValueInvalid.Message, 7));
                    return false;
                }
            }
            if (!string.IsNullOrWhiteSpace(_Model.CorrectItem7.ActualValue))
            {
                if (!decimal.TryParse(_Model.CorrectItem7.ActualValue.Trim(), out decimal actualValue))
                {
                    this.ShowMessage(string.Format(Errors.SensorCorrectActualValueInvalid.Message, 7));
                    return false;
                }
                if (string.IsNullOrWhiteSpace(_Model.CorrectItem7.ReadValue))
                {
                    this.ShowMessage(string.Format(Errors.SensorCorrectReadValueRequired.Message, 7));
                    return false;
                }
                if (!decimal.TryParse(_Model.CorrectItem7.ReadValue.Trim(), out decimal readValue))
                {
                    this.ShowMessage(string.Format(Errors.SensorCorrectReadValueInvalid.Message, 7));
                    return false;
                }
            }
            return true;
        }
        #endregion
        private bool ValidatePageStep4()
        {
            if (_Model.CalFormula?.Trim().ToUpper() == "ACC()")
            {
                this.ShowMessage(LanguageManager.Instance.GetString("MessageFormulaInvalidPleaseModify"));
                return false;
            }
            return true;
        }
        protected async void btnCompleteStep_Click(object sender, EventArgs e)
        {
            try
            {
                if (!AppInfo.IsAuthed)
                {
                    this.ShowError(LanguageManager.Instance.GetString("MessageSoftwareNotRegistered"));
                    return;
                }
                if (!ValidatePageStep1() || !ValidatePageStep2() || !ValidatePageStep3() || !ValidatePageStep4())
                {
                    return;
                }

                _SensorInfo ??= new();
                _SensorInfo.ComputeXS = 1;
                _SensorInfo.ComputeXSIncrement = 1;
                _SensorInfo.Sn = _Model.ModuleSN ?? "";
                _SensorInfo.Enabled = _Model.IsEnabled;
                _SensorInfo.EnabledHide = _Model.IsHidden;
                _SensorInfo.Exp = _Model.CalFormula ?? "";
                _SensorInfo.MuitValue = _Model.IsMuitValue;
                _SensorInfo.Acc = _Model.IsAccumulatedYiYe;
                _SensorInfo.Unitstr = _Model.MeasureUnitStr ?? "";
                _SensorInfo.IconId = Convert.ToUInt32(_Model.Icon);
                _SensorInfo.CheckValid = _Model.IsEnabledValidDataValue;
                _SensorInfo.UnitId = Convert.ToUInt32(_Model.MeasureUnitId);
                _SensorInfo.TypeId = Convert.ToUInt32(_Model.SensorCategory);
                _SensorInfo.RecordMid = _Model.IsStorageProductionMiddlePara;
                _SensorInfo.Decimals = Convert.ToUInt32(_Model.DecimalPrecision);
                _SensorInfo.SamplingFrequency = (uint)CalculateCollectIntervalMillay();
                _SensorInfo.LineColor = (uint)ColorExtensions.ConvertToArgbFromHex(_Model.LineColor);
                _SensorInfo.UserId = string.IsNullOrEmpty(_Model.UserGroup) ? 0 : Convert.ToUInt32(_Model.UserGroup);






                if (!string.IsNullOrEmpty(_Model.SensorNameGroup))
                {
                    _SensorInfo.Name = $"{_Model.SensorNameGroup}#{_Model.SensorName}";
                }
                else
                {
                    _SensorInfo.Name = _Model.SensorName ?? String.Empty;
                }

                _SensorInfo.DataType = Convert.ToInt32(_Model.SensorModuleType) switch
                {
                    CodeNames.SensorModuleType.Data => ProBase.Types.SensorInfo.Types.DataType.Digital,
                    CodeNames.SensorModuleType.Simulate => ProBase.Types.SensorInfo.Types.DataType.Analog,
                    CodeNames.SensorModuleType.Virtual => ProBase.Types.SensorInfo.Types.DataType.VirtualData,
                    _ => ProBase.Types.SensorInfo.Types.DataType.Digital,
                };

                _SensorInfo.AccTimeUnit = Convert.ToInt32(_Model.AccumulationTimeUnit) switch
                {
                    CodeNames.AccumulationTimeUnit.Minute => ProBase.Types.SensorInfo.Types.TimeSpanInfo.Minute,
                    CodeNames.AccumulationTimeUnit.Hour => ProBase.Types.SensorInfo.Types.TimeSpanInfo.Hour,
                    CodeNames.AccumulationTimeUnit.Day => ProBase.Types.SensorInfo.Types.TimeSpanInfo.Day,
                    _ => ProBase.Types.SensorInfo.Types.TimeSpanInfo.Minute,
                };

                if (_Model.IsEnabledValidDataValue)
                {
                    _SensorInfo.ValidValue = double.Parse(_Model.ValidDataValue.Trim());
                }

                if (IsSimulateSensor())
                {
                    _SensorInfo.RanMin = string.IsNullOrWhiteSpace(_Model.MinRange) ? 0 : float.Parse(_Model.MinRange.Trim());
                    _SensorInfo.RanMax = string.IsNullOrWhiteSpace(_Model.MaxRange) ? 0 : float.Parse(_Model.MaxRange.Trim());
                    _SensorInfo.AdcMin = string.IsNullOrWhiteSpace(_Model.ADCMinRange) ? 0 : float.Parse(_Model.ADCMinRange.Trim());
                    _SensorInfo.AdcMax = string.IsNullOrWhiteSpace(_Model.ADCMaxRange) ? 0 : float.Parse(_Model.ADCMaxRange.Trim());
                }
                if (IsSimulateSensor() || IsDataSensor())
                {
                    _SensorInfo.ConnectType = ProBase.Types.SensorInfo.Types.ConnectType.Hardware;
                    _SensorInfo.ValueId = string.IsNullOrWhiteSpace(_Model.DevIndexID) ? 0 : Convert.ToUInt32(_Model.DevIndexID.Trim());

                    _SensorInfo.Corrects ??= new ProBase.Types.SensorInfo.Types.CorrectInfoList();
                    _SensorInfo.Corrects.Items.Clear();

                    if (!string.IsNullOrWhiteSpace(_Model.CorrectItem1.ReadValue))
                    {
                        _SensorInfo.Corrects.Items.Add(new ProBase.Types.SensorInfo.Types.CorrectInfo()
                        {
                            Correction = double.Parse(_Model.CorrectItem1.ReadValue.Trim()),
                            Real = double.Parse(_Model.CorrectItem1.ActualValue.Trim()),
                        });
                    }
                    if (!string.IsNullOrWhiteSpace(_Model.CorrectItem2.ReadValue))
                    {
                        _SensorInfo.Corrects.Items.Add(new ProBase.Types.SensorInfo.Types.CorrectInfo()
                        {
                            Correction = double.Parse(_Model.CorrectItem2.ReadValue.Trim()),
                            Real = double.Parse(_Model.CorrectItem2.ActualValue.Trim()),
                        });
                    }
                    if (!string.IsNullOrWhiteSpace(_Model.CorrectItem3.ReadValue))
                    {
                        _SensorInfo.Corrects.Items.Add(new ProBase.Types.SensorInfo.Types.CorrectInfo()
                        {
                            Correction = double.Parse(_Model.CorrectItem3.ReadValue.Trim()),
                            Real = double.Parse(_Model.CorrectItem3.ActualValue.Trim()),
                        });
                    }
                    if (!string.IsNullOrWhiteSpace(_Model.CorrectItem4.ReadValue))
                    {
                        _SensorInfo.Corrects.Items.Add(new ProBase.Types.SensorInfo.Types.CorrectInfo()
                        {
                            Correction = double.Parse(_Model.CorrectItem4.ReadValue.Trim()),
                            Real = double.Parse(_Model.CorrectItem4.ActualValue.Trim()),
                        });
                    }
                    if (!string.IsNullOrWhiteSpace(_Model.CorrectItem5.ReadValue))
                    {
                        _SensorInfo.Corrects.Items.Add(new ProBase.Types.SensorInfo.Types.CorrectInfo()
                        {
                            Correction = double.Parse(_Model.CorrectItem5.ReadValue.Trim()),
                            Real = double.Parse(_Model.CorrectItem5.ActualValue.Trim()),
                        });
                    }
                    if (!string.IsNullOrWhiteSpace(_Model.CorrectItem6.ReadValue))
                    {
                        _SensorInfo.Corrects.Items.Add(new ProBase.Types.SensorInfo.Types.CorrectInfo()
                        {
                            Correction = double.Parse(_Model.CorrectItem6.ReadValue.Trim()),
                            Real = double.Parse(_Model.CorrectItem6.ActualValue.Trim()),
                        });
                    }
                    if (!string.IsNullOrWhiteSpace(_Model.CorrectItem7.ReadValue))
                    {
                        _SensorInfo.Corrects.Items.Add(new ProBase.Types.SensorInfo.Types.CorrectInfo()
                        {
                            Correction = double.Parse(_Model.CorrectItem7.ReadValue.Trim()),
                            Real = double.Parse(_Model.CorrectItem7.ActualValue.Trim()),
                        });
                    }
                }
                else
                {
                    _SensorInfo.ConnectType = ProBase.Types.SensorInfo.Types.ConnectType.Virtual;
                }

                #region 附加属性 Ahri

                if (!string.IsNullOrEmpty(_Model.SensorAdditionalPropertieTextSN) && _Model.SensorAdditionalPropertieSelected != null && !string.IsNullOrEmpty(_Model.SensorAdditionalPropertieSelected.Value))
                {
                    _SensorInfo.Sens = new ProBase.Types.SensorInfo.Types.SensorAdditionalProperties
                    {
                        Id = Convert.ToUInt32(_Model.SensorAdditionalPropertieTextSN)
                    };

                    switch (_Model.SensorAdditionalPropertieSelected.Value)
                    {

                        case "WellHead":
                            _SensorInfo.Sens.WellHeadField = new WellHead { ItemsField = ConvertToEnum<WellHead.Types.items>(_Model.SensorAdditionalPropertieChild.Value) };
                            break;
                        case "Separator":
                            _SensorInfo.Sens.SeparatorField = new Separator { ItemsField = ConvertToEnum<Separator.Types.items>(_Model.SensorAdditionalPropertieChild.Value) };
                            break;
                        case "OilNozzlePipeManifold":
                            _SensorInfo.Sens.OilNozzlePipeManifoldField = new OilNozzlePipeManifold { ItemsField = ConvertToEnum<OilNozzlePipeManifold.Types.items>(_Model.SensorAdditionalPropertieChild.Value) };
                            break;
                        case "MSRV":
                            _SensorInfo.Sens.MSRVField = new MSRV { ItemsField = ConvertToEnum<MSRV.Types.items>(_Model.SensorAdditionalPropertieChild.Value) };
                            break;
                        case "DebrisCatcher":
                            _SensorInfo.Sens.DebrisCatcherField = new DebrisCatcher { ItemsField = ConvertToEnum<DebrisCatcher.Types.items>(_Model.SensorAdditionalPropertieChild.Value) };
                            break;
                        case "Desander":
                            _SensorInfo.Sens.DesanderField = new Desander { ItemsField = ConvertToEnum<Desander.Types.items>(_Model.SensorAdditionalPropertieChild.Value) };
                            break;
                        case "HeatExchanger":
                            _SensorInfo.Sens.HeatExchangerField = new HeatExchanger { ItemsField = ConvertToEnum<HeatExchanger.Types.items>(_Model.SensorAdditionalPropertieChild.Value) };
                            break;
                        case "BufferTank":
                            _SensorInfo.Sens.BufferTankField = new BufferTank { ItemsField = ConvertToEnum<BufferTank.Types.items>(_Model.SensorAdditionalPropertieChild.Value) };
                            break;
                        case "CriticalFlowrateMeter":
                            _SensorInfo.Sens.CriticalFlowrateMeterField = new CriticalFlowrateMeter { ItemsField = ConvertToEnum<CriticalFlowrateMeter.Types.items>(_Model.SensorAdditionalPropertieChild.Value) };
                            break;
                        case "MeteringTank":
                            _SensorInfo.Sens.MeteringTankField = new MeteringTank { ItemsField = ConvertToEnum<MeteringTank.Types.items>(_Model.SensorAdditionalPropertieChild.Value) };
                            break;
                        case "GasProbe":
                            _SensorInfo.Sens.GasProbeField = new GasProbe
                            {
                                Id = Convert.ToUInt32(_Model.SensorAdditionalPropertieTextId),
                                ItemsField = ConvertToEnum<GasProbe.Types.items>(_Model.SensorAdditionalPropertieChild.Value)
                            };
                            break;
                    }
                }


                #endregion 附加属性 Ahri


                if (string.IsNullOrEmpty(_Model.SensorId))
                {
                    _SensorInfoList.Items.Add(_SensorInfo);
                    _Model.SensorId = $"{_SensorInfoList.Items.Count - 1}";
                    await UtilityHandler.LogOperationForWellStation(string.Format(LanguageManager.Instance.GetString("LogAddSensor"), _Model.SensorId, _SensorInfo.Name), _Model.WellStationId);
                }
                else
                {
                    await UtilityHandler.LogOperationForWellStation(string.Format(LanguageManager.Instance.GetString("LogModifySensor"), _Model.SensorId, _SensorInfo.Name), _Model.WellStationId);
                }
                SetMessage(_SensorInfoList);
                this.Close();
                SensorAddOrUpdated?.Invoke(null);
            }
            catch (Exception ex)
            {
                this.ShowError(ex);
            }
        }
        public static T ConvertToEnum<T>(string enumStr)
        {
            var fields = typeof(T).GetFields(BindingFlags.Static | BindingFlags.Public);
            var field = fields.FirstOrDefault(w => w.Name == enumStr);
            if (field == null)
                return default;
            return (T)Enum.Parse(typeof(T), field.Name);
        }
        protected void btnCancel_Click(object sender, EventArgs e)
        {
            try
            {
                this.Close();
            }
            catch (Exception ex)
            {
                this.ShowError(ex);
            }
        }
        protected void ddlSensorNameGroup_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                if (!ddlSensorNameGroup.IsDropDownOpen)
                {
                    return;
                }
                if (string.IsNullOrEmpty(_Model.SensorNameGroup))
                {
                    _Model.UserGroup = _Model.UserGroups.FirstOrDefault()?.Value;
                }
                else
                {
                    _Model.UserGroup = _Model.SensorNameGroup;
                }
            }
            catch (Exception ex)
            {
                this.ShowError(ex);
            }
        }
        protected void ddlSensorName_TextChanged(object sender, EventArgs e)
        {
            try
            {
                if (!(IsSimulateSensor() || IsDataSensor()))
                {
                    _Model.SensorNameTitle = _Model.SensorName?.Trim();
                }
            }
            catch (Exception ex)
            {
                this.ShowError(ex);
            }
        }
        protected void ddlSensorName_SelectionChanged(object sender, EventArgs e)
        {
            try
            {
                var ddl = sender as ComboBox;
                if (!ddl.IsDropDownOpen)
                {
                    return;
                }

                var selectItem = ddl.SelectedItem as SelectItem;
                if (selectItem != null && !string.IsNullOrEmpty(selectItem.Text) && _Model.DeviceItemInfo != null)
                {

                    _Model.DevIndexID = $"{_Model.DeviceItemInfo.ItemNames.IndexOf(selectItem.Text)}";
                }

                if (selectItem == null || string.IsNullOrWhiteSpace(selectItem.Value))
                {
                    return;
                }

            }
            catch (Exception ex)
            {
                this.ShowError(ex);
            }

        }
        protected void ddlModuleType_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                if (!ddlModuleType.IsDropDownOpen)
                {
                    return;
                }
                if (IsSimulateSensor())
                {
                    if (string.IsNullOrWhiteSpace(_Model.MinRange) && string.IsNullOrWhiteSpace(_Model.MaxRange))
                    {
                        _Model.MinRange = DefaultValues.SensorMinRange.ToString();
                        _Model.MaxRange = DefaultValues.SensorMaxRange.ToString();
                    }
                    if (string.IsNullOrWhiteSpace(_Model.ADCMinRange) && string.IsNullOrWhiteSpace(_Model.ADCMaxRange))
                    {
                        _Model.ADCMinRange = DefaultValues.ADCMinRange.ToString();
                        _Model.ADCMaxRange = DefaultValues.ADCMaxRange.ToString();
                    }
                }
                SetModuleTypeControlVisible();
            }
            catch (Exception ex)
            {
                this.ShowError(ex);
            }
        }
        protected void txtModuleSN_TextChanged(object sender, EventArgs e)
        {
            try
            {
                TextBox txtText = (TextBox)sender;
                _Model.SensorNameTitle = txtText.Text;
            }
            catch (Exception ex)
            {
                this.ShowError(ex);
            }
        }
        protected void ddlSensorCategory_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                var ddlSensorCategory = sender as ComboBox;
                if (ddlSensorCategory.SelectedItem == null)
                {
                    return;
                }
                var record = ddlSensorCategory.SelectedItem as SelectItem;
                if (record != null)
                {
                    FillMeasureUnits(record.Value);
                    // 传递当前的MeasureUnitId以保持选中状态
                    MeasureUnitAssignment(_Model.MeasureUnitId);
                }
            }
            catch (Exception ex)
            {
                this.ShowError(ex);
            }
        }

        protected void ddlMeasureUnit_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                var ddlMeasureUnit = sender as ComboBox;
                if (ddlMeasureUnit?.SelectedItem != null)
                {
                    var selectedUnit = ddlMeasureUnit.SelectedItem as SelectItem;
                    if (selectedUnit != null)
                    {
                        // selectedUnit.Value 就是在 _UnitInfos.Items 中的全局索引
                        _Model.MeasureUnitId = selectedUnit.Value;
                        _Model.MeasureUnitStr = selectedUnit.Text;
                    }
                }
            }
            catch (Exception ex)
            {
                this.ShowError(ex);
            }
        }

        private void MeasureUnitAssignment(string measureUnitId = "")
        {
            if (_Model.MeasureUnits == null) return;
            var measureUnit = _Model.MeasureUnits.FirstOrDefault();
            if (!string.IsNullOrEmpty(measureUnitId))
            {
                // 优先通过MeasureUnitStr匹配单位名称
                if (!string.IsNullOrEmpty(_Model.MeasureUnitStr))
                {
                    measureUnit = _Model.MeasureUnits.FirstOrDefault(th => th.Text == _Model.MeasureUnitStr);
                }
                // 如果没找到，直接通过全局索引匹配（measureUnitId就是全局索引）
                if (measureUnit == null)
                {
                measureUnit = _Model.MeasureUnits.FirstOrDefault(th => th.Value == measureUnitId);
                }
            }
            _Model.MeasureUnitId = measureUnit?.Value;
            _Model.MeasureUnitStr = measureUnit?.Text;
        }

        private void FillDeviceItemInfo()
        {
            _Model.SensorNames.Clear();
            if (_Model.DeviceItemInfo != null && _Model.DeviceItemInfo.ItemNames != null && _Model.DeviceItemInfo.ItemNames.Count > 0)
            {
                foreach (var itemName in _Model.DeviceItemInfo.ItemNames)
                {
                    _Model.SensorNames.Add(new()
                    {
                        Text = itemName
                    });
                }
            }

        }

        protected void chkIsEnabledValidDataValue_Checked(object sender, EventArgs e)
        {
            try
            {
                if (!_Model.IsEnabledValidDataValue)
                {
                    _Model.ValidDataValue = string.Empty;
                }
            }
            catch (Exception ex)
            {
                this.ShowError(ex);
            }
        }
        #region warning setting
        private string GetWarningFilePath(string warningFileName)
        {
            return FileHandler.GetAppDefaultDirectory() + "\\" + StoreFiles.WarningSound.DBDir + "\\" + warningFileName;
        }
        protected async void btnPlayMaxExceedWarning(object sender, EventArgs e)
        {
            try
            {
                string warningType = _Model.MaxWarningType;
                if (!string.IsNullOrEmpty(warningType))
                {
                    string warningFileName = await UtilityHandler.GetCodeValue(CategoryNames.DefaultWarningType, warningType);
                    string warningFilePath = GetWarningFilePath(warningFileName);
                    await Task.Run(() =>
                    {
                        SoundPlayerHandler.PlaySound(warningFilePath);
                    });
                }
            }
            catch (Exception ex)
            {
                this.ShowError(ex);
            }
        }
        protected async void btnPlayMinExceedWarning(object sender, EventArgs e)
        {
            try
            {
                string warningType = _Model.MinWarningType;
                if (!string.IsNullOrEmpty(warningType))
                {
                    string warningFileName = await UtilityHandler.GetCodeValue(CategoryNames.DefaultWarningType, warningType);
                    string warningFilePath = GetWarningFilePath(warningFileName);
                    await Task.Run(() =>
                    {
                        SoundPlayerHandler.PlaySound(warningFilePath);
                    });
                }
            }
            catch (Exception ex)
            {
                this.ShowError(ex);
            }
        }
        protected async void btnPlayErrorWarning(object sender, EventArgs e)
        {
            try
            {
                string warningType = _Model.ErrorWarningType;
                if (!string.IsNullOrEmpty(warningType))
                {
                    string warningFileName = await UtilityHandler.GetCodeValue(CategoryNames.DefaultWarningType, warningType);
                    string warningFilePath = GetWarningFilePath(warningFileName);
                    await Task.Run(() =>
                    {
                        SoundPlayerHandler.PlaySound(warningFilePath);
                    });
                }
            }
            catch (Exception ex)
            {
                this.ShowError(ex);
            }
        }
        protected void btnPlayLightWarning(object sender, EventArgs e)
        {
            try
            {
                string warningType = _Model.LightWarningType;
                if (string.IsNullOrEmpty(warningType))
                {
                    return;
                }
                //await _LighterService.Test(Convert.ToInt32(warningType));
            }
            catch (Exception ex)
            {
                this.ShowError(ex);
            }
        }
        #endregion
        #region data re-set
        protected void btnReadRealTimeData_Click(object sender, EventArgs e)
        {
            try
            {
                var btn = sender as Button;
                if (!uint.TryParse(_Model.SensorId, out uint sensorId) || !int.TryParse(btn.Uid, out int indexNo) || _SensorRealDataList == null || _SensorRealDataList.Items == null || _SensorRealDataList.Items.Count <= sensorId) return;

                if (_SensorRealDataList.Items.TryGetValue(sensorId, out SensorRealData realData) && SensorRealTimeDataExtensions.ConvertToSensorStatus(realData.Status) == CodeNames.SensorRealTimeStatus.Normal)
                {
                    switch (indexNo)
                    {
                        case 0:
                            _Model.CorrectItem1.ReadValue = $"{realData.Value:F3}";
                            break;
                        case 1:
                            _Model.CorrectItem2.ReadValue = $"{realData.Value:F3}";
                            break;
                        case 2:
                            _Model.CorrectItem3.ReadValue = $"{realData.Value:F3}";
                            break;
                        case 3:
                            _Model.CorrectItem4.ReadValue = $"{realData.Value:F3}";
                            break;
                        case 4:
                            _Model.CorrectItem5.ReadValue = $"{realData.Value:F3}";
                            break;
                        case 5:
                            _Model.CorrectItem6.ReadValue = $"{realData.Value:F3}";
                            break;
                        case 6:
                            _Model.CorrectItem7.ReadValue = $"{realData.Value:F3}";
                            break;
                    }


                }


            }
            catch (Exception ex)
            {
                this.ShowError(ex);
            }
        }
        #endregion

        #region formula
        protected async void ddlNormalFormula_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                var ddlNormalFormula = sender as ComboBox;
                if (ddlNormalFormula.SelectedItem == null)
                {
                    return;
                }
                var record = ddlNormalFormula.SelectedItem as SelectItem;
                if (record != null && !string.IsNullOrWhiteSpace(record.Value))
                {
                    await FormulaInsert(record.Value + "()");
                }
            }
            catch (Exception ex)
            {
                this.ShowError(ex);
            }
        }
        protected void menuInsertFormula_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var win = new FormulaListWindow();
                win.FormulaSelected += OnFormulaSelected;
                this.ShowWindowAsDialog(win);
            }
            catch (Exception ex)
            {
                this.ShowError(ex);
            }
        }
        protected void menuInsertSensorVar_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var win = new SensorPickerWindow(_Model.WellStationId);
                win.SensorSelected += OnSensorSelected;
                this.ShowWindowAsDialog(win);
            }
            catch (Exception ex)
            {
                this.ShowError(ex);
            }
        }

        private async void OnSensorSelected(string sensorId, string sensorFullName)
        {
            await FormulaInsert(sensorFullName);
        }

        protected void menuInsertEnvVar_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                try
                {
                    var win = new EnvironmentVarPickerWindow(_Model.WellStationId);
                    win.EnvironmentVarSelected += OnEnvironmentVarSelected;
                    this.ShowWindowAsDialog(win);
                }
                catch (Exception ex)
                {
                    this.ShowError(ex);
                }
            }
            catch (Exception ex)
            {
                this.ShowError(ex);
            }
        }

        private async void OnEnvironmentVarSelected(string id, string name)
        {
            await FormulaInsert(name);
        }

        protected async void menuInsertFormulaInput_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                await FormulaInsert("Input");
            }
            catch (Exception ex)
            {
                this.ShowError(ex);
            }
        }

        private async void OnFormulaSelected(FormulaListViewModel.FormulaInfo formula)
        {
            if (!string.IsNullOrWhiteSpace(formula.Name))
            {
                await FormulaInsert(formula.Name + "()");
            }
        }
        private async Task FormulaInsert(string insertText)
        {
            var lastCaretIndex = txtCalFormula.CaretIndex;
            txtCalFormula.Text = txtCalFormula.Text?.Insert(lastCaretIndex, insertText);
            if (insertText.EndsWith("()"))
            {
                txtCalFormula.CaretIndex = lastCaretIndex + insertText.Length - 1;
            }
            else
            {
                txtCalFormula.CaretIndex = lastCaretIndex + insertText.Length;
            }
            CheckFormula(txtCalFormula.Text?.Trim());
        }

        protected async void txtCalFormula_TextChanged(object sender, EventArgs e)
        {
            try
            {
                string formulaText = _Model.CalFormula?.Trim();
                CheckFormula(formulaText);
            }
            catch
            {
            }
        }
        private void CheckFormula(string formulaText)
        {
            //var listEnvVar = await GetAllFormulaEnvironmentVars();
            //bool isValid = StringHandler.ValidateFormula(listEnvVar, formulaText, out string result);
            if (AppInfo.IsServerStarted)
            {
                Commiunication.ThreadedWorker.CheckExpression(formulaText);
            }

        }
        private List<string> _FormulaEnvironmentVars = null;
        private async Task<List<string>> GetAllFormulaEnvironmentVars()
        {
            if (_FormulaEnvironmentVars != null)
            {
                return _FormulaEnvironmentVars;
            }
            var result = new List<string>();

            //var environmentVarResponse = await _EnvironmentVariableService.GetAll(_Model.WellStationId);
            //if (environmentVarResponse.IsSuccess)
            //{
            //    foreach (var environmentVar in environmentVarResponse.Content.EnvironmentVariables)
            //    {
            //        result.Add(environmentVar.Name);
            //    }
            //}
            //result = result.OrderByDescending(th => th.Length).ToList();
            //_FormulaEnvironmentVars = result;
            return result;
        }
        #endregion


        #region 新增业务 Ahri

        private void BindSensorInfo()
        {
            int collectIntervalMill = 0;
            _Model.NormalFormulas = FillFunctionInfos();
            _Model.AccumulationTimeUnit = $"{CodeNames.AccumulationTimeUnit.Minute}";
            if (_SensorInfo != null)
            {
                _Model.SensorNameGroup = "";
                _Model.ModuleSN = _SensorInfo.Sn;
                _Model.CalFormula = _SensorInfo.Exp;
                _Model.Icon = $"{_SensorInfo.IconId}";
                _Model.IsEnabled = _SensorInfo.Enabled;
                string sensorFullName = _SensorInfo.Name;
                _Model.MinRange = $"{_SensorInfo.RanMin}";
                _Model.MaxRange = $"{_SensorInfo.RanMax}";
                _Model.IsHidden = _SensorInfo.EnabledHide;
                _Model.IsAccumulatedYiYe = _SensorInfo.Acc;
                _Model.UserGroup = $"{_SensorInfo.UserId}";
                _Model.IsMuitValue = _SensorInfo.MuitValue;
                _Model.DevIndexID = $"{_SensorInfo.ValueId}";
                _Model.ADCMinRange = $"{_SensorInfo.AdcMin}";
                _Model.ADCMaxRange = $"{_SensorInfo.AdcMax}";
                _Model.MeasureUnitStr = _SensorInfo.Unitstr;
                _Model.MeasureUnitId = $"{_SensorInfo.UnitId}";
                _Model.SensorCategory = $"{_SensorInfo.TypeId}";
                _Model.DecimalPrecision = $"{_SensorInfo.Decimals}";
                _Model.IsEnabledValidDataValue = _SensorInfo.CheckValid;
                collectIntervalMill = (int)_SensorInfo.SamplingFrequency;
                _Model.IsStorageProductionMiddlePara = _SensorInfo.RecordMid;
                _Model.LineColor = ColorExtensions.ConvertToHexFromArgb((int)_SensorInfo.LineColor);


                if (_Model.IsEnabledValidDataValue)
                {
                    _Model.ValidDataValue = $"{_SensorInfo.ValidValue}";
                }

                if (sensorFullName.Length > 2 && sensorFullName[1] == '#')
                {
                    _Model.SensorName = sensorFullName[2..];
                    _Model.SensorNameGroup = sensorFullName[..1];
                }
                else
                {
                    _Model.SensorName = sensorFullName;
                }
                _Model.SensorModuleType = _SensorInfo.DataType switch
                {
                    ProBase.Types.SensorInfo.Types.DataType.Digital => $"{CodeNames.SensorModuleType.Data}",
                    ProBase.Types.SensorInfo.Types.DataType.Analog => $"{CodeNames.SensorModuleType.Simulate}",
                    ProBase.Types.SensorInfo.Types.DataType.VirtualData => $"{CodeNames.SensorModuleType.Virtual}",
                    _ => $"{CodeNames.SensorModuleType.Simulate}",
                };

                if (_Model.IsAccumulatedYiYe)
                {
                    _Model.AccumulationTimeUnit = _SensorInfo.AccTimeUnit switch
                    {
                        ProBase.Types.SensorInfo.Types.TimeSpanInfo.Minute => $"{CodeNames.AccumulationTimeUnit.Minute}",
                        ProBase.Types.SensorInfo.Types.TimeSpanInfo.Hour => $"{CodeNames.AccumulationTimeUnit.Hour}",
                        ProBase.Types.SensorInfo.Types.TimeSpanInfo.Day => $"{CodeNames.AccumulationTimeUnit.Day}",
                        _ => $"{CodeNames.AccumulationTimeUnit.Minute}",
                    };

                }

                if (_SensorInfo.Corrects != null && _SensorInfo.Corrects.Items != null)
                {
                    if (_SensorInfo.Corrects.Items.Count > 0)
                    {
                        _Model.CorrectItem1 = new()
                        {
                            ActualValue = $"{_SensorInfo.Corrects.Items[0].Real}",
                            ReadValue = $"{_SensorInfo.Corrects.Items[0].Correction}",
                        };
                    }
                    if (_SensorInfo.Corrects.Items.Count > 1)
                    {
                        _Model.CorrectItem2 = new()
                        {
                            ActualValue = $"{_SensorInfo.Corrects.Items[1].Real}",
                            ReadValue = $"{_SensorInfo.Corrects.Items[1].Correction}",
                        };
                    }
                    if (_SensorInfo.Corrects.Items.Count > 2)
                    {
                        _Model.CorrectItem3 = new()
                        {
                            ActualValue = $"{_SensorInfo.Corrects.Items[2].Real}",
                            ReadValue = $"{_SensorInfo.Corrects.Items[2].Correction}",
                        };
                    }
                    if (_SensorInfo.Corrects.Items.Count > 3)
                    {
                        _Model.CorrectItem4 = new()
                        {
                            ActualValue = $"{_SensorInfo.Corrects.Items[3].Real}",
                            ReadValue = $"{_SensorInfo.Corrects.Items[3].Correction}",
                        };
                    }
                    if (_SensorInfo.Corrects.Items.Count > 4)
                    {
                        _Model.CorrectItem5 = new()
                        {
                            ActualValue = $"{_SensorInfo.Corrects.Items[4].Real}",
                            ReadValue = $"{_SensorInfo.Corrects.Items[4].Correction}",
                        };
                    }
                    if (_SensorInfo.Corrects.Items.Count > 5)
                    {
                        _Model.CorrectItem6 = new()
                        {
                            ActualValue = $"{_SensorInfo.Corrects.Items[5].Real}",
                            ReadValue = $"{_SensorInfo.Corrects.Items[5].Correction}",
                        };
                    }
                    if (_SensorInfo.Corrects.Items.Count > 6)
                    {
                        _Model.CorrectItem7 = new()
                        {
                            ActualValue = $"{_SensorInfo.Corrects.Items[6].Real}",
                            ReadValue = $"{_SensorInfo.Corrects.Items[6].Correction}",
                        };
                    }
                }

                //加载附加属性
                FillAdditionalPropertie();
            }
            else
            {
                _Model.DevIndexID = "0";
                _Model.IsEnabled = true;
                _Model.Icon = _Model.Icons.FirstOrDefault()?.Value;
                _Model.UserGroup = _Model.UserGroups.FirstOrDefault()?.Value;
                _Model.DecimalPrecision = $"{DefaultValues.DecimalPrecision}";
                _Model.SensorCategory = _Model.SensorCategories.FirstOrDefault()?.Value;
                _Model.SensorNameGroup = _Model.SensorNameGroups.FirstOrDefault()?.Value;
                //默认值
                if (!string.IsNullOrWhiteSpace(_Model.ModuleSN))
                {
                    _Model.ADCMinRange = $"{DefaultValues.ADCMinRange}";
                    _Model.ADCMaxRange = $"{DefaultValues.ADCMaxRange}";
                    _Model.MinRange = $"{DefaultValues.SensorMinRange}";
                    _Model.MaxRange = $"{DefaultValues.SensorMaxRange}";
                    _Model.SensorModuleType = $"{CodeNames.SensorModuleType.Simulate}";
                }
                else
                {
                    _Model.SensorName = LanguageManager.Instance.GetString("TextVirtualSensor");
                    _Model.SensorModuleType = $"{CodeNames.SensorModuleType.Virtual}";
                }

                MeasureUnitAssignment();
            }
            FillMeasureUnits(_Model.SensorCategory);
            // 在填充完单位列表后，重新匹配当前传感器的单位
            if (_SensorInfo != null)
            {
                MeasureUnitAssignment(_Model.MeasureUnitId);
            }
            BindCollectIntervalMillay(collectIntervalMill);
            SetModuleTypeControlVisible();

            _Model.IsEnableReadRealTime = !string.IsNullOrEmpty(_Model.WellStationId) && !string.IsNullOrEmpty(_Model.SensorId) && AppInfo.IsDataMonitored();
        }

        private void FillAdditionalPropertie()
        {
            #region 附加属性 Ahri

            if (_SensorInfo != null && _SensorInfo.Sens != null)
            {
                string type = "";
                string value = "";
                _Model.SensorAdditionalPropertieTextSN = $"{_SensorInfo.Sens.Id}";
                if (_SensorInfo.Sens.WellHeadField != null)
                {
                    type = "WellHead";
                    value = $"{_SensorInfo.Sens.WellHeadField.ItemsField}";
                }
                else if (_SensorInfo.Sens.SeparatorField != null)
                {
                    type = "Separator";
                    value = $"{_SensorInfo.Sens.SeparatorField.ItemsField}";
                }
                else if (_SensorInfo.Sens.OilNozzlePipeManifoldField != null)
                {
                    type = "OilNozzlePipeManifold";
                    value = $"{_SensorInfo.Sens.OilNozzlePipeManifoldField.ItemsField}";
                }
                else if (_SensorInfo.Sens.MSRVField != null)
                {
                    type = "MSRV";
                    value = $"{_SensorInfo.Sens.MSRVField.ItemsField}";
                }
                else if (_SensorInfo.Sens.DebrisCatcherField != null)
                {
                    type = "DebrisCatcher";
                    value = $"{_SensorInfo.Sens.DebrisCatcherField.ItemsField}";
                }
                else if (_SensorInfo.Sens.DesanderField != null)
                {
                    type = "Desander";
                    value = $"{_SensorInfo.Sens.DesanderField.ItemsField}";
                }
                else if (_SensorInfo.Sens.HeatExchangerField != null)
                {
                    type = "HeatExchanger";
                    value = $"{_SensorInfo.Sens.HeatExchangerField.ItemsField}";
                }
                else if (_SensorInfo.Sens.BufferTankField != null)
                {
                    type = "BufferTank";
                    value = $"{_SensorInfo.Sens.BufferTankField.ItemsField}";
                }
                else if (_SensorInfo.Sens.CriticalFlowrateMeterField != null)
                {
                    type = "CriticalFlowrateMeter";
                    value = $"{_SensorInfo.Sens.CriticalFlowrateMeterField.ItemsField}";
                }
                else if (_SensorInfo.Sens.MeteringTankField != null)
                {
                    type = "MeteringTank";
                    value = $"{_SensorInfo.Sens.MeteringTankField.ItemsField}";
                }
                else if (_SensorInfo.Sens.GasProbeField != null)
                {
                    type = "GasProbe";
                    value = $"{_SensorInfo.Sens.GasProbeField.ItemsField}";
                    _Model.SensorAdditionalPropertieTextId = $"{_SensorInfo.Sens.GasProbeField.Id}";
                }

                if (!string.IsNullOrEmpty(type) && !string.IsNullOrEmpty(value))
                {
                    _Model.SensorAdditionalPropertieSelected = _Model.SensorAdditionalProperties.First(d => d.Value.Equals(type));
                    _Model.SensorAdditionalPropertieChild = _Model.SensorAdditionalPropertieChilds.First(d => d.Value.Equals(value));
                }
            }


            #endregion 附加属性 Ahri
        }
        private void BindCollectIntervalMillay(int collectIntervalMill)
        {
            if (collectIntervalMill <= 0)
            {
                _Model.CollectInterval = "0";
                _Model.CollectIntervalUnit = CodeNames.CollectIntervalUnit.Second;
                return;
            }
            int tempTimeUnit = defaultTimeUnit * defaultTimeUnit;
            if (collectIntervalMill / tempTimeUnit > 0)
            {
                //second
                _Model.CollectInterval = (collectIntervalMill / tempTimeUnit).ToString();
                _Model.CollectIntervalUnit = CodeNames.CollectIntervalUnit.Second;
            }
            else if (collectIntervalMill / defaultTimeUnit > 0)
            {
                //Millisecond
                _Model.CollectInterval = (collectIntervalMill / defaultTimeUnit).ToString();
                _Model.CollectIntervalUnit = CodeNames.CollectIntervalUnit.Millisecond;
            }

        }

        private int CalculateCollectIntervalMillay()
        {
            int collectInteralMicro = Convert.ToInt32(_Model.CollectInterval);
            if (collectInteralMicro <= 0) return collectInteralMicro;

            switch (_Model.CollectIntervalUnit)
            {
                case CodeNames.CollectIntervalUnit.Second:
                    collectInteralMicro *= defaultTimeUnit * defaultTimeUnit;
                    break;
                case CodeNames.CollectIntervalUnit.Millisecond:
                    collectInteralMicro *= defaultTimeUnit;
                    break;
            }
            return collectInteralMicro;
        }

        private void LoadMeasureUnitListay(string sensorCategory)
        {
            _Model.MeasureUnits = new List<SelectItem>();
            if (!string.IsNullOrEmpty(sensorCategory))
            {
                var listBaseMeasureUnits = UtilityHandler.GetBaseMeasureUnitsay(Convert.ToInt32(sensorCategory), _Model.WellStationId);
                _Model.MeasureUnits = listBaseMeasureUnits;
            }
        }


        private void LoadSensorAdditionalPropertie(DataCollection.ApplicationCore.Domain.Response.SensorDetailSettingResponseContent.SensorAdditionalProperties sensorAdditionalProperties)
        {
            if (sensorAdditionalProperties != null && !string.IsNullOrEmpty(sensorAdditionalProperties.SN))
            {
                _Model.SensorAdditionalPropertieTextSN = sensorAdditionalProperties.SN;
                _Model.SensorAdditionalPropertieTextId = sensorAdditionalProperties.Id ?? "";
                _Model.SensorAdditionalPropertieSelected = _Model.SensorAdditionalProperties.First(d => d.Value.Equals(sensorAdditionalProperties.Type));

                _Model.SensorAdditionalPropertieChild = _Model.SensorAdditionalPropertieChilds.First(d => d.Value.Equals(sensorAdditionalProperties.Value));

            }
        }

        #endregion 新增业务 Ahri
    }
}
