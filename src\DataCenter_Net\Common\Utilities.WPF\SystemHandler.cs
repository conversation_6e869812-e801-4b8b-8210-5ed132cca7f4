﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Management;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;

namespace SDHD.DC.Utilities.WPF
{
#pragma warning disable CA1416
    public class SystemHandler : ISystemHandler
    {
        /// <summary>
        /// 取得硬盘序列号
        /// </summary>
        /// <returns></returns>
        public string GetDiskDriveSerialNo(string specifiedDisk = null)
        {
            string sysDisk;
            if (!string.IsNullOrWhiteSpace(specifiedDisk))
            {
                sysDisk = specifiedDisk;
            }
            else
            {
                sysDisk = FileHandler.GetAppInstallDisk();
            }
            ManagementObject disk = new ManagementObject($"win32_logicaldisk.deviceid=\"{sysDisk}:\"");
            disk.Get();
            string result = disk.GetPropertyValue("VolumeSerialNumber").ToString();
            return result;
        }
        /// <summary>
        /// 获取CPU序列号
        /// </summary>
        /// <returns></returns>
        public string GetCPUSerialNo()
        {
            string result = string.Empty;
            ManagementObjectSearcher Wmi = new ManagementObjectSearcher("SELECT * FROM Win32_Processor");
            foreach (ManagementObject WmiObj in Wmi.Get())
            {
                result = WmiObj["ProcessorId"].ToString();
                break;
            }
            return result;
        }
        /// <summary>
        /// 获取Borad序列号
        /// </summary>
        /// <returns></returns>
        public string GetBaseBoardSerialNo()
        {
            string result = string.Empty;
            ManagementObjectSearcher Wmi = new ManagementObjectSearcher("SELECT * FROM WIN32_BaseBoard");
            foreach (ManagementObject WmiObj in Wmi.Get())
            {
                result = WmiObj["SerialNumber"].ToString();
                break;
            }
            return result;
        }

        /// <summary>
        ///当前系统所硬盘的物理编号
        /// </summary>
        /// <returns></returns>
        public string GetOSHardDiskID(bool returnOriginalString = false)
        {
            string hardDiskID = null;
            string systemDisk = Path.GetPathRoot(Environment.SystemDirectory).TrimEnd('\\');
            using (var m1 = new ManagementObjectSearcher("ASSOCIATORS OF {Win32_LogicalDisk.DeviceID='" + systemDisk + "'} WHERE ResultClass=Win32_DiskPartition"))
            {
                foreach (var i1 in m1.Get())
                {
                    using (var m2 = new ManagementObjectSearcher("ASSOCIATORS OF {Win32_DiskPartition.DeviceID='" + i1["DeviceID"] + "'} WHERE ResultClass=Win32_DiskDrive"))
                    {
                        foreach (var i2 in m2.Get())
                        {
                            hardDiskID = i2["SerialNumber"].ToString().Trim();
                            break;
                        }
                    }
                    break;
                }
            }
            if (!returnOriginalString)
            {
                hardDiskID = ProcessHardDiskIDByOSVersion(hardDiskID);
            }
            return hardDiskID;
        }
        private string ProcessHardDiskIDByOSVersion(string hardDiskID)
        {
            //hardDiskID = "2020202057202d44585731503341383030343435"; //win7 format
            if (hardDiskID.Length == 40)
            {
                try
                {
                    //win7 format is Hex format,然后字符从右向左2个一组反序后
                    if (StringHandler.TryConvertFromHexString(hardDiskID, out byte[] data))
                    {
                        var arrChars = Encoding.ASCII.GetString(data).Trim().ToCharArray();
                        for (int i = arrChars.Length; i > 0;)
                        {
                            var a = arrChars[i - 1];
                            var b = arrChars[i - 2];
                            arrChars[i - 1] = b;
                            arrChars[i - 2] = a;
                            i = i - 2;
                        }
                        var result = string.Join("", arrChars).Trim();
                        return result;
                    }
                    else
                    {
                        return hardDiskID;
                    }
                }
                catch
                {
                    return hardDiskID;
                }
            }
            else
            {
                return hardDiskID;
            }
        }
        public string GetMechineNum()
        {
            var cpuSN = GetCPUSerialNo().ToCharArray();
            uint cpu_id = 0;
            {
                char[] buf = new char[] { '1', '9', '7', '8', '0', '3', ',', '8' };
                int j = 0;
                for (int i = 0; i < cpuSN.Length; i++)
                {
                    if (cpuSN[i] != 0x2d && cpuSN[i] != 0x20 && cpuSN[i] != 0x0d && cpuSN[i] != 0x0a)
                    {
                        buf[j] = (char)(buf[j] ^ cpuSN[i]);
                        j++;
                        if (j >= 8) j = 0;
                    }
                }
                var dataBytes = Encoding.ASCII.GetBytes(buf);
                var a = BitConverter.ToUInt32(dataBytes, 0);
                var b = BitConverter.ToUInt32(dataBytes, 4);
                cpu_id = a ^ b;
            }
            if (cpu_id == 0)
            {
                throw new InvalidOperationException("Cannot get the CPUID.");
                //cpu_id = GetCPUID();
            }
            uint disk_id = 0;
            {
                char[] buf = new char[] { '1', '9', '7', '8', '0', '3', ',', '8' };
                var diskSN = GetOSHardDiskID(true).ToCharArray();
                int j = 0;
                for (int i = 0; i < diskSN.Length; i++)
                {
                    if (diskSN[i] != 0x2d && diskSN[i] != 0x20 && diskSN[i] != 0x0d && diskSN[i] != 0x0a)
                    {
                        buf[j] = (char)(buf[j] ^ diskSN[i]);
                        j++;
                        if (j >= 8) j = 0;
                    }
                }
                var dataBytes = Encoding.ASCII.GetBytes(buf);
                var a = BitConverter.ToUInt32(dataBytes, 0);
                var b = BitConverter.ToUInt32(dataBytes, 4);
                disk_id = a ^ b;
            }
            if (disk_id == 0)
            {
                char[] buf = new char[] { '1', '9', '7', '8', '0', '3', ',', '8' };
                var boardSN = GetBaseBoardSerialNo().ToCharArray();
                uint board_id = 0;
                int j = 0;
                for (int i = 0; i < boardSN.Length; i++)
                {
                    if (boardSN[i] != 0x2d && boardSN[i] != 0x20 && boardSN[i] != 0x0d && boardSN[i] != 0x0a)
                    {
                        buf[j] = (char)(buf[j] ^ boardSN[i]);
                        j++;
                        if (j >= 8) j = 0;
                    }
                }
                var dataBytes = Encoding.ASCII.GetBytes(buf);
                var a = BitConverter.ToUInt32(dataBytes, 0);
                var b = BitConverter.ToUInt32(dataBytes, 4);
                board_id = a ^ b;
                disk_id = board_id;
            }
            if (disk_id == 0)
            {
                //disk_id = GetCPUID();
                return string.Empty;
            }
            Int64 macId = cpu_id;
            macId = (macId << 32) | (long)disk_id;

            var macBytes = BitConverter.GetBytes(macId);
            var strContent = BitConverter.ToString(macBytes);
            var arrStr = strContent.Split('-').Reverse(); //两个为一组，反序
            var result = string.Join("", arrStr);
            return result;
        }
        private uint GetCPUID()
        {
            uint BRANDID = 0x80000002;  // 从0x80000002开始，到0x80000004结束
            var cBrand = new char[100];// { (char)0 };    // 用来存储商标字符串，48个字符
            CPUIF m = new CPUIF();
            m.type = 0;
            Executecpuid(m);
            m.mExif.EX.rEAX = 0;
            Buffer.BlockCopy(m.mExif.str, 0, cBrand, 0, 12);
            for (int i = 0; i < 3; i++)   // 依次执行3个指令
            {
                m.type = BRANDID + (uint)i;
                Executecpuid(m);
                // 每次执行结束后，保存四个寄存器里的asc码到数组
                Buffer.BlockCopy(m.mExif.str, 0, cBrand, 12 + i * 16, 16);
            }
            uint id = 0;
            var dataBytes = Encoding.ASCII.GetBytes(cBrand);
            id = BitConverter.ToUInt32(dataBytes, 0) ^ BitConverter.ToUInt32(dataBytes, 4)
                ^ BitConverter.ToUInt32(dataBytes, 8) ^ BitConverter.ToUInt32(dataBytes, 12)
                ^ BitConverter.ToUInt32(dataBytes, 16) ^ BitConverter.ToUInt32(dataBytes, 20)
                ^ BitConverter.ToUInt32(dataBytes, 24) ^ BitConverter.ToUInt32(dataBytes, 28);
            return id;
            //return string(cBrand);  // 以string的形式返回
        }

        private void Executecpuid(CPUIF M)
        {
            // 因为嵌入式的汇编代码不能识别 类成员变量
            // 所以定义四个临时变量作为过渡
            uint deax = M.type;
            uint debx = 0;
            uint decx = 0;
            uint dedx = 0;
            //TODO:Ahri— asm  汇编代码 Executecpuid
            // __asm
            //{
            //  mov eax, deax
            //  cpuid
            //  mov deax, eax
            //  mov debx, ebx
            //  mov decx, ecx
            //  mov dedx, edx
            //}
            M.mExif.EX.rEAX = deax; // 把临时变量中的内容放入类成员变量
            M.mExif.EX.rEBX = debx;
            M.mExif.EX.rECX = decx;
            M.mExif.EX.rEDX = dedx;
        }
    }
#pragma warning restore CA1416
    public struct EX
    {
        public uint rEBX, rEDX, rECX, rEAX;
    }
    [StructLayout(LayoutKind.Explicit, Size = 4 * 4 + 17)]
    public struct EXIF
    {
        [FieldOffset(0)]
        public EX EX;
        [FieldOffset(0)]
        public char[] str = new char[17];
        public EXIF()
        {
            EX = new EX();
        }
    }
    public struct CPUIF
    {
        public uint type;
        public EXIF mExif;
    }
}
