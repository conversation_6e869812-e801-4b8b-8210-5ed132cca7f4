# Simple fix for UserHandler references
$content = Get-Content 'WinForm\DataMonitor\MainWindow.xaml.cs' -Raw

# Fix LogOut call
$content = $content -replace 'UserHandler\.Instance\.LogOut\(\);', '// UserHandler removed'

# Fix RefreshLoginUser call - use simpler replacement
$content = $content -replace 'UserHandler\.Instance\.RefreshLoginUser\(\)', 'null'

# Save file
$content | Set-Content 'WinForm\DataMonitor\MainWindow.xaml.cs'

Write-Host "Fixed UserHandler references"
