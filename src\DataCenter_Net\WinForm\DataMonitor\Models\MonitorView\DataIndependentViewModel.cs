﻿using SDHD.DC.DataMonitor.Utils;
using SDHD.DC.Utilities;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Media;

namespace SDHD.DC.DataMonitor.Models.MonitorView
{
    public class DataIndependentViewModel : NotifyPropertyChangedModel
    {
        public string WindowTitle { get; set; }
        public string WellStationId { get; set; }
        public string SensorId { get; set; }

        private string _SensorFullName;
        public string SensorFullName
        {
            get { return _SensorFullName; }
            set
            {
                if (_SensorFullName == value) return;
                _SensorFullName = value;
                OnPropertyChanged(nameof(SensorFullName));
            }
        }

        private string _DataValue;
        public string DataValue
        {
            get { return _DataValue; }
            set
            {
                if (_DataValue == value) return;
                _DataValue = value;
                OnPropertyChanged(nameof(DataValue));
            }
        }
        private string _DisplayDataUnit;
        public string DisplayDataUnit
        {
            get { return _DisplayDataUnit; }
            set
            {
                if (_DisplayDataUnit == value) return;
                _DisplayDataUnit = value;
                OnPropertyChanged(nameof(DisplayDataUnit));
            }
        }

        private string _DataUnit;
        public string DataUnit
        {
            get { return _DataUnit; }
            set
            {
                if (_DataUnit == value) return;
                _DataUnit = value;
                OnPropertyChanged(nameof(DataUnit));
            }
        }

        public int DataPrecision { get; set; }

        private int _FontSize = 0;
        public int FontSize
        {
            get { return _FontSize; }
            set
            {
                if (_FontSize == value) return;
                _FontSize = value;
                OnPropertyChanged(nameof(FontSize));
            }
        }

        private int _FontSizeSuper = 0;
        public int FontSizeSuper
        {
            get { return _FontSizeSuper; }
            set
            {
                if (_FontSizeSuper == value) return;
                _FontSizeSuper = value;
                OnPropertyChanged(nameof(FontSizeSuper));
            }
        }

        private SolidColorBrush _FontColor;
        public SolidColorBrush FontColor
        {
            get { return _FontColor; }
            set
            {
                if (_FontColor == value) return;
                _FontColor = value;
                OnPropertyChanged(nameof(FontColor));
            }
        }

    }
}
