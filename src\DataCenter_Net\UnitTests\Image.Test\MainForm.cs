using SDHD.DC.Utilities;
using SDHD.DC.Utilities.WPF;

namespace Image.Test
{
    public partial class MainForm : Form
    {
        public MainForm()
        {
            InitializeComponent();
            Init();
        }
        protected void Init()
        {
            List<string> listDisk = new List<string>()
            {
                "C", "D", "E", "F", "G","H","I"
            };
            ddlDisk.DataSource = listDisk;
            ddlDisk.SelectedIndex = 0;
        }

        private void button1_Click(object sender, EventArgs e)
        {
            try
            {
                System.Drawing.Bitmap bitmap = new System.Drawing.Bitmap(pictureBox1.Width, pictureBox1.Height, System.Drawing.Imaging.PixelFormat.Format32bppArgb);
                bitmap.SetResolution(72, 72);
                //bitmap.SetResolution(96, 96);
                //bitmap.SetResolution(120, 120);

                using (Graphics g = Graphics.FromImage(bitmap))
                {
                    g.Clear(Color.White);
                    g.TextRenderingHint = System.Drawing.Text.TextRenderingHint.ClearTypeGridFit;
                    g.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;
                    g.CompositingQuality = System.Drawing.Drawing2D.CompositingQuality.HighQuality;
                    g.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;
                    //g.PixelOffsetMode = System.Drawing.Drawing2D.PixelOffsetMode.HighQuality;
                    
                    Pen pen = new Pen(new SolidBrush(Color.Black), 0.1f);
                    g.DrawLine(pen, 0, 0, 80, 80);
                    pen = new Pen(new SolidBrush(Color.Black), 0.5f);
                    g.DrawLine(pen, 20, 0, 90, 90);

                    pen = new Pen(new SolidBrush(Color.Black), 1f);
                    g.DrawLine(pen, 30, 0, 90, 90);

                    System.Drawing.Font drawFont = new System.Drawing.Font("Arial", 12);
                    System.Drawing.SolidBrush drawBrush = new System.Drawing.SolidBrush(Color.Black);
                    g.DrawString("2020-33-33 33:99:000", drawFont, drawBrush, 50, 50);
                }
                bitmap.Save("D:\\1.bmp");
                pictureBox1.Image = bitmap;
                pictureBox1.Image.Save("D:\\2.bmp");
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        private void btnLoadCPU_Click(object sender, EventArgs e)
        {
            try
            {
                var sysHandler = new SystemHandler();
                var cpuNo = sysHandler.GetCPUSerialNo();
                string disk = ddlDisk.Text;
                var diskNo = sysHandler.GetDiskDriveSerialNo(disk);
                txtCPUSN.Text = cpuNo.ToString();
                txtDiskSN.Text = diskNo.ToString();
                txtHardDiskID.Text = sysHandler.GetOSHardDiskID();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }
    }
}