﻿using SDHD.DC.DataMonitor.Utils;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Media;

namespace SDHD.DC.DataMonitor.Models.Sensor
{
    public class SensorListViewModel : NotifyPropertyChangedModel
    {
        public string WindowTitle { set; get; }
        public string WellStationId { set; get; }
        public ObservableCollection<SensorInfo> Sensors { set; get; } = new ObservableCollection<SensorInfo>();

        private bool _IsAddEnabled = true;
        public bool IsAddEnabled
        {
            get { return _IsAddEnabled; }
            set
            {
                _IsAddEnabled = value;
                OnPropertyChanged(nameof(IsAddEnabled));
            }
        }

        private bool _IsEditEnabled = false;
        public bool IsEditEnabled
        {
            get { return _IsEditEnabled; }
            set
            {
                _IsEditEnabled = value;
                OnPropertyChanged(nameof(IsEditEnabled));
            }
        }

        public class SensorInfo
        {
            public string SensorId { set; get; }
            public string ModuleSN { set; get; }
            public string SensorName { set; get; }
            public string SensorCategory { set; get; }
            public bool IsEnabled { set; get; }
            public string IsEnabledDesc { set; get; }
            public string MeasureUnit { set; get; }
            public string MinRange { set; get; }
            public string MaxRange { set; get; }
            public string MinExceedWarning { set; get; }
            public string MaxExceedWarning { set; get; }
            public string CalFormula { set; get; }
            public string LineColor { set; get; } = "#000000";
            public SolidColorBrush LineColorBrush
            {
                get
                {
                    var color = ColorExtensionsWPF.ConvertFromHex(LineColor, true);
                    return new SolidColorBrush(color);
                }
            }
        }
    }
}
