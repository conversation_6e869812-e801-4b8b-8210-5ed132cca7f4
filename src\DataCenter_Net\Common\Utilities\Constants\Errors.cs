﻿using System;
using System.Collections.Generic;
using System.Text;

namespace SDHD.DC.Utilities.Constants
{
    public class Errors
    {
        public static ErrorInfo ServerConnectFailure = new ErrorInfo("SERVER_CONNECT_FAILURE", "服务器连接失败！");
        public static ErrorInfo ServerConnectTimeout = new ErrorInfo("SERVER_CONNECT_TIMEOUT", "服务器连接超时！");
        public static ErrorInfo AuthFailure = new ErrorInfo("AUTH_FAILURE", "授权失败！");
        public static ErrorInfo RequestToStartServerFirst = new ErrorInfo("RequestToStartServerFirst", "服务未启动，请先启动服务！");

        public static ErrorInfo WellStationNameRequired = new ErrorInfo("WellStationNameRequired", "井场名称不能为空！");
        public static ErrorInfo WellStationNameCannotIncludeCommaAndSemicolon = new ErrorInfo("WellStationNameCannotIncludeCommaAndSemicolon", "井场名称不能包含英文逗号或分号！");
        public static ErrorInfo CollectFrequencyRequired = new ErrorInfo("CollectFrequencyRequired", "采样时间不能为空！");
        public static ErrorInfo CollectFrequencyInvalid = new ErrorInfo("CollectFrequencyInvalid", "采样时间格式不正确！");
        public static ErrorInfo CollectFrequencySmallerThanOne = new ErrorInfo("CollectFrequencySmallerThanOne", "采样时间不能小于1！");

        public static ErrorInfo ErrorAndLoginFailure = new ErrorInfo("ERROR_AND_LOGIN_FAILURE", "发生错误，登录失败！");
        public static ErrorInfo UserNameRequired = new ErrorInfo("USERNAME_REQUIRED", "用户名不能为空！");
        public static ErrorInfo UserNameInvalid = new ErrorInfo("USERNAME_INVALID", "用户名格式不正确！");
        public static ErrorInfo PasswordRequired = new ErrorInfo("PASSWORD_REQUIRED", "密码不能为空！");
        public static ErrorInfo PasswordInvalid = new ErrorInfo("PASSWORD_INVALID", "密码格式不正确！");
        public static ErrorInfo PasswordIncorrect = new ErrorInfo("PASSWORD_INCORRECRT", "密码不正确！");
        public static ErrorInfo UserNameOrPasswordIncorrect = new ErrorInfo("USERNAME_OR_PASSWORD_INCORRECRT", "用户名或密码不正确！");

        public static ErrorInfo OldPasswordRequired = new ErrorInfo("OLD_PASSWORD_REQUIRED", "旧密码不能为空！");
        public static ErrorInfo OldPasswordInvalid = new ErrorInfo("OLD_PASSWORD_INVALID", "旧密码格式不正确！");
        public static ErrorInfo OldPasswordIncorrect = new ErrorInfo("OLD_PASSWORD_INCORRECRT", "旧密码不正确！");
        public static ErrorInfo NewPasswordRequired = new ErrorInfo("NEW_PASSWORD_REQUIRED", "新密码不能为空！");
        public static ErrorInfo NewPasswordInvalid = new ErrorInfo("NEW_PASSWORD_INVALID", "新密码格式不正确！");
        public static ErrorInfo ConfirmPasswordRequired = new ErrorInfo("CONFIRM_PASSWORD_REQUIRED", "确认密码不能为空！");
        public static ErrorInfo ConfirmPasswordNotMatch = new ErrorInfo("CONFIRM_PASSWORD_REQUIRED", "确认密码不匹配！");
        public static ErrorInfo ChangePasswordFailure = new ErrorInfo("CHANGE_PASSWORD_FAILURE", "修改密码失败！");

        public static ErrorInfo VerifyCodeRequired = new ErrorInfo("VerifyCodeRequired", "验证码不能为空！");
        public static ErrorInfo VerifyCodeInvalid = new ErrorInfo("VerifyCodeInvalid", "验证码无效！");
        public static ErrorInfo AuthCodeRequired = new ErrorInfo("AuthCodeRequired", "授权码不能为空！");
        public static ErrorInfo AuthCodeInvalid = new ErrorInfo("AuthCodeInvalid", "授权码无效！");

        public static ErrorInfo MinRangeRequired = new ErrorInfo("MIN_RANGE_REQUIRED", "量程起始不能为空！");
        public static ErrorInfo MinRangeInvalid = new ErrorInfo("MIN_RANGE_INVALID", "量程起始格式不正确！");
        public static ErrorInfo MaxRangeRequired = new ErrorInfo("MAX_RANGE_REQUIRED", "量程截止不能为空！");
        public static ErrorInfo MaxRangeInvalid = new ErrorInfo("MAX_RANGE_INVALID", "量程截止格式不正确！");
        public static ErrorInfo MinRangeGreateThanMaxRange = new ErrorInfo("MIN_RANGE_GREATE_THAN_MAX_RANGE", "起始量程不能大于截止量程！");
        public static ErrorInfo RangeValueDIFShouleBeInTimes = new ErrorInfo("MIN_RANGE_GREATE_THAN_MAX_RANGE", "量程差应该为{0}的整数倍！");
        public static ErrorInfo RangeExceed = new ErrorInfo("RANGE_EXCEED", "量程超出范围！");

        public static ErrorInfo ADCMinRangeRequired = new ErrorInfo("ADC_MIN_RANGE_REQUIRED", "数模转换输出模块起始值不能为空！");
        public static ErrorInfo ADCMinRangeInvalid = new ErrorInfo("ADC_MIN_RANGE_INVALID", "数模转换输出模块起始值格式不正确！");
        public static ErrorInfo ADCMaxRangeRequired = new ErrorInfo("ADC_MAX_RANGE_REQUIRED", "数模转换输出模块截止值不能为空！");
        public static ErrorInfo ADCMaxRangeInvalid = new ErrorInfo("ADC_MAX_RANGE_INVALID", "数模转换输出模块截止值格式不正确！");
        public static ErrorInfo ADCMinRangeGreateThanMaxRange = new ErrorInfo("ADC_MIN_RANGE_GREATE_THAN_MAX_RANGE", "数模转换输出模块起始值不能大于截止值！");


        public static ErrorInfo ServerIPRequired = new ErrorInfo("SERVER_IP_REQUIRED", "服务器IP地址不能为空！");
        public static ErrorInfo ServerIPInvalid = new ErrorInfo("SERVER_IP_INVALID", "服务器IP地址格式不正确！");
        public static ErrorInfo ServerPortRequired = new ErrorInfo("SERVER_PROT_REQUIRED", "服务器端口不能为空！");
        public static ErrorInfo ServerPortInvalid = new ErrorInfo("SERVER_PORT_INVALID", "服务器端口格式不正确！");
        public static ErrorInfo TimeoutRequired = new ErrorInfo("TIMEOUT_REQUIRED", "超时时间不能为空！");
        public static ErrorInfo TimeoutInvalid = new ErrorInfo("TIMEOUT_INVALID", "超时时间格式不正确！");

        public static ErrorInfo UpdateFailure = new ErrorInfo("UPDATE_FAILURE", "修改失败！");
        public static ErrorInfo CreateFailure = new ErrorInfo("CREATE_FAILURE", "创建失败！");
        public static ErrorInfo DeleteFailure = new ErrorInfo("DELETE_FAILURE", "删除失败！");
        public static ErrorInfo DataNotInt = new ErrorInfo("DATA_NOT_INT", "数据不是整型类型！");
        public static ErrorInfo DataNotSmallerThanZero = new ErrorInfo("DATA_NOT_SMALLER_THAN_ZERO", "数据不能小于0！");

        public static ErrorInfo RecordNotFound = new ErrorInfo("RECORD_NOT_FOUND", "没有找到相关记录！");
        public static ErrorInfo RecordExistsAlready = new ErrorInfo("RECORD_EXISTS_ALREADY", "该记录已经存在！");
        public static ErrorInfo SensorNotFound = new ErrorInfo("SensorNotFound", "没有找到该传感器！");
        public static ErrorInfo DefaultMonitorViewNotFound = new ErrorInfo("DefaultMonitorViewNotFound", "不能找到系统默认监视设置！");
        public static ErrorInfo MonitorViewNotFound = new ErrorInfo("MonitorViewNotFound", "监视图信息未找到！");
        public static ErrorInfo MonitorViewNameRequired = new ErrorInfo("MonitorViewNameRequired", "监视窗口名称不能为空！");

        public static ErrorInfo TemplateFileNotAllExists = new ErrorInfo("TemplateFileNotAllExists", "工程模板文件不全！");

        public static ErrorInfo MeasureUnitNameRequired = new ErrorInfo("MeasureUnitNameRequired", "单位符号不能为空！");
        public static ErrorInfo MeasureUnitCategoryRequired = new ErrorInfo("MeasureUnitCategoryRequired", "请选择单位类型！");
        public static ErrorInfo MeasureUnitExchangeRelationshipRequired = new ErrorInfo("MeasureUnitExchangeRelationshipRequired", "请选择换算关系！");
        public static ErrorInfo MeasureUnitBaseUnitRequired = new ErrorInfo("MeasureUnitBaseUnitRequired", "请选择基础单位！");
        public static ErrorInfo MeasureUnitExchangeMethodRequired = new ErrorInfo("MeasureUnitExchangeMethodRequired", "请选择换算方法！");
        public static ErrorInfo CustomizeFormulaRequired = new ErrorInfo("MeasureUnitExchangeMethodRequired", "计算公式不能为空！");
        public static ErrorInfo MeasureUnitDefaultRangeRequired = new ErrorInfo("MeasureUnitDefaultRangeRequired", "默认量程不能为空！");
        public static ErrorInfo MeasureUnitDefaultRangeInvalid = new ErrorInfo("MeasureUnitDefaultRangeInvalid", "默认量程格式不正确！");
        public static ErrorInfo MeasureUnitDefaultMinRangeExceed = new ErrorInfo("MeasureUnitDefaultMinRangeExceed", "默认量程最小值不能大于最大值！");
        public static ErrorInfo MeasureUnitDefaultDisplayRangeRequired = new ErrorInfo("MeasureUnitDefaultDisplayRangeRequired", "默认显示范围不能为空！");
        public static ErrorInfo MeasureUnitDefaultDisplayRangeInvalid = new ErrorInfo("MeasureUnitDefaultDisplayRangeInvalid", "默认显示范围格式不正确！");
        public static ErrorInfo MeasureUnitDefaultMinDisplayRangeExceed = new ErrorInfo("MeasureUnitDefaultMinDisplayRangeExceed", "默认显示范围最小值不能大于最大值！");
        public static ErrorInfo MeasureUnitCoefficientRequired = new ErrorInfo("MeasureUnitCoefficientRequired", "系数不能为空！");
        public static ErrorInfo MeasureUnitCoefficientInvalid = new ErrorInfo("MeasureUnitCoefficientRequired", "系数格式不正确！");
        public static ErrorInfo MeasureUnitDefaultIconRequired = new ErrorInfo("MeasureUnitDefaultIconRequired", "请选择默认图标！");
        public static ErrorInfo MeasureUnitDefaultColorRequired = new ErrorInfo("MeasureUnitDefaultColorRequired", "请选择默认颜色！");
        public static ErrorInfo MeasureUnitNameDuplicated = new ErrorInfo("MeasureUnitNameDuplicated", "该单位符号不能重复，请确认！");

        //public static ErrorInfo SensorHardwareCategoryRequired = new ErrorInfo("SensorHardwareCategoryRequired", "请选择适合硬件！");
        public static ErrorInfo SensorModuleTypeRequired = new ErrorInfo("SensorModuleTypeRequired", "请选择模拟类型！");
        public static ErrorInfo SensorNameRequired = new ErrorInfo("SensorNameRequired", "传感器名称不能为空！");
        public static ErrorInfo SensorNameDuplicated = new ErrorInfo("SensorNameDuplicated", "传感器名称不能重复！");
        public static ErrorInfo SensorCategoryRequired = new ErrorInfo("SensorCategoryRequired", "请选择传感器类型！");
        public static ErrorInfo SensorMeasureUnitRequired = new ErrorInfo("SensorMeasureUnitRequired", "请选择计量单位！");
        public static ErrorInfo SensorMinRangeRequired = new ErrorInfo("SensorMinRangeRequired", "最小量程不能为空！");
        public static ErrorInfo SensorMinRangeInvalid = new ErrorInfo("SensorMinRangeInvalid", "最小量程格式不正确！");
        public static ErrorInfo SensorMaxRangeRequired = new ErrorInfo("SensorMaxRangeRequired", "最大量程不能为空！");
        public static ErrorInfo SensorMaxRangeInvalid = new ErrorInfo("SensorMaxRangeInvalid", "最大量程格式不正确！");
        public static ErrorInfo SensorUserGroupRequired = new ErrorInfo("SensorUserGroupRequired", "请选择用户分组！");
        public static ErrorInfo SensorIconRequired = new ErrorInfo("SensorIconRequired", "请选择图标！");
        public static ErrorInfo SensorLineColorRequired = new ErrorInfo("SensorLineColorRequired", "请选择线条颜色！");
        public static ErrorInfo SensorDecimalPrecisonRequired = new ErrorInfo("SensorDecimalPrecisonRequired", "请选择小数位！");


        public static ErrorInfo SensorMaxRangeWarningRequired = new ErrorInfo("SensorMaxRangeWarningRequired", "上限一级报警不能为空！");
        public static ErrorInfo SensorMaxRangeWarningInvalid = new ErrorInfo("SensorMaxRangeWarningInvalid", "上限一级报警格式不正确！");
        public static ErrorInfo SensorMaxRangeWarningExceed = new ErrorInfo("SensorMaxRangeWarningExceed", "上限报警不能超过传感器量程！");
        public static ErrorInfo SensorMinRangeWarningRequired = new ErrorInfo("SensorMinRangeWarningRequired", "下限报警不能为空！");
        public static ErrorInfo SensorMinRangeWarningInvalid = new ErrorInfo("SensorMinRangeWarningInvalid", "下限报警格式不正确！");
        public static ErrorInfo SensorMinRangeWarningExceed = new ErrorInfo("SensorMinRangeWarningExceed", "下限报警不能低于传感器量程！");
        public static ErrorInfo SensorMinRangeWarningGreaterThanMax = new ErrorInfo("SensorMinRangeWarningGreaterThanMax", "下限报警值不能大于上限报警值！");
        public static ErrorInfo SensorMaxRangeTimeRequired = new ErrorInfo("SensorMaxRangeTimeRequired", "上限一级报警时间阀值不能为空！");
        public static ErrorInfo SensorMaxRangeTimeInvalid = new ErrorInfo("SensorMaxRangeTimeInvalid", "上限一级报警时间阀值格式不正确！");
        public static ErrorInfo SensorMaxRangeWarning2Required = new ErrorInfo("SensorMaxRangeWarning2Required", "上限二级报警不能为空！");
        public static ErrorInfo SensorMaxRangeWarning2Invalid = new ErrorInfo("SensorMaxRangeWarning2Invalid", "上限二级报警格式不正确！");
        public static ErrorInfo SensorMaxRangeTime2Required = new ErrorInfo("SensorMaxRangeTime2Required", "上限一级报警时间阀值不能为空！");
        public static ErrorInfo SensorMaxRangeTime2Invalid = new ErrorInfo("SensorMaxRangeTime2Invalid", "上限一级报警时间阀值格式不正确！");
        public static ErrorInfo SensorMaxRangeWarning2Exceed = new ErrorInfo("SensorMaxRangeWarning2Exceed", "上限二级报警不能超过传感器量程！");
        public static ErrorInfo SensorMaxRangeWarning3Required = new ErrorInfo("SensorMaxRangeWarning3Required", "上限三级报警不能为空！");
        public static ErrorInfo SensorMaxRangeWarning3Invalid = new ErrorInfo("SensorMaxRangeWarning3Invalid", "上限三级报警格式不正确！");
        public static ErrorInfo SensorMaxRangeTime3Required = new ErrorInfo("SensorMaxRangeTime3Required", "上限三级报警时间阀值不能为空！");
        public static ErrorInfo SensorMaxRangeTime3Invalid = new ErrorInfo("SensorMaxRangeTime3Invalid", "上限三级报警时间阀值格式不正确！");
        public static ErrorInfo SensorMaxRangeWarning3Exceed = new ErrorInfo("SensorMaxRangeWarning3Exceed", "上限三级报警不能超过传感器量程！");

        public static ErrorInfo SensorMinRangeWarning1Required = new ErrorInfo("SensorMinRangeWarningRequired", "下限一级报警不能为空！");
        public static ErrorInfo SensorMinRangeWarning1Invalid = new ErrorInfo("SensorMinRangeWarningInvalid", "下限一级报警格式不正确！");
        public static ErrorInfo SensorMinRangeTime1Required = new ErrorInfo("SensorMinRangeTimeRequired", "下限一级报警时间阀值不能为空！");
        public static ErrorInfo SensorMinRangeTime1Invalid = new ErrorInfo("SensorMinRangeTimeInvalid", "下限一级报警时间阀值格式不正确！");
        public static ErrorInfo SensorMinRangeWarning1Exceed = new ErrorInfo("SensorMinRangeWarningExceed", "下限一级报警不能低于传感器量程！");
        public static ErrorInfo SensorMinRangeWarning2Required = new ErrorInfo("SensorMinRangeWarning2Required", "下限二级报警不能为空！");
        public static ErrorInfo SensorMinRangeTime2Required = new ErrorInfo("SensorMinRangeTime2Required", "下限二级报警时间阀值不能为空！");
        public static ErrorInfo SensorMinRangeWarning2Invalid = new ErrorInfo("SensorMinRangeWarning2Invalid", "下限二级报警格式不正确！");
        public static ErrorInfo SensorMinRangeTime2Invalid = new ErrorInfo("SensorMinRangeTime2Invalid", "下限二级报警时间阀值格式不正确！");
        public static ErrorInfo SensorMinRangeWarning2Exceed = new ErrorInfo("SensorMinRangeWarning2Exceed", "下限二级报警不能低于传感器量程！");
        public static ErrorInfo SensorMinRangeWarning3Required = new ErrorInfo("SensorMinRangeWarning3Required", "下限三级报警不能为空！");
        public static ErrorInfo SensorMinRangeWarning3Invalid = new ErrorInfo("SensorMinRangeWarning3Invalid", "下限三级报警格式不正确！");
        public static ErrorInfo SensorMinRangeTime3Required = new ErrorInfo("SensorMinRangeTime3Required", "下限三级报警时间阀值不能为空！");
        public static ErrorInfo SensorMinRangeTime3Invalid = new ErrorInfo("SensorMinRangeTime3Invalid", "下限三级报警时间阀值格式不正确！");
        public static ErrorInfo SensorMinRangeWarning3Exceed = new ErrorInfo("SensorMinRangeWarning3Exceed", "下限三级报警不能低于传感器量程！");
        public static ErrorInfo SensorMinRangeWarningGreaterThanOrEqualMax = new ErrorInfo("SensorMinRangeWarningGreaterThanOrEqualMax", "下限报警值不能大于或等于上限报警值！");

        public static ErrorInfo SensorErrorWarningRequired = new ErrorInfo("SensorErrorWarningRequired", "故障报警不能为空！");
        public static ErrorInfo SensorErrorWarningInvalid = new ErrorInfo("SensorErrorWarningInvalid", "故障报警格式不正确！");
        public static ErrorInfo ModuleSNRequired = new ErrorInfo("ModuleSNRequired", "模块编号不能为空！");
        public static ErrorInfo SensorCorrectReadValueRequired = new ErrorInfo("SensorCorrectReadValueRequired", "传感器标定读数{0}不能为空！");
        public static ErrorInfo SensorCorrectReadValueInvalid = new ErrorInfo("SensorCorrectReadValueInvalid", "传感器标定读数{0}格式不正确！");
        public static ErrorInfo SensorCorrectActualValueRequired = new ErrorInfo("SensorCorrectActualValueRequired", "传感器标定读数{0}对应的实际值不能为空！");
        public static ErrorInfo SensorCorrectActualValueInvalid = new ErrorInfo("SensorCorrectActualValueInvalid", "传感器标定读数{0}对应的实际值格式不正确！");
        public static ErrorInfo SensorDevIndexIDRequired = new ErrorInfo("SensorDevIndexIDRequired", "多值索引不能为空！");
        public static ErrorInfo SensorDevIndexIDInvalid = new ErrorInfo("SensorDevIndexIDInvalid", "多值索引格式不正确！");


        public static ErrorInfo ModBusDeviceNameDuplicated = new ErrorInfo("ModBusDeviceNameDuplicated", "设备名称不能重复！");

        public static ErrorInfo UserNameDuplicated = new ErrorInfo("UserNameDuplicated", "用户名不能重复，请确认！");
        public static ErrorInfo UserNameExistAlready = new ErrorInfo("UserNameExistAlready", "该用户名已经存在，请确认！");
        public static ErrorInfo UserNameNotExist = new ErrorInfo("UserNameNotExist", "该用户名不存在，请确认！");

    }
}
