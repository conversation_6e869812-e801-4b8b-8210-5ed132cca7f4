using SDHD.DC.DataMonitor.Resources.Localization;
using SDHD.DC.DataMonitor.Utils;
using SDHD.DC.DataMonitor.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SDHD.DC.DataMonitor.Models.Base
{
    /// <summary>
    /// 国际化ViewModel基类，提供本地化字符串获取功能
    /// </summary>
    public abstract class InternationalizationViewModelBase : NotifyPropertyChangedModel
    {
        /// <summary>
        /// 获取本地化字符串
        /// </summary>
        /// <param name="key">资源键</param>
        /// <returns>本地化字符串</returns>
        protected string GetLocalizedString(string key)
        {
            return ResourceHelper.GetString(key);
        }

        /// <summary>
        /// 刷新本地化内容，子类可以重写此方法来刷新特定的本地化属性
        /// </summary>
        public virtual void RefreshLocalization()
        {
            // 基类默认实现为空，子类可以重写
        }
    }
}
