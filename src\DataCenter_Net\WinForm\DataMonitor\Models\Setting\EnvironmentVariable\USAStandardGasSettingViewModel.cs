﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Media;

namespace SDHD.DC.DataMonitor.Models.Setting.EnvironmentVariable
{
    internal class USAStandardGasSettingViewModel : NotifyPropertyChangedModel
    {
        public string WellStationId { set; get; }

        public string CurrentWorkflowType { set; get; }

        private USAStandardGasInfo _Record = new USAStandardGasInfo();
        public USAStandardGasInfo Record
        {
            get { return _Record; }
            set
            {
                _Record = value;
                OnPropertyChanged(nameof(Record));
            }
        }
        public List<SelectItem> WorkflowTypes { set; get; } = new List<SelectItem>();
        public List<USAStandardGasInfo> USAStandardGases { set; get; } = new List<USAStandardGasInfo>();

        public class USAStandardGasInfo : NotifyPropertyChangedModel
        {
            private string _WorkflowType;
            public string WorkflowType
            {
                get { return _WorkflowType; }
                set
                {
                    if (value == _WorkflowType) return;
                    _WorkflowType = value;
                    OnPropertyChanged(nameof(WorkflowType));
                }
            }

            private string _SpecificGravity;
            public string SpecificGravity
            {
                get { return _SpecificGravity; }
                set
                {
                    if (value == _SpecificGravity) return;
                    _SpecificGravity = value;
                    OnPropertyChanged(nameof(SpecificGravity));
                }
            }

            private string _PipelineRunDiameter;
            public string PipelineRunDiameter
            {
                get { return _PipelineRunDiameter; }
                set
                {
                    if (value == _PipelineRunDiameter) return;
                    _PipelineRunDiameter = value;
                    OnPropertyChanged(nameof(PipelineRunDiameter));
                }
            }

            private string _OrificeRunDiameter;
            public string OrificeRunDiameter
            {
                get { return _OrificeRunDiameter; }
                set
                {
                    if (value == _OrificeRunDiameter) return;
                    _OrificeRunDiameter = value;
                    OnPropertyChanged(nameof(OrificeRunDiameter));
                }
            }

            private string _DegFTgr;
            public string DegFTgr
            {
                get { return _DegFTgr; }
                set
                {
                    if (value == _DegFTgr) return;
                    _DegFTgr = value;
                    OnPropertyChanged(nameof(DegFTgr));
                }
            }

            private string _PsiaPgr;
            public string PsiaPgr
            {
                get { return _PsiaPgr; }
                set
                {
                    if (value == _PsiaPgr) return;
                    _PsiaPgr = value;
                    OnPropertyChanged(nameof(PsiaPgr));
                }
            }

            private string _DegFTb;
            public string DegFTb
            {
                get { return _DegFTb; }
                set
                {
                    if (value == _DegFTb) return;
                    _DegFTb = value;
                    OnPropertyChanged(nameof(DegFTb));
                }
            }

            private string _PsiaPb;
            public string PsiaPb
            {
                get { return _PsiaPb; }
                set
                {
                    if (value == _PsiaPb) return;
                    _PsiaPb = value;
                    OnPropertyChanged(nameof(PsiaPb));
                }
            }

            private string _AtmosphericPressure;
            public string AtmosphericPressure
            {
                get { return _AtmosphericPressure; }
                set
                {
                    if (value == _AtmosphericPressure) return;
                    _AtmosphericPressure = value;
                    OnPropertyChanged(nameof(AtmosphericPressure));
                }
            }

            private string _IsentropicEmxponent;
            public string IsentropicEmxponent
            {
                get { return _IsentropicEmxponent; }
                set
                {
                    if (value == _IsentropicEmxponent) return;
                    _IsentropicEmxponent = value;
                    OnPropertyChanged(nameof(IsentropicEmxponent));
                }
            }

            private string _CompressibilityOfAirStdCond;
            public string CompressibilityOfAirStdCond
            {
                get { return _CompressibilityOfAirStdCond; }
                set
                {
                    if (value == _CompressibilityOfAirStdCond) return;
                    _CompressibilityOfAirStdCond = value;
                    OnPropertyChanged(nameof(CompressibilityOfAirStdCond));
                }
            }

            private string _OrificePlateThermalExpansionCoef;
            public string OrificePlateThermalExpansionCoef
            {
                get { return _OrificePlateThermalExpansionCoef; }
                set
                {
                    if (value == _OrificePlateThermalExpansionCoef) return;
                    _OrificePlateThermalExpansionCoef = value;
                    OnPropertyChanged(nameof(OrificePlateThermalExpansionCoef));
                }
            }

            private string _RunThermalExpansionCoef;
            public string RunThermalExpansionCoef
            {
                get { return _RunThermalExpansionCoef; }
                set
                {
                    if (value == _RunThermalExpansionCoef) return;
                    _RunThermalExpansionCoef = value;
                    OnPropertyChanged(nameof(RunThermalExpansionCoef));
                }
            }

            private string _DynamicViscosity;
            public string DynamicViscosity
            {
                get { return _DynamicViscosity; }
                set
                {
                    if (value == _DynamicViscosity) return;
                    _DynamicViscosity = value;
                    OnPropertyChanged(nameof(DynamicViscosity));
                }
            }
        }
    }
}
