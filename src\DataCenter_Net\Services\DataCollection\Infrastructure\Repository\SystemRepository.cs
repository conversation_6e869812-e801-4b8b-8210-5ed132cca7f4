﻿using NPOI.POIFS.Storage;
using PBEntities;
using ProjectStruct;
using SDHD.DC.DataCollection.ApplicationCore.Domain.Entities;
using SDHD.DC.DataCollection.ApplicationCore.Interfaces.Repository;
using SDHD.DC.DataCollection.Infrastructure.Mapping;
using SDHD.DC.Utilities;
using SDHD.DC.Utilities.Constants;
using SDHD.DC.Utilities.DataStore;
using SDHD.DC.Utilities.Net;
using SDHD.DC.Utilities.ServiceExtensions;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using static ProjectStruct.SysBase.Types;
using static ProjectStruct.SysBase.Types.deviceBase.Types.dataInfo.Types;
using Rank = SDHD.DC.DataCollection.ApplicationCore.Domain.Entities.Rank;
using UploadConfig = SDHD.DC.DataCollection.ApplicationCore.Domain.Entities.UploadConfig;

namespace SDHD.DC.DataCollection.Infrastructure.Repository
{
    public class SystemRepository : BaseRepository, ISystemRepository
    {
        private ITCPPbStore _TCPPbStore;
        public SystemRepository()
        {
            _TCPPbStore = ServiceHelper.GetService<ITCPPbStore>();
        }
        public Task<bool> TestServerConnection(string serverIP, int port, int connectionTimeout, out string errorMsg)
        {
            errorMsg = null;
            if (TCPClientHelper.TryConnectTest(serverIP, port, connectionTimeout, out string error))
            {
                return Task.FromResult(true);
            }
            errorMsg = error;
            return Task.FromResult(false);
        }
        public Task CloseServerConnection()
        {
            _TCPPbStore?.Close();
            return Task.CompletedTask;
        }
        private async Task<PBEntities.SysConfig> GetSysConfigDataEntity()
        {
            var dataEntity = await GetDataFromPb<PBEntities.SysConfig>(StoreFiles.RegisterData, null);
            return dataEntity ?? new PBEntities.SysConfig();
        }
        private async Task<bool> SaveSysConfigDataEntity(PBEntities.SysConfig dataEntity)
        {
            return await SaveDataAsPb(StoreFiles.RegisterData, dataEntity, null);
        }
        public async Task<ApplicationCore.Domain.Entities.SysConfig> GetSysConfig()
        {
            ApplicationCore.Domain.Entities.SysConfig result = new ApplicationCore.Domain.Entities.SysConfig();
            var dataEntity = await GetSysConfigDataEntity();
            if (dataEntity != null)
            {
                result = dataEntity.ToDomainEntity(result);
            }
            return result;
        }
        public async Task<bool> UpdateSysConfig(RegisterData sysConfig)
        {
            //var itmes = await GetRegisterData();
            //if (itmes == null)
            //{
            //    itmes = new RegisterData();
            //}
            var entity = new SysBase.Types.sysInfo
            {
                User = sysConfig.UserName,
                RegStr = sysConfig.AuthCode,
                MachineCode = sysConfig.machineCode,
                LastPro = sysConfig.lastPro
            };
            //itmes = sysConfig.ToDataEntity(itmes);
            await ExecuteUpdateMessage<SysBase.Types.sysInfo>(-1, entity);
            //await SaveSysConfigDataEntity(itmes);
            return true;
        }

        public async Task<RegisterData> GetRegisterData()
        {
            var entity = GlobalVariables.sysBase.SysInfoField;

            if (entity == null || string.IsNullOrEmpty(entity.User) || string.IsNullOrEmpty(entity.RegStr) || string.IsNullOrEmpty(entity.MachineCode))
                entity = await ExecuteGetAllMessage<SysBase.Types.sysInfo>();

            var dataEntity = new RegisterData
            {
                UserName = entity.User,
                AuthCode = entity.RegStr,
                machineCode = entity.MachineCode,
                lastPro = entity.LastPro
            };
            return dataEntity;
            //RegisterData result = null;
            //var itmes = await GetSysConfigDataEntity();
            //if (itmes != null)
            //{
            //    result = new RegisterData();
            //    result.UserName = itmes.UnitName;
            //    result.AuthCode = itmes.AuthCode;
            //}
            //return result;
        }
        public async Task<bool> SaveRegisterData(RegisterData entity)
        {
            var dataEntity = new SysBase.Types.sysInfo
            {
                User = entity.UserName,
                RegStr = entity.AuthCode
            };
            return await ExecuteAddMessage(dataEntity);
        }
        //public async Task<bool> SaveRegisterData(RegisterData entity)
        //{
        //    var itmes = await GetSysConfigDataEntity();
        //    if (itmes == null)
        //    {
        //        itmes = new PBEntities.SysConfig();
        //    }
        //    itmes.UnitName = entity.UserName;
        //    itmes.AuthCode = entity.AuthCode;
        //    await SaveSysConfigDataEntity(itmes);
        //    return true;
        //}
        public async Task<ApplicationCore.Domain.Entities.SysExternal> GetSysExternal()
        {
            var dataEntity = await GetDataFromPb<PBEntities.SysExternal>(StoreFiles.SysExternal, null);
            if (dataEntity == null)
            {
                dataEntity = new PBEntities.SysExternal();
            }
            var result = new ApplicationCore.Domain.Entities.SysExternal();
            result = dataEntity.ToDomainEntity(result);
            return result;
        }
        public async Task<bool> UpdateSysExternal(ApplicationCore.Domain.Entities.SysExternal entity)
        {
            var dataEntity = await GetDataFromPb<PBEntities.SysExternal>(StoreFiles.SysExternal, null);
            if (dataEntity == null)
            {
                dataEntity = new PBEntities.SysExternal();
            }
            dataEntity = entity.ToDataEntity(dataEntity);
            await SaveDataAsPb(StoreFiles.SysExternal, dataEntity, null);
            return true;
        }

        #region 变更代码 Ahri

        /// <summary>
        /// 发送异步消息
        /// </summary>
        public void GetRegisterDataay()
        {
            ExecuteGetAllMessageay<SysBase.Types.sysInfo>();
        }
        public void GetNationalStandardGasProductionParametersay()
        {
            ExecuteGetAllMessageay<NationalStandardGasProductionParameters>();
        }
        public async Task GetNationalStandardGasProductionParametersData()
        {
            GlobalVariables.sysBase.NationalStandardGasProductionParametersField ??= await ExecuteGetAllMessage<NationalStandardGasProductionParameters>();
        }
        public List<GasParameterData> GetNationalStandardGasProductionParameters()
        {
            var result = new List<GasParameterData>();
            if (GlobalVariables.sysBase.NationalStandardGasProductionParametersField != null)
            {
                var itmes = GlobalVariables.sysBase.NationalStandardGasProductionParametersField.Items;

                foreach (var item in itmes)
                {
                    GasParameterData gas = new()
                    {
                        Id = item.Id,
                        GasSG = item.GasSG,
                        AtmosphericPressure = item.AtmosphericPressure,
                        CO2MoleFraction = item.CO2,
                        N2MoleFraction = item.N2,
                        PipeDiameter = item.PipeDiameter,
                        OrificePlateDiameter = item.OrificePlateDiameter,
                        GDstuff = item.GDstuff,
                        KBstuff = item.KBstuff
                    };
                    result.Add(gas);
                }
            }

            return result;
        }

        public List<GasParameterMBData> GetNationalStandardGasProductionParametersMB()
        {
            var result = new List<GasParameterMBData>();
            if (GlobalVariables.sysBase.NationalStandardGasProductionParametersField != null)
            {
                var items = GlobalVariables.sysBase.NationalStandardGasProductionParametersField.MBitems;

                foreach (var item in items)
                {
                    GasParameterMBData gas = new()
                    {
                        Id = item.Id,
                        DegFTb = item.DegFTb,
                        PsiaPB = item.PsiaPB,
                        DegFTgr = item.DegFTgr,
                        PsiaPgr = item.PsiaPgr,
                        SpecificGravity = item.SpecificGravity,
                        DynamicViscosity = item.DynamicViscosity,
                        InOrificeDiameter = item.InOrificeDiameter,
                        InMeterRunDiameter = item.InMeterRunDiameter,
                        AtmosphericPressure = item.AtmosphericPressure,
                        InsentropicExponent = item.InsentropicExponent,
                        MeterRunThermalExpansionCofe = item.MeterRunThermalExpansionCofe,
                        CompressibilityOfAirAtStdCond = item.CompressibilityOfAirAtStdCond,
                        OrificePlateThermalExpansionCofe = item.OrificePlateThermalExpansionCofe,
                    };
                    result.Add(gas);
                }
            }

            return result;
        }

        public List<GasParameterSandData> GetNationalStandardGasProductionParametersSand()
        {
            var result = new List<GasParameterSandData>();
            if (GlobalVariables.sysBase.NationalStandardGasProductionParametersField != null)
            {
                var items = GlobalVariables.sysBase.NationalStandardGasProductionParametersField.SandItems;

                foreach (var item in items)
                {
                    GasParameterSandData gas = new()
                    {
                        Id = item.Id,
                        YSXS = item.YSXS,
                        GDNJ = item.GDNJ,
                        BJZY = item.BJZY,
                        TSPAN = item.TSPAN,
                        AUTOGDJZ = item.AUTOGDJZ,
                        AUTOBJZY = item.AUTOBJZY,
                        CheckRang = item.CheckRang,
                        GDJZ = (SandJZ)(int)item.GDJZ,
                    };
                    result.Add(gas);
                }
            }

            return result;
        }
        public bool SetNationalStandardGasProductionParameters(List<GasParameterData> gasParameterDatas)
        {
            var entity = GlobalVariables.sysBase.NationalStandardGasProductionParametersField;
            entity.Items.Clear();
            foreach (var item in gasParameterDatas)
            {
                var dataEntity = new NationalStandardGasProductionParameters.Types.item
                {
                    Id = item.Id,
                    GasSG = item.GasSG,
                    GDstuff = item.GDstuff,
                    KBstuff = item.KBstuff,
                    N2 = item.N2MoleFraction,
                    CO2 = item.CO2MoleFraction,
                    PipeDiameter = item.PipeDiameter,
                    AtmosphericPressure = item.AtmosphericPressure,
                    OrificePlateDiameter = item.OrificePlateDiameter,
                };
                entity.Items.Add(dataEntity);
            }
            return ExecuteUpdateMessage(-1, entity).Result;

        }

        public bool SetNationalStandardGasProductionParametersMB(List<GasParameterMBData> gasParameterDatas)
        {
            var entity = GlobalVariables.sysBase.NationalStandardGasProductionParametersField;
            entity.MBitems.Clear();
            foreach (var item in gasParameterDatas)
            {
                var dataEntity = new NationalStandardGasProductionParameters.Types.MBitem
                {
                    Id = item.Id,
                    DegFTb = item.DegFTb,
                    PsiaPB = item.PsiaPB,
                    DegFTgr = item.DegFTgr,
                    PsiaPgr = item.PsiaPgr,
                    SpecificGravity = item.SpecificGravity,
                    DynamicViscosity = item.DynamicViscosity,
                    InOrificeDiameter = item.InOrificeDiameter,
                    InMeterRunDiameter = item.InMeterRunDiameter,
                    AtmosphericPressure = item.AtmosphericPressure,
                    InsentropicExponent = item.InsentropicExponent,
                    MeterRunThermalExpansionCofe = item.MeterRunThermalExpansionCofe,
                    CompressibilityOfAirAtStdCond = item.CompressibilityOfAirAtStdCond,
                    OrificePlateThermalExpansionCofe = item.OrificePlateThermalExpansionCofe,
                };
                entity.MBitems.Add(dataEntity);
            }
            return ExecuteUpdateMessage(-1, entity).Result;

        }

        public bool SetNationalStandardGasProductionParametersSand(List<GasParameterSandData> gasParameterDatas)
        {
            var entity = GlobalVariables.sysBase.NationalStandardGasProductionParametersField;
            entity.SandItems.Clear();
            foreach (var item in gasParameterDatas)
            {
                var dataEntity = new NationalStandardGasProductionParameters.Types.sandItem
                {
                    Id = item.Id,
                    YSXS = item.YSXS,
                    GDNJ = item.GDNJ,
                    BJZY = item.BJZY,
                    TSPAN = item.TSPAN,
                    AUTOGDJZ = item.AUTOGDJZ,
                    AUTOBJZY = item.AUTOBJZY,
                    CheckRang = item.CheckRang,
                    GDJZ = (NationalStandardGasProductionParameters.Types.sandJZ)(int)item.GDJZ,
                };
                entity.SandItems.Add(dataEntity);
            }
            return ExecuteUpdateMessage(-1, entity).Result;
        }

        public bool SetUploadConfig(UploadConfig uploadConfig)
        {
            if (uploadConfig == null) return false;
            var entity = uploadConfig.ToDataEntity(new ProBase.Types.UploadConfig());
            GlobalVariables.sysBase.ProBaseField.UploadConfigField = entity;
            return ExecuteUpdateMessage(-1, entity).Result;
        }

        public bool SetUploadConfigZSH(UploadConfigZSH uploadConfigZSH)
        {
            if (uploadConfigZSH == null) return false;
            var entity = GlobalVariables.sysBase.ProBaseField.UploadConfigField.ToDomainEntity(new UploadConfig());
            var _tempZSH = entity.ZSHList.SingleOrDefault(d => d.ProcessNo.Equals(uploadConfigZSH.ProcessNo));
            if (_tempZSH == null) return false;
            int index = entity.ZSHList.IndexOf(_tempZSH);
            entity.ZSHList.RemoveAt(index);
            entity.ZSHList.Insert(index, uploadConfigZSH);
            return SetUploadConfig(entity);
        }
        public async Task<UploadConfig> GetUploadConfig(bool reload)
        {
            if (reload) GlobalVariables.sysBase.ProBaseField.UploadConfigField = null;
            GlobalVariables.sysBase.ProBaseField.UploadConfigField ??= await ExecuteGetAllMessage<SysBase.Types.ProBase.Types.UploadConfig>();
            return GlobalVariables.sysBase.ProBaseField.UploadConfigField.ToDomainEntity(new UploadConfig());
        }


        public async Task<List<Rank>> GetRanks()
        {
            List<Rank> ranks = GetDefaultRanks();
            var ranklist = await ExecuteGetAllMessage<SysBase.Types.ranklist>();
            if (ranklist != null && ranklist.Ranks != null && ranklist.Ranks.Count > 0)
                ranks = ranklist.Ranks.Select(d => d.ToDomainEntity()).ToList();
            return ranks;
        }

        public async Task<bool> UpdateRanks(List<Rank> ranks)
        {
            SysBase.Types.ranklist ranklist = new();
            ranklist.Ranks.AddRange(ranks.Select(d => d.ToDataEntity()).ToList());
            return await ExecuteUpdateMessage(-1, ranklist);
        }

        private List<Rank> GetDefaultRanks()
        {
            List<Rank> ranks = new();
            var meuns = GetDefaultMenus();
            ranks.Add(new() { Level = 1, Name = "管理员", Menus = meuns });
            ranks.Add(new() { Level = 2, Name = "班长", Menus = meuns });
            List<string> excludeMenus = new() { "004|004", "004|008", "004|009", "004|010", "006|001", };
            ranks.Add(new() { Level = 3, Name = "操作员", Menus = meuns.Where(d => !excludeMenus.Contains(d.Path)).ToList() });
            return ranks;
        }


        public List<Menu> GetDefaultMenus(int depth = 1)
        {
            return new List<Menu>()
            {
            new("井场","001","001"),
            new("新建井场","001","001|001"),
            new("打开历史井场","002","001|002"),
            new("刷新历史井场","003","001|003"),

            new("当前井场","002","002"),
            new("开始采集","001","002|001"),
            new("停止采集","002","002|002"),
            new("新建图形监视","003","002|003"),
            new("打开图形监视","004","002|004"),
            new("关闭并删除当前监视图","005","002|005"),
            new("三维流程图","006","002|006"),

            new("导出","003","003"),
            new("历史数据查询及导出","001","003|001"),
            new("设置","004","004"),
            new("传感器设置","001","004|001"),
            new("井场设置","002","004|002"),
            new("计量单位设置","003","004|003"),
            new("TCP网络设备设置","004","004|004"),
            //new("Redis服务设置","005","004|005"),
            //new("通用ModBus设备设置","006","004|006"),
            //new("ModBusTCP数据设置","007","004|007"),
            new("国标气产量参数设置","008","004|008"),
            new("美标气产量参数设置","009","004|009"),
            new("砂量参数设置","010","004|010"),
            new("窗口","005","005"),
            new("纵向排列窗口","001","005|001"),
            new("横向排列窗口","002","005|002"),
            new("关闭所有窗口","003","005|003"),
            new("设置为游离窗口","004","005|004"),
            new("配置Web窗口","005","005|005"),
            new("管理","006","006"),
            new("用户管理","001","006|001"),
            new("报警","007","007"),
            new("报警设置","001","007|001"),
            new("报警信息","002","007|002"),
            new("报警日志","003","007|003"),
            }.Where(d => d.Depth > depth).ToList();
        }


        public async Task<List<string>> GetWebUrlConfig()
        {
            var listDataEntities = await GetDataFromJson<Entities.WebUrlConfig>(StoreFiles.WebUrlConfig, null);
            return listDataEntities?.Urls;
        }

        public async Task<bool> SaveWebUrlConfig(List<string> urls)
        {
            return await SaveDataAsJson<Entities.WebUrlConfig>(StoreFiles.WebUrlConfig, new() { Urls = urls }, null);
        }

        #endregion 变更代码 Ahri
    }
}
