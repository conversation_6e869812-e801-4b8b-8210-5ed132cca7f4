using SDHD.DC.Utilities;
using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Threading;

namespace SDHD.DC.DataMonitor.Views.SplashScreen
{
    /// <summary>
    /// SplashScreenWindow.xaml 的交互逻辑
    /// </summary>
    public partial class SplashScreenWindow : Window
    {
        private readonly SplashScreenViewModel _viewModel;
        private readonly DispatcherTimer _minimumDisplayTimer;
        private bool _canClose = false;
        private bool _mainWindowReady = false;

        public SplashScreenWindow()
        {
            InitializeComponent();
            
            _viewModel = new SplashScreenViewModel();
            DataContext = _viewModel;

            // 设置版本信息
            _viewModel.VersionText = $"v{GetProductVersion()}";
            
            // 从配置获取产品信息
            try
            {
                if (Config.Instance != null)
                {
                    _viewModel.ProductName = Config.Instance.ProductName ?? "油气田智能一体化系统";
                    _viewModel.ProductSubName = Config.Instance.ProductShortName ?? "数据采集系统";
                }
            }
            catch
            {
                // 使用默认值
            }

            // 设置最小显示时间（2秒），确保用户能看到启动界面
            _minimumDisplayTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(2)
            };
            _minimumDisplayTimer.Tick += (s, e) =>
            {
                _minimumDisplayTimer.Stop();
                _canClose = true;
                CheckAndClose();
            };

            // 订阅进度更新事件
            SplashProgressReporter.ProgressChanged += OnProgressChanged;
            
            Loaded += OnLoaded;
            Closing += OnClosing;
        }

        private void OnLoaded(object sender, RoutedEventArgs e)
        {
            // 启动最小显示计时器
            _minimumDisplayTimer.Start();
            
            // 开始初始化
            _viewModel.UpdateProgress(0, "正在启动应用程序...");
        }

        private void OnClosing(object sender, System.ComponentModel.CancelEventArgs e)
        {
            // 取消订阅事件
            SplashProgressReporter.ProgressChanged -= OnProgressChanged;
            _minimumDisplayTimer?.Stop();
        }

        private void OnProgressChanged(object sender, SplashProgressEventArgs e)
        {
            // 确保在UI线程中更新
            Dispatcher.BeginInvoke(() =>
            {
                _viewModel.UpdateProgress(e.Progress, e.Status);
                
                // 如果进度达到100%，标记主窗口准备就绪
                if (e.Progress >= 100)
                {
                    _mainWindowReady = true;
                    CheckAndClose();
                }
            });
        }

        /// <summary>
        /// 检查是否可以关闭启动界面
        /// </summary>
        private void CheckAndClose()
        {
            if (_canClose && _mainWindowReady)
            {
                // 延迟一点时间让用户看到100%的进度
                Task.Delay(500).ContinueWith(_ =>
                {
                    Dispatcher.BeginInvoke(() =>
                    {
                        Close();
                    });
                });
            }
        }

        /// <summary>
        /// 显示错误信息
        /// </summary>
        /// <param name="errorMessage">错误消息</param>
        public void ShowError(string errorMessage)
        {
            Dispatcher.BeginInvoke(() =>
            {
                _viewModel.ShowError(errorMessage);
                _minimumDisplayTimer.Stop();
                _canClose = true;
            });
        }

        /// <summary>
        /// 强制关闭启动界面
        /// </summary>
        public void ForceClose()
        {
            Dispatcher.BeginInvoke(() =>
            {
                _canClose = true;
                _mainWindowReady = true;
                Close();
            });
        }

        /// <summary>
        /// 获取产品版本号
        /// </summary>
        /// <returns>版本号字符串</returns>
        private string GetProductVersion()
        {
            try
            {
                var assembly = System.Reflection.Assembly.GetExecutingAssembly();
                var version = assembly.GetName().Version;
                return $"{version.Major}.{version.Minor}.{version.Build}";
            }
            catch
            {
                return "1.0.0";
            }
        }

        /// <summary>
        /// 静态方法：显示启动界面
        /// </summary>
        /// <returns>启动界面实例</returns>
        public static SplashScreenWindow ShowSplashScreen()
        {
            var splashScreen = new SplashScreenWindow();
            splashScreen.Show();
            return splashScreen;
        }

        /// <summary>
        /// 静态方法：更新进度（线程安全）
        /// </summary>
        /// <param name="progress">进度值 (0-100)</param>
        /// <param name="status">状态文本</param>
        public static void UpdateProgress(double progress, string status)
        {
            SplashProgressReporter.ReportProgress(progress, status);
        }

        /// <summary>
        /// 静态方法：标记主窗口加载完成
        /// </summary>
        public static void MarkMainWindowReady()
        {
            SplashProgressReporter.ReportProgress(100, "启动完成");
        }
    }
}
