using SDHD.DC.DataMonitor.Models;
using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows;

namespace SDHD.DC.DataMonitor.Views.SplashScreen
{
    /// <summary>
    /// 启动界面的ViewModel，负责进度和状态的数据绑定
    /// </summary>
    public class SplashScreenViewModel : INotifyPropertyChanged
    {
        private double _progress = 0;
        private string _statusText = "正在初始化...";
        private string _versionText = "v1.0.0";
        private string _productName = "油气田智能一体化系统";
        private string _productSubName = "数据采集系统";
        private Visibility _errorVisibility = Visibility.Collapsed;
        private string _errorMessage = "";

        /// <summary>
        /// 当前进度 (0-100)
        /// </summary>
        public double Progress
        {
            get => _progress;
            set
            {
                if (_progress != value)
                {
                    _progress = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(ProgressText));
                }
            }
        }

        /// <summary>
        /// 进度百分比文本
        /// </summary>
        public string ProgressText => $"{_progress:F0}%";

        /// <summary>
        /// 当前状态文本
        /// </summary>
        public string StatusText
        {
            get => _statusText;
            set
            {
                if (_statusText != value)
                {
                    _statusText = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 版本信息
        /// </summary>
        public string VersionText
        {
            get => _versionText;
            set
            {
                if (_versionText != value)
                {
                    _versionText = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 产品名称
        /// </summary>
        public string ProductName
        {
            get => _productName;
            set
            {
                if (_productName != value)
                {
                    _productName = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 产品副标题
        /// </summary>
        public string ProductSubName
        {
            get => _productSubName;
            set
            {
                if (_productSubName != value)
                {
                    _productSubName = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 错误信息可见性
        /// </summary>
        public Visibility ErrorVisibility
        {
            get => _errorVisibility;
            set
            {
                if (_errorVisibility != value)
                {
                    _errorVisibility = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage
        {
            get => _errorMessage;
            set
            {
                if (_errorMessage != value)
                {
                    _errorMessage = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 更新进度和状态
        /// </summary>
        /// <param name="progress">进度值 (0-100)</param>
        /// <param name="status">状态文本</param>
        public void UpdateProgress(double progress, string status)
        {
            Progress = Math.Max(0, Math.Min(100, progress));
            StatusText = status ?? "正在加载...";
        }

        /// <summary>
        /// 显示错误信息
        /// </summary>
        /// <param name="errorMessage">错误消息</param>
        public void ShowError(string errorMessage)
        {
            ErrorMessage = errorMessage;
            ErrorVisibility = Visibility.Visible;
            StatusText = "加载失败";
        }

        /// <summary>
        /// 隐藏错误信息
        /// </summary>
        public void HideError()
        {
            ErrorVisibility = Visibility.Collapsed;
            ErrorMessage = "";
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// <summary>
    /// 启动进度报告事件参数
    /// </summary>
    public class SplashProgressEventArgs : EventArgs
    {
        public double Progress { get; }
        public string Status { get; }

        public SplashProgressEventArgs(double progress, string status)
        {
            Progress = progress;
            Status = status;
        }
    }

    /// <summary>
    /// 启动进度报告器
    /// </summary>
    public static class SplashProgressReporter
    {
        public static event EventHandler<SplashProgressEventArgs> ProgressChanged;

        /// <summary>
        /// 报告进度
        /// </summary>
        /// <param name="progress">进度 (0-100)</param>
        /// <param name="status">状态文本</param>
        public static void ReportProgress(double progress, string status)
        {
            ProgressChanged?.Invoke(null, new SplashProgressEventArgs(progress, status));
        }
    }
}
