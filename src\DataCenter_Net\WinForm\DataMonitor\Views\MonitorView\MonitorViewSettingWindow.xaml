﻿<Window x:Class="SDHD.DC.DataMonitor.Views.MonitorView.MonitorViewSettingWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:SDHD.DC.DataMonitor.Views.MonitorView"
        xmlns:uc="clr-namespace:SDHD.DC.DataMonitor.UserControls"
        xmlns:loc="clr-namespace:SDHD.DC.DataMonitor.Resources.Localization"
        mc:Ignorable="d"
        Icon="/assets/images/default.png"
        Title="{loc:LocalizationExtension MonitorViewSettingWindowTitle}" Height="520" Width="460" ResizeMode="NoResize" Style="{StaticResource chromeWindow}">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <uc:UcWindowHeader Grid.Row="0" Title="{loc:LocalizationExtension MonitorViewSettingWindowTitle}"/>

        <Grid Grid.Row="1" Margin="15,10">
            <StackPanel>
                <!-- 基本设置 -->
                <GroupBox Header="{loc:LocalizationExtension LabelBasicSettings}" Margin="0,0,0,8">
                    <StackPanel Margin="8,5">
                        <DockPanel Margin="0,0,0,10">
                            <TextBlock Text="{loc:LocalizationExtension LabelMonitorSchemeName}" Width="120" VerticalAlignment="Center"/>
                            <TextBox MaxLength="100" Text="{Binding Path=MonitorName}" Height="28"/>
                        </DockPanel>

                        <UniformGrid Columns="2" Margin="0,0,0,0">
                            <DockPanel Margin="0,0,10,10">
                                <TextBlock Text="{loc:LocalizationExtension LabelCurveWidth}" Width="80" VerticalAlignment="Center"/>
                                <TextBox Width="60" MaxLength="3" Text="{Binding LineWidth}"
                                        Height="28" HorizontalAlignment="Left"/>
                            </DockPanel>

                            <DockPanel Margin="0,0,0,10">
                                <TextBlock Text="{loc:LocalizationExtension LabelSmallGridCount}" Width="80" VerticalAlignment="Center"/>
                                <TextBox Width="60" MaxLength="2" Text="{Binding YScaleCount}"
                                        Height="28" HorizontalAlignment="Left"/>
                            </DockPanel>

                            <DockPanel Margin="0,0,10,0">
                                <TextBlock Text="{loc:LocalizationExtension LabelBigGridCount}" Width="80" VerticalAlignment="Center"/>
                                <TextBox Width="60" MaxLength="2" Text="{Binding XScaleLabelIntervalTime}"
                                        Height="28" HorizontalAlignment="Left"/>
                            </DockPanel>

                            <DockPanel>
                                <TextBlock Text="{loc:LocalizationExtension LabelYAxisInterval}" Width="80" VerticalAlignment="Center"/>
                                <TextBox Width="60" MaxLength="2" Text="{Binding YCategoryXInterval}"
                                        Height="28" HorizontalAlignment="Left"/>
                            </DockPanel>
                        </UniformGrid>
                    </StackPanel>
                </GroupBox>

                <!-- 显示设置 -->
                <GroupBox Header="{loc:LocalizationExtension LabelDisplaySettings}" Margin="0,0,0,8">
                    <StackPanel Margin="8,5">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="20"/>  <!-- 分隔空间 -->
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="10"/>  <!-- 行间距 -->
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="10"/>  <!-- 行间距 -->
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- 第一行 -->
                            <TextBlock Grid.Column="0" Text="{loc:LocalizationExtension LabelFontSize}" Width="80" VerticalAlignment="Center"/>
                            <TextBox Grid.Column="1" Width="60" MaxLength="2" Text="{Binding SensorDataFontSize}"
                                    Height="28" HorizontalAlignment="Left"/>

                            <!-- 第二行 -->
                            <TextBlock Grid.Row="2" Grid.Column="0" Text="{loc:LocalizationExtension LabelBackgroundColor}" Width="80" VerticalAlignment="Center"/>
                            <Label Grid.Row="2" Grid.Column="1" Width="50" Height="25" Name="BackgroudColor"
                                   BorderThickness="1" BorderBrush="Black" Background="{Binding BackgroudColorBrush}"
                                   MouseDown="lineColor_Click" HorizontalAlignment="Left"/>

                            <TextBlock Grid.Row="2" Grid.Column="3" Text="{loc:LocalizationExtension LabelTitleColor}" Width="80" VerticalAlignment="Center"/>
                            <Label Grid.Row="2" Grid.Column="4" Width="50" Height="25" Name="TitleFontColor"
                                   BorderThickness="1" BorderBrush="Black" Background="{Binding TitleFontColorBrush}"
                                   MouseDown="lineColor_Click" HorizontalAlignment="Left"/>

                            <!-- 第三行 -->
                            <TextBlock Grid.Row="4" Grid.Column="0" Text="{loc:LocalizationExtension LabelTableLineColor}" Width="80" VerticalAlignment="Center"/>
                            <Label Grid.Row="4" Grid.Column="1" Width="50" Height="25" Name="TableCellBorderLineColor"
                                   BorderThickness="1" BorderBrush="Black" Background="{Binding TableCellBorderLineColorBrush}"
                                   MouseDown="lineColor_Click" HorizontalAlignment="Left"/>

                            <TextBlock Grid.Row="4" Grid.Column="3" Text="{loc:LocalizationExtension LabelBigGridLineColor}" Width="80" VerticalAlignment="Center"/>
                            <Label Grid.Row="4" Grid.Column="4" Width="50" Height="25" Name="TableBorderLineColor"
                                   BorderThickness="1" BorderBrush="Black" Background="{Binding TableBorderLineColorBrush}"
                                   MouseDown="lineColor_Click" HorizontalAlignment="Left"/>
                        </Grid>
                    </StackPanel>
                </GroupBox>

                <!-- 高级设置 -->
                <GroupBox Header="{loc:LocalizationExtension LabelAdvancedSettings}" Margin="0,0,0,8">
                    <StackPanel Margin="8,5">
                        <DockPanel Margin="0,5">
                            <TextBlock Text="{loc:LocalizationExtension LabelDataSamplingInterval}" Width="120" VerticalAlignment="Center"/>
                            <TextBox Width="60" MaxLength="5" Text="{Binding DisconnectLineDataCount}"
                                    Height="28" HorizontalAlignment="Left"/>
                        </DockPanel>
                    </StackPanel>
                </GroupBox>

                <!-- 操作按钮 -->
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,5">
                    <Button Content="{loc:LocalizationExtension ButtonConfirm}" Width="90" Height="30" Style="{StaticResource confirmButton}"
                            Click="btnConfirm_Click" Margin="0,0,15,0"/>
                    <Button Content="{loc:LocalizationExtension ButtonCancel}" Width="90" Height="30" Style="{StaticResource cancelButton}"
                            Click="btnCancel_Click"/>
                </StackPanel>
            </StackPanel>
        </Grid>
    </Grid>
</Window>

