using System;
using Commiunication;

namespace SDHD.DC.DataMonitor.Utils
{
    /// <summary>
    /// OnMouseMove 单位获取修复工具类
    /// 解决 "SelectedSenStr += $" {YAxisproperts[SelectedYAxis].Name}"" 问题
    /// 提供通用的单位获取方法，不依赖于特定的类名或命名空间
    /// </summary>
    public static class OnMouseMoveUnitHelper
    {
        /// <summary>
        /// 获取传感器的实际单位，用于替换坐标轴单位
        /// 这是解决 OnMouseMove 单位显示问题的核心方法
        /// </summary>
        /// <param name="sensorId">传感器ID</param>
        /// <param name="fallbackYAxisUnit">备用的坐标轴单位</param>
        /// <returns>传感器的实际单位</returns>
        public static string GetSensorUnit(int sensorId, string fallbackYAxisUnit = "")
        {
            try
            {
                // 🎯 第一优先：实时从Global.Messages获取最新的传感器单位
                if (Global.Messages.TryGetValue("SensorInfoList", out var sensorMsg) && 
                    sensorMsg is ProjectStruct.SensorInfoList sensorInfoList)
                {
                    // 根据SensorId查找对应的传感器信息
                    if (sensorId >= 0 && sensorId < sensorInfoList.Items.Count)
                    {
                        var latestSensorInfo = sensorInfoList.Items[sensorId];
                        string latestUnit = latestSensorInfo.Unitstr ?? "";
                        if (!string.IsNullOrEmpty(latestUnit))
                        {
                            return latestUnit;
                        }
                    }
                }

                // 第二优先：使用备用的坐标轴单位（保持兼容性）
                if (!string.IsNullOrEmpty(fallbackYAxisUnit))
                {
                    return fallbackYAxisUnit;
                }
            }
            catch (Exception ex)
            {
                // 记录错误但不抛出异常，避免影响鼠标移动事件
                System.Diagnostics.Debug.WriteLine($"Error in GetSensorUnit for sensor {sensorId}: {ex.Message}");
            }

            return "";
        }

        /// <summary>
        /// 获取传感器的实际名称
        /// </summary>
        /// <param name="sensorId">传感器ID</param>
        /// <param name="fallbackName">备用名称</param>
        /// <returns>传感器名称</returns>
        public static string GetSensorName(int sensorId, string fallbackName = "")
        {
            try
            {
                // 实时从Global.Messages获取最新的传感器名称
                if (Global.Messages.TryGetValue("SensorInfoList", out var sensorMsg) && 
                    sensorMsg is ProjectStruct.SensorInfoList sensorInfoList)
                {
                    // 根据SensorId查找对应的传感器信息
                    if (sensorId >= 0 && sensorId < sensorInfoList.Items.Count)
                    {
                        var latestSensorInfo = sensorInfoList.Items[sensorId];
                        string latestName = latestSensorInfo.Name ?? "";
                        if (!string.IsNullOrEmpty(latestName))
                        {
                            return latestName;
                        }
                    }
                }

                // 备用方案
                if (!string.IsNullOrEmpty(fallbackName))
                {
                    return fallbackName;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in GetSensorName for sensor {sensorId}: {ex.Message}");
            }

            return $"Sensor {sensorId}";
        }

        /// <summary>
        /// 格式化完整的传感器信息字符串，用于OnMouseMove显示
        /// </summary>
        /// <param name="sensorId">传感器ID</param>
        /// <param name="value">传感器数值</param>
        /// <param name="precision">数值精度</param>
        /// <param name="fallbackName">备用传感器名称</param>
        /// <param name="fallbackYAxisUnit">备用坐标轴单位</param>
        /// <returns>格式化的传感器信息字符串</returns>
        public static string FormatSensorInfo(
            int sensorId, 
            double value, 
            int precision = 2,
            string fallbackName = "",
            string fallbackYAxisUnit = "")
        {
            try
            {
                // 获取传感器名称
                string sensorName = GetSensorName(sensorId, fallbackName);

                // 获取传感器单位
                string sensorUnit = GetSensorUnit(sensorId, fallbackYAxisUnit);

                // 格式化数值
                string formattedValue = value.ToString($"F{precision}");

                // 构建完整的显示字符串
                string result = $"{sensorName}: {formattedValue}";
                
                if (!string.IsNullOrEmpty(sensorUnit))
                {
                    result += $" {sensorUnit}";
                }

                return result;
            }
            catch (Exception ex)
            {
                // 备用显示方式
                System.Diagnostics.Debug.WriteLine($"Error in FormatSensorInfo for sensor {sensorId}: {ex.Message}");
                return $"Sensor {sensorId}: {value:F{precision}}";
            }
        }
    }
}

/* 
使用示例：

在您的 OnMouseMove 方法中，将原来的代码：

❌ 原来的代码：
SelectedSenStr += $" {YAxisproperts[SelectedYAxis].Name}"

✅ 替换为：
string sensorUnit = OnMouseMoveUnitHelper.GetSensorUnit(currentSensorId, YAxisproperts[SelectedYAxis].Name);
SelectedSenStr += $" {sensorUnit}";

或者使用完整的格式化方法：
SelectedSenStr = OnMouseMoveUnitHelper.FormatSensorInfo(
    currentSensorId,
    currentValue,
    2,  // 精度
    currentSensorName,  // 备用名称
    YAxisproperts[SelectedYAxis].Name  // 备用单位
);

这样就能正确显示传感器的实际单位，而不是坐标轴单位了！
*/
