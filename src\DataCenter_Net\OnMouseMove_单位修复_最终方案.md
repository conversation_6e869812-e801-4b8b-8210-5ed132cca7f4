# OnMouseMove 单位修复 - 最终解决方案

## 🎯 **问题描述**

您发现在 OnMouseMove 函数中有这样的代码：
```csharp
SelectedSenStr += $" {YAxisproperts[SelectedYAxis].Name}"
```

这行代码从坐标轴获取单位，而不是从传感器属性获取，导致显示的单位不正确。

## ✅ **解决方案**

我已经为您创建了完整的解决方案：

### **1. 核心工具类：OnMouseMoveUnitHelper**

文件：`src/DataCenter_Net/OnMouseMove_Unit_Fix_Universal.cs`

这个类提供了以下方法：
- `GetSensorUnit(int sensorId, string fallbackYAxisUnit)` - 获取传感器实际单位
- `GetSensorName(int sensorId, string fallbackName)` - 获取传感器名称
- `FormatSensorInfo(...)` - 格式化完整的传感器信息

### **2. 具体修改方式**

在您的 OnMouseMove 方法中，将：

```csharp
// ❌ 原来的错误代码
SelectedSenStr += $" {YAxisproperts[SelectedYAxis].Name}"
```

替换为：

```csharp
// ✅ 正确的代码
string sensorUnit = OnMouseMoveUnitHelper.GetSensorUnit(
    currentSensorId, 
    YAxisproperts[SelectedYAxis].Name  // 作为备用
);
SelectedSenStr += $" {sensorUnit}";
```

### **3. 完整示例**

```csharp
private void OnMouseMove(MouseEventArgs e)
{
    try
    {
        // 获取当前鼠标位置对应的传感器信息
        var currentSensor = GetCurrentFocusedSensor(e.X, e.Y);
        if (currentSensor != null)
        {
            // 🎯 使用工具类获取传感器实际单位
            string sensorUnit = OnMouseMoveUnitHelper.GetSensorUnit(
                currentSensor.SensorId,
                YAxisproperts[SelectedYAxis].Name  // 备用单位
            );
            
            // 构建显示字符串
            SelectedSenStr = $"{currentSensor.SensorName}: {currentSensor.Value:F2}";
            
            if (!string.IsNullOrEmpty(sensorUnit))
            {
                SelectedSenStr += $" {sensorUnit}";
            }
        }
    }
    catch (Exception ex)
    {
        Logger.Write($"Error in OnMouseMove: {ex.Message}");
    }
}
```

### **4. 或者使用完整的格式化方法**

```csharp
private void OnMouseMove(MouseEventArgs e)
{
    try
    {
        var currentSensor = GetCurrentFocusedSensor(e.X, e.Y);
        if (currentSensor != null)
        {
            // 🎯 一行代码搞定，自动获取最新的传感器信息
            SelectedSenStr = OnMouseMoveUnitHelper.FormatSensorInfo(
                currentSensor.SensorId,
                currentSensor.Value,
                2,  // 精度
                currentSensor.SensorName,  // 备用名称
                YAxisproperts[SelectedYAxis].Name  // 备用单位
            );
        }
    }
    catch (Exception ex)
    {
        Logger.Write($"Error in OnMouseMove: {ex.Message}");
    }
}
```

## 🔧 **核心原理**

### **优先级顺序**
1. **第一优先**：从 `Global.Messages["SensorInfoList"]` 获取最新的传感器单位
2. **第二优先**：使用坐标轴单位作为备用（保持兼容性）

### **关键优势**
- ✅ **实时性**：每次都获取最新的传感器单位
- ✅ **正确性**：显示传感器实际单位，不是坐标轴单位
- ✅ **兼容性**：有完整的备用机制
- ✅ **简单性**：只需要替换一行代码

## 🎯 **预期效果**

修改后：
- ✅ 鼠标悬停显示传感器的实际单位
- ✅ 传感器单位修改后立即生效
- ✅ 不需要重新打开窗口
- ✅ 有完整的错误处理

## 📝 **使用步骤**

1. **找到您的 OnMouseMove 方法**
2. **找到这行代码**：`SelectedSenStr += $" {YAxisproperts[SelectedYAxis].Name}"`
3. **替换为上面提供的代码**
4. **确保能获取到 currentSensorId**
5. **测试验证**

## 🔍 **测试验证**

1. **修改传感器单位**：在传感器设置中修改单位并保存
2. **鼠标悬停测试**：将鼠标移动到曲线上
3. **确认单位正确**：确认显示的是新单位，不是坐标轴单位

## 🚨 **注意事项**

1. **获取 currentSensorId**：确保在 OnMouseMove 中能够获取到当前传感器的 ID
2. **错误处理**：使用 try-catch 包装，避免影响鼠标移动
3. **性能**：实时获取是轻量级操作，不会影响性能

## 📋 **文件清单**

我为您创建了以下文件：
1. `OnMouseMove_Unit_Fix_Universal.cs` - 核心工具类
2. `OnMouseMove_Unit_Fix.md` - 详细修复指南
3. `OnMouseMove_单位修复_最终方案.md` - 本文档

**现在您只需要按照上面的步骤修改 OnMouseMove 方法，就能正确显示传感器的实际单位了！** 🎉

---

## 💡 **核心思想**

**不依赖坐标轴单位，直接从传感器属性获取最新单位**

这样就彻底解决了单位显示不正确的问题！
