﻿using System.Collections.ObjectModel;
using models = SDHD.DC.DataCollection.ApplicationCore.Domain.Entities;

namespace SDHD.DC.DataMonitor.Models.User
{
    public class UserListViewModel : NotifyPropertyChangedModel
    {
        public models.User CurrentUser { get; set; }
        public ObservableCollection<UserInfo> Users { set; get; } = new();

        public class UserInfo
        {
            public models.Rank Rank { set; get; }
            public string UserName { set; get; }
            
        }
    }
}
