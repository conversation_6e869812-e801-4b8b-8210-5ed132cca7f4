using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using SDHD.DC.DataMonitor.Resources.Localization;

namespace SDHD.DC.DataMonitor
{
    class MainWindowModel
    {
        public static ConcurrentDictionary<string, Window> _DictMonitorViewWindows = new ConcurrentDictionary<string, Window>();
        
        // Static resource manager for localization
        private static ResourceWrapper _resManager;
        public static ResourceWrapper ResManager 
        {
            get
            {
                if (_resManager == null)
                {
                    _resManager = new ResourceWrapper();
                }
                return _resManager;
            }
        }
    }

    /// <summary>
    /// Wrapper for the language manager that provides localized strings for binding in XAML
    /// </summary>
    public class ResourceWrapper
    {
        // Menu sections
        public string MenuFile => LanguageManager.Instance.GetString("MenuFile");
        public string MenuEdit => LanguageManager.Instance.GetString("MenuEdit");
        public string MenuView => LanguageManager.Instance.GetString("MenuView");
        public string MenuTools => LanguageManager.Instance.GetString("MenuTools");
        public string MenuHelp => LanguageManager.Instance.GetString("MenuHelp");
        public string MenuSettings => LanguageManager.Instance.GetString("MenuSettings");
        public string MenuExit => LanguageManager.Instance.GetString("MenuExit");
        public string MenuWindow => LanguageManager.Instance.GetString("MenuWindow");
        public string MenuAlarm => LanguageManager.Instance.GetString("MenuAlarm");
        public string MenuExport => LanguageManager.Instance.GetString("MenuExport");
        public string MenuCurrentProject => LanguageManager.Instance.GetString("MenuCurrentProject");

        // File menu items
        public string MenuNewProject => LanguageManager.Instance.GetString("MenuNewProject");
        public string MenuOpenHistoryProject => LanguageManager.Instance.GetString("MenuOpenHistoryProject");
        public string MenuRefreshHistoryProject => LanguageManager.Instance.GetString("MenuRefreshHistoryProject");

        // Current project menu items
        public string MenuStartCollect => LanguageManager.Instance.GetString("MenuStartCollect");
        public string MenuStopCollect => LanguageManager.Instance.GetString("MenuStopCollect");
        public string MenuNewGraphMonitor => LanguageManager.Instance.GetString("MenuNewGraphMonitor");
        public string MenuOpenGraphMonitor => LanguageManager.Instance.GetString("MenuOpenGraphMonitor");
        public string MenuDeleteCurrentMonitor => LanguageManager.Instance.GetString("MenuDeleteCurrentMonitor");
        public string MenuProjectLog => LanguageManager.Instance.GetString("MenuProjectLog");

        // Export menu items
        public string MenuExportPanorama => LanguageManager.Instance.GetString("MenuExportPanorama");
        public string MenuExportTimePeriod => LanguageManager.Instance.GetString("MenuExportTimePeriod");
        public string MenuHistoricalDataQuery => LanguageManager.Instance.GetString("MenuHistoricalDataQuery");

        // Common buttons
        public string ButtonOk => LanguageManager.Instance.GetString("ButtonOk");
        public string ButtonCancel => LanguageManager.Instance.GetString("ButtonCancel");
        public string ButtonSave => LanguageManager.Instance.GetString("ButtonSave");
        public string ButtonClose => LanguageManager.Instance.GetString("ButtonClose");
        public string ButtonApply => LanguageManager.Instance.GetString("ButtonApply");

        // General application strings
        public string AppTitle => LanguageManager.Instance.GetString("AppTitle");
        public string Welcome => LanguageManager.Instance.GetString("Welcome");

        // Settings menu items
        public string MenuSensorSettings => LanguageManager.Instance.GetString("MenuSensorSettings");
        public string MenuProjectSettings => LanguageManager.Instance.GetString("MenuProjectSettings");
        public string MenuMeasurementUnitSettings => LanguageManager.Instance.GetString("MenuMeasurementUnitSettings");
        public string MenuUnitTypeListSettings => LanguageManager.Instance.GetString("MenuUnitTypeListSettings");
        public string MenuTCPNetworkDeviceSettings => LanguageManager.Instance.GetString("MenuTCPNetworkDeviceSettings");
        public string MenuRedisServiceSettings => LanguageManager.Instance.GetString("MenuRedisServiceSettings");
        public string MenuModBusDeviceSettings => LanguageManager.Instance.GetString("MenuModBusDeviceSettings");
        public string MenuModBusTCPDataSettings => LanguageManager.Instance.GetString("MenuModBusTCPDataSettings");
        public string MenuS7PointTableSettings => LanguageManager.Instance.GetString("MenuS7PointTableSettings");
        public string MenuInterlockRuleSettings => LanguageManager.Instance.GetString("MenuInterlockRuleSettings");
        public string MenuGBGasProductionParameterSettings => LanguageManager.Instance.GetString("MenuGBGasProductionParameterSettings");
        public string MenuUSGasProductionParameterSettings => LanguageManager.Instance.GetString("MenuUSGasProductionParameterSettings");
        public string MenuSandVolumeParameterSettings => LanguageManager.Instance.GetString("MenuSandVolumeParameterSettings");
        public string MenuLanguageSettings => LanguageManager.Instance.GetString("MenuLanguageSettings");

        // Alarm menu items
        public string MenuAlarmSettings => LanguageManager.Instance.GetString("MenuAlarmSettings");
        public string MenuAlarmInformation => LanguageManager.Instance.GetString("MenuAlarmInformation");
        public string MenuAlarmLog => LanguageManager.Instance.GetString("MenuAlarmLog");

        // Window menu items
        public string MenuVerticalWindowArrangement => LanguageManager.Instance.GetString("MenuVerticalWindowArrangement");
        public string MenuHorizontalWindowArrangement => LanguageManager.Instance.GetString("MenuHorizontalWindowArrangement");
        public string MenuCloseAllWindows => LanguageManager.Instance.GetString("MenuCloseAllWindows");
        public string MenuSetAsFloatingWindow => LanguageManager.Instance.GetString("MenuSetAsFloatingWindow");
        public string MenuConfigureWebWindow => LanguageManager.Instance.GetString("MenuConfigureWebWindow");

        // Help menu items
        public string MenuRegister => LanguageManager.Instance.GetString("MenuRegister");

        // Button texts
        public string ButtonStartCollection => LanguageManager.Instance.GetString("ButtonStartCollection");
        public string ButtonStopCollection => LanguageManager.Instance.GetString("ButtonStopCollection");
        public string ButtonSensorSettings => LanguageManager.Instance.GetString("ButtonSensorSettings");
        public string ButtonDataMonitor => LanguageManager.Instance.GetString("ButtonDataMonitor");
        public string Button3DFlowDiagram => LanguageManager.Instance.GetString("Button3DFlowDiagram");
        public string Button2DFlowDiagram => LanguageManager.Instance.GetString("Button2DFlowDiagram");
        public string ButtonLaserPlatform => LanguageManager.Instance.GetString("ButtonLaserPlatform");
        public string ButtonStartService => LanguageManager.Instance.GetString("ButtonStartService");

        // Labels
        public string LabelServiceNotStarted => LanguageManager.Instance.GetString("LabelServiceNotStarted");
        public string LabelSoftwareNotRegistered => LanguageManager.Instance.GetString("LabelSoftwareNotRegistered");
        public string LabelCurrentProject => LanguageManager.Instance.GetString("LabelCurrentProject");

        // Language menu items
        public string MenuLanguageEnglish => LanguageManager.Instance.GetString("MenuLanguageEnglish");
        public string MenuLanguageChinese => LanguageManager.Instance.GetString("MenuLanguageChinese");
    }
}
