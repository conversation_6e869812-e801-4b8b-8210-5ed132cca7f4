using SDHD.DC.DataCollection.ApplicationCore.Domain.Entities;
using SDHD.DC.DataMonitor.Resources.Localization;
using SDHD.DC.DataMonitor.Utils;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Media;

namespace SDHD.DC.DataMonitor.Models.WellStation
{
    public class RealTimeDeviceListViewModel : NotifyPropertyChangedModel
    {
        private List<DeviceInfo> _DeviceInfos = new List<DeviceInfo>();
        public List<DeviceInfo> DeviceInfos
        {
            get { return _DeviceInfos; }
            set
            {
                _DeviceInfos = value;
                OnPropertyChanged(nameof(DeviceInfos));
            }
        }

        private int _DeviceCount = 0;
        public int DeviceCount
        {
            get { return _DeviceCount; }
            set
            {
                _DeviceCount = value;
                OnPropertyChanged(nameof(DeviceCount));
            }
        }

        public class DeviceInfo : NotifyPropertyChangedModel
        {
            private string _ModuleSN;
            public string ModuleSN
            {
                get { return _ModuleSN; }
                set
                {
                    if (_ModuleSN == value) return;
                    _ModuleSN = value;
                    OnPropertyChanged(nameof(ModuleSN));
                }
            }

            private string _IPAddress;
            public string IPAddress
            {
                get { return _IPAddress; }
                set
                {
                    if (_IPAddress == value) return;
                    _IPAddress = value;
                    OnPropertyChanged(nameof(IPAddress));
                }
            }

            private string _DeviceTypeDesc;
            public string DeviceTypeDesc
            {
                get { return _DeviceTypeDesc; }
                set
                {
                    if (_DeviceTypeDesc == value) return;
                    _DeviceTypeDesc = value;
                    OnPropertyChanged(nameof(DeviceTypeDesc));
                }
            }

            private bool _IsConnected;
            public bool IsConnected
            {
                get { return _IsConnected; }
                set
                {
                    if (_IsConnected == value) return;
                    _IsConnected = value;
                    OnPropertyChanged(nameof(IsConnected));
                    OnPropertyChanged(nameof(ConnectionStatus));
                    OnPropertyChanged(nameof(StatusColor));
                }
            }

            private bool _IsTimeout;
            public bool IsTimeout
            {
                get { return _IsTimeout; }
                set
                {
                    if (_IsTimeout == value) return;
                    _IsTimeout = value;
                    OnPropertyChanged(nameof(IsTimeout));
                    OnPropertyChanged(nameof(TimeoutStatus));
                }
            }

            private bool _IsOccupied;
            public bool IsOccupied
            {
                get { return _IsOccupied; }
                set
                {
                    if (_IsOccupied == value) return;
                    _IsOccupied = value;
                    OnPropertyChanged(nameof(IsOccupied));
                    OnPropertyChanged(nameof(OccupiedStatus));
                }
            }

            private string _OccupiedBy;
            public string OccupiedBy
            {
                get { return _OccupiedBy; }
                set
                {
                    if (_OccupiedBy == value) return;
                    _OccupiedBy = value;
                    OnPropertyChanged(nameof(OccupiedBy));
                }
            }

            private bool _IsExpanded;
            public bool IsExpanded
            {
                get { return _IsExpanded; }
                set
                {
                    if (_IsExpanded == value) return;
                    _IsExpanded = value;
                    OnPropertyChanged(nameof(IsExpanded));
                }
            }

            private ObservableCollection<DeviceChannelInfo> _Channels = new ObservableCollection<DeviceChannelInfo>();
            public ObservableCollection<DeviceChannelInfo> Channels
            {
                get { return _Channels; }
                set
                {
                    _Channels = value;
                    OnPropertyChanged(nameof(Channels));
                    OnPropertyChanged(nameof(ChannelCount));
                }
            }

            // 🎯 新增：保存原始模块数据，用于延迟加载通道
            public ProjectStruct.SysBase.Types.deviceBase.Types.moduleItem ModuleItemData { get; set; }

            // 🎯 新增：标记通道是否已完全加载
            private bool _ChannelsLoaded = false;
            public bool ChannelsLoaded
            {
                get { return _ChannelsLoaded; }
                set
                {
                    if (_ChannelsLoaded == value) return;
                    _ChannelsLoaded = value;
                    OnPropertyChanged(nameof(ChannelsLoaded));
                }
            }

            // 🎯 新增：实时数值（从 real 中的第一个 values 获取）
            private string _RealValue = "-";
            public string RealValue
            {
                get { return _RealValue; }
                set
                {
                    if (_RealValue == value) return;
                    _RealValue = value;
                    OnPropertyChanged(nameof(RealValue));
                }
            }

            // 🎯 新增：实时超时状态（从 real 中的 span 获取）
            private string _RealSpan = "-";
            public string RealSpan
            {
                get { return _RealSpan; }
                set
                {
                    if (_RealSpan == value) return;
                    _RealSpan = value;
                    OnPropertyChanged(nameof(RealSpan));
                }
            }

            // 计算属性
            public string ConnectionStatus => IsConnected ? ResourceHelper.GetString("RealTimeDeviceConnection_Online") : ResourceHelper.GetString("RealTimeDeviceConnection_Offline");
            public string TimeoutStatus => IsTimeout ? ResourceHelper.GetString("RealTimeDeviceConnection_Timeout") : ResourceHelper.GetString("RealTimeDeviceConnection_Normal");
            public string OccupiedStatus => IsOccupied ? ResourceHelper.GetString("RealTimeDeviceConnection_Occupied") : ResourceHelper.GetString("RealTimeDeviceConnection_Free");
            public int ChannelCount => Channels?.Count ?? 0;
            public Brush StatusColor => IsConnected ? Brushes.Green : Brushes.Red;
        }
    }

    public class DeviceChannelInfo : NotifyPropertyChangedModel
    {
        private string _Name;
        public string Name
        {
            get { return _Name; }
            set
            {
                if (_Name == value) return;
                _Name = value;
                OnPropertyChanged(nameof(Name));
            }
        }

        private string _IndexNo;
        public string IndexNo
        {
            get { return _IndexNo; }
            set
            {
                if (_IndexNo == value) return;
                _IndexNo = value;
                OnPropertyChanged(nameof(IndexNo));
            }
        }

        private string _DataTypeDesc;
        public string DataTypeDesc
        {
            get { return _DataTypeDesc; }
            set
            {
                if (_DataTypeDesc == value) return;
                _DataTypeDesc = value;
                OnPropertyChanged(nameof(DataTypeDesc));
            }
        }

        private string _Value;
        public string Value
        {
            get { return _Value; }
            set
            {
                if (_Value == value) return;
                _Value = value;
                OnPropertyChanged(nameof(Value));
            }
        }

        private bool _IsTimeout;
        public bool IsTimeout
        {
            get { return _IsTimeout; }
            set
            {
                if (_IsTimeout == value) return;
                _IsTimeout = value;
                OnPropertyChanged(nameof(IsTimeout));
                OnPropertyChanged(nameof(TimeoutStatus));
            }
        }

        private string _Remark;
        public string Remark
        {
            get { return _Remark; }
            set
            {
                if (_Remark == value) return;
                _Remark = value;
                OnPropertyChanged(nameof(Remark));
            }
        }

        // 计算属性
        public string TimeoutStatus => IsTimeout ? ResourceHelper.GetString("RealTimeDeviceConnection_Timeout") : ResourceHelper.GetString("RealTimeDeviceConnection_Normal");
    }
}
