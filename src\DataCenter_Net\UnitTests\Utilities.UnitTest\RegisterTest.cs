﻿using SDHD.DC.Utilities;
using SDHD.DC.Utilities.WPF;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using Xunit;

namespace Utilities.UnitTest
{
    public class RegisterTest
    {
        [Fact]
        public void GetVerifyCode()
        {
            var sysHandler = new SystemHandler();
            string name = "test";
            int version = 10;
            var cpuInfo = sysHandler.GetCPUSerialNo();
            var diskInfo = sysHandler.GetOSHardDiskID();

            var info = version + name + cpuInfo + diskInfo;
            var verifyCode = StringHandler.MD5Encrypt32(info);
            Assert.Equal(32, verifyCode.Length);
            Assert.True(true);


            //long version2 = (version << 1) ^ 0x80000001;
            ////var a = Encoding.UTF8.GetBytes(version2);
            //var b = BitConverter.GetBytes(version2);
        }

        [Fact]
        public void TestMD5()
        {
            var result = StringHandler.MD5Encrypt32("a");
            Assert.Equal(32, result.Length);
        }
    }
}
