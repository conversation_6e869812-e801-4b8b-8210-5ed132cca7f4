using Commiunication;
using ProjectStruct;
using SDHD.DC.DataCollection.ApplicationCore.Domain.Request;
using SDHD.DC.DataCollection.ApplicationCore.Interfaces.Service;
using SDHD.DC.DataCollection.ApplicationCore.Services;
using SDHD.DC.DataMonitor.Models.MonitorView;
using SDHD.DC.DataMonitor.UserControls.Chart;
using SDHD.DC.DataMonitor.Utils;
using SDHD.DC.DataMonitor.Views.Common;
using SDHD.DC.Utilities;
using SDHD.DC.Utilities.Constants;
using SDHD.DC.Utilities.ServiceExtensions;
using SDHD.DC.Utilities.WPF.Extensions;
using SDHD.DC.DataMonitor.Resources.Localization;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using static ProjectStruct.SysBase.Types.ProBase.Types;
using static ProjectStruct.SysBase.Types;
using SDHD.DC.Utilities.Extensions;
using SDHD.DC.DataCollection.ApplicationCore.Domain.Entities;
using Google.Protobuf;
using System.Windows.Forms;

namespace SDHD.DC.DataMonitor.Views.MonitorView
{
    /// <summary>
    /// MonitorViewSettingWindow.xaml 的交互逻辑
    /// </summary>
    public partial class MonitorViewSettingWindow : Window
    {
        int _ViewId = -1;
        MonitorViewSettingViewModel _Model;
        IMonitorViewService _MonitorViewService;
        RealTimeCurveChartSetting _RealTimeCurveChartSetting;
        public Action<string, string> MonitorViewSettingChanged;
        public Action<RealTimeCurveChartSetting> MonitorViewChartSettingChanged;
        private SysBase.Types.ProBase.Types.ViewFileList.Types.ViewInfo _ViewInfo = new();

        public MonitorViewSettingWindow(string wellStationId, string monitorViewItemId, RealTimeCurveChartSetting realTimeCurveChartSetting, bool isFirstCreateNew = false)
        {
            InitializeComponent();
            _Model = new()
            {
                SensorDataFontSize = "14",
                DisconnectLineDataCount = "0",
                IsFirstCreateNew = isFirstCreateNew,
                MonitorViewItemId = monitorViewItemId,
                WellStationId = wellStationId ?? AppInfo.CurrentWellStationId,
            };
            _RealTimeCurveChartSetting = realTimeCurveChartSetting;
            _MonitorViewService = ServiceHelper.GetService<IMonitorViewService>();
            Init();
            this.DataContext = _Model;
        }

        private void Init()
        {
            try
            {
                string windowTitle = this.Title;
                if (string.IsNullOrEmpty(_Model.WellStationId))
                {
                    windowTitle += LanguageManager.Instance.GetString("TextUnknown");
                }
                else
                {
                    windowTitle += string.Format(" - {0}", AppInfo.CurrentWellStationName);
                }
                Title = windowTitle;
                _Model.WindowTitle = windowTitle;

                if (_Model.IsFirstCreateNew)
                {
                    _Model.MonitorName = LanguageManager.Instance.GetString("TextNewMonitorScheme");
                    _Model.BackgroudColor = "#FFFFFFFF";
                }
                else
                {
                    FillMonitorViewItem();
                }

            }
            catch (Exception ex)
            {
                this.ShowError(ex);
            }
        }

        private void FillMonitorViewItem()
        {
            if (int.TryParse(_Model.MonitorViewItemId, out _ViewId))
            {
                _ViewInfo = ViewFileListHandler.GetViewInfo(_ViewId) ?? new();
            }

            _Model.MonitorName = _ViewInfo.Name;
            _Model.IsShowSerialNo = _ViewInfo.ShowSN;
            _Model.YCategoryXInterval = $"{_ViewInfo.Yinterval}";
            _Model.DisconnectLineDataCount = $"{_ViewInfo.Offline}";
            _Model.IntDisplayTimeInterval = $"{_ViewInfo.ClockLock}";
            _Model.LineWidth = $"{(_ViewInfo.LineWeight <= 0 ? DefaultValues.MonitorViewDefaultLineWidth : _ViewInfo.LineWeight)}";
            _Model.SensorDataFontSize = $"{(_ViewInfo.FontSize <= 0 ? DefaultValues.MonitorViewDefaultFontSize : _ViewInfo.FontSize)}";
            _Model.YScaleCount = $"{(_ViewInfo.CellCount < DefaultValues.MonitorViewDefaultSmallTickCount ? DefaultValues.MonitorViewDefaultSmallTickCount : _ViewInfo.CellCount)}";
            _Model.XScaleLabelIntervalTime = $"{(_ViewInfo.XCellScale < DefaultValues.MonitorViewDefaultBigTickCount ? DefaultValues.MonitorViewDefaultBigTickCount : _ViewInfo.XCellScale)}";

            _Model.IsDisplayMultipleLines = Convert.ToBoolean(_ViewInfo.MutShow);
            _Model.IsShowWorkStatus = !Convert.ToBoolean(_ViewInfo.HideGongKang);
            _Model.IsShowAxisRelatedSensor = Convert.ToBoolean(_ViewInfo.SensorNameShow);
            _Model.BackgroudColor = ColorExtensions.ConvertToHexFromArgb(_ViewInfo.BGColor);
            _Model.TitleFontColor = ColorExtensions.ConvertToHexFromArgb(_ViewInfo.CaptionFontColor);
            _Model.XLineTimeFontColor = ColorExtensions.ConvertToHexFromArgb(_ViewInfo.TimeFontColor);
            _Model.TableBorderLineColor = ColorExtensions.ConvertToHexFromArgb(_ViewInfo.BigCellColor);
            _Model.WorkStatusLineColor = ColorExtensions.ConvertToHexFromArgb(_ViewInfo.GongKangColor);
            _Model.TableCellBorderLineColor = ColorExtensions.ConvertToHexFromArgb(_ViewInfo.CellColor);
            _Model.WorkStatusTextColor = ColorExtensions.ConvertToHexFromArgb(_ViewInfo.GongKangFontColor);
        }

        #region events
        protected void lineColor_Click(object sender, EventArgs e)
        {
            try
            {
                var lblColor = sender as System.Windows.Controls.Label;
                string colorName = lblColor.Name;
                var colorHexString = WPFColorExtensions.ConvertToHexString(lblColor.Background);
                var win = new ColorPickerWindow(colorHexString);
                if (win.ShowDialog() == true)
                    OnColorSelected(win.SelectedColorHex, colorName);
            }
            catch (Exception ex)
            {
                this.ShowError(ex);
            }
        }

        public void OnColorSelected(string selectedColorHex, string colorName)
        {
            switch (colorName)
            {
                case nameof(_Model.BackgroudColor):
                    _Model.BackgroudColor = selectedColorHex;
                    break;
                case nameof(_Model.TitleFontColor):
                    _Model.TitleFontColor = selectedColorHex;
                    break;
                case nameof(_Model.TableCellBorderLineColor):
                    _Model.TableCellBorderLineColor = selectedColorHex;
                    break;
                case nameof(_Model.XLineTimeFontColor):
                    _Model.XLineTimeFontColor = selectedColorHex;
                    break;
                case nameof(_Model.TableBorderLineColor):
                    _Model.TableBorderLineColor = selectedColorHex;
                    break;
                case nameof(_Model.WorkStatusLineColor):
                    _Model.WorkStatusLineColor = selectedColorHex;
                    break;
                case nameof(_Model.WorkStatusTextColor):
                    _Model.WorkStatusTextColor = selectedColorHex;
                    break;
            }
        }

        private void btnConfirm_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(_Model.MonitorName))
                {
                    this.ShowMessage(LanguageManager.Instance.GetString("MessageMonitorSchemeNameRequired"));
                    return;
                }

                if (!int.TryParse(_Model.LineWidth.Trim(), out int lineWidth))
                {
                    this.ShowMessage(LanguageManager.Instance.GetString("MessageCurveWidthFormatError"));
                    return;
                }
                if (lineWidth < 1)
                {
                    this.ShowMessage(LanguageManager.Instance.GetString("MessageCurveWidthMinValue"));
                    return;
                }

                if (!int.TryParse(_Model.YScaleCount.Trim(), out int smallTickCount))
                {
                    this.ShowMessage(LanguageManager.Instance.GetString("MessageSmallGridCountFormatError"));
                    return;
                }
                if (smallTickCount < DefaultValues.MonitorViewDefaultSmallTickCount)
                {
                    this.ShowMessage(string.Format(LanguageManager.Instance.GetString("MessageSmallGridCountMinValue"), DefaultValues.MonitorViewDefaultSmallTickCount));
                    return;
                }

                if (!int.TryParse(_Model.XScaleLabelIntervalTime.Trim(), out int bigTickConut))
                {
                    this.ShowMessage(LanguageManager.Instance.GetString("MessageBigGridCountFormatError"));
                    return;
                }
                if (bigTickConut < DefaultValues.MonitorViewDefaultBigTickCount)
                {
                    this.ShowMessage(string.Format(LanguageManager.Instance.GetString("MessageBigGridCountMinValue"), DefaultValues.MonitorViewDefaultBigTickCount));
                    return;
                }
                if (!int.TryParse(_Model.YCategoryXInterval.Trim(), out int yCategoryXInterval))
                {
                    this.ShowMessage(LanguageManager.Instance.GetString("MessageYAxisIntervalFormatError"));
                    return;
                }
                if (yCategoryXInterval < DefaultValues.MonitorViewDefaultYCategoryXInterval)
                {
                    this.ShowMessage(string.Format(LanguageManager.Instance.GetString("MessageYAxisIntervalMinValue"), DefaultValues.MonitorViewDefaultYCategoryXInterval));
                    return;
                }

                if (string.IsNullOrWhiteSpace(_Model.SensorDataFontSize))
                {
                    this.ShowMessage(LanguageManager.Instance.GetString("MessageFontSizeRequired"));
                    return;
                }
                if (!int.TryParse(_Model.SensorDataFontSize.Trim(), out int sensorDataFontSize))
                {
                    this.ShowMessage(LanguageManager.Instance.GetString("MessageFontSizeFormatError"));
                    return;
                }
                if (sensorDataFontSize < 1)
                {
                    this.ShowMessage(LanguageManager.Instance.GetString("MessageFontSizeMinValue"));
                    return;
                }

                if (string.IsNullOrWhiteSpace(_Model.DisconnectLineDataCount))
                {
                    this.ShowMessage(LanguageManager.Instance.GetString("MessageSamplingIntervalRequired"));
                    return;
                }
                if (!int.TryParse(_Model.DisconnectLineDataCount.Trim(), out int disconnectLineDataCount))
                {
                    this.ShowMessage(LanguageManager.Instance.GetString("MessageSamplingIntervalFormatError"));
                    return;
                }
                if (disconnectLineDataCount < 0)
                {
                    this.ShowMessage(LanguageManager.Instance.GetString("MessageSamplingIntervalMinValue"));
                    return;
                }

                _ViewInfo.ClockLock = 1;
                _ViewInfo.LineWeight = lineWidth;
                _ViewInfo.XCellScale = bigTickConut;
                _ViewInfo.CellCount = smallTickCount;
                _ViewInfo.FontSize = sensorDataFontSize;
                _ViewInfo.ShowSN = _Model.IsShowSerialNo;
                _ViewInfo.Yinterval = yCategoryXInterval;
                _ViewInfo.Name = _Model.MonitorName.Trim();
                _ViewInfo.Offline = disconnectLineDataCount;
                _ViewInfo.MutShow = _Model.IsDisplayMultipleLines;
                _ViewInfo.HideGongKang = !_Model.IsShowWorkStatus;
                _ViewInfo.SensorNameShow = _Model.IsShowAxisRelatedSensor ? 1 : 0;
                _ViewInfo.BGColor = ColorExtensions.ConvertToArgbFromHex(_Model.BackgroudColor, true);
                _ViewInfo.CaptionFontColor = ColorExtensions.ConvertToArgbFromHex(_Model.TitleFontColor, true);
                _ViewInfo.TimeFontColor = ColorExtensions.ConvertToArgbFromHex(_Model.XLineTimeFontColor, true);
                _ViewInfo.BigCellColor = ColorExtensions.ConvertToArgbFromHex(_Model.TableBorderLineColor, true);
                _ViewInfo.GongKangColor = ColorExtensions.ConvertToArgbFromHex(_Model.WorkStatusLineColor, true);
                _ViewInfo.CellColor = ColorExtensions.ConvertToArgbFromHex(_Model.TableCellBorderLineColor, true);
                _ViewInfo.GongKangFontColor = ColorExtensions.ConvertToArgbFromHex(_Model.WorkStatusTextColor, true);

                _Model.MonitorViewItemId = $"{ViewFileListHandler.AddOrUpdate(_Model.IsFirstCreateNew ? -1 : _ViewId, _ViewInfo)}";

                if (_RealTimeCurveChartSetting != null)
                {
                    _RealTimeCurveChartSetting.CurveWidth = lineWidth;
                    _RealTimeCurveChartSetting.Title = _Model.MonitorName;
                    _RealTimeCurveChartSetting.BigTickCount = bigTickConut;
                    _RealTimeCurveChartSetting.YAxisSpan = yCategoryXInterval;
                    _RealTimeCurveChartSetting.SmallTickCount = smallTickCount;
                    _RealTimeCurveChartSetting.TitleFontSize = sensorDataFontSize;
                    _RealTimeCurveChartSetting.SpanSecond =
                    _RealTimeCurveChartSetting.ShowLineSpan =
                    _RealTimeCurveChartSetting.SampledIntervalSeconds = disconnectLineDataCount;
                    _RealTimeCurveChartSetting.BigTickColor = WPFColorExtensions.ConvertToControlColor(_Model.TableBorderLineColor);
                    _RealTimeCurveChartSetting.TitleFontColor = WPFColorExtensions.ConvertToControlColor(_Model.TitleFontColor);
                    _RealTimeCurveChartSetting.SmallTickColor = WPFColorExtensions.ConvertToControlColor(_Model.TableCellBorderLineColor);
                    _RealTimeCurveChartSetting.BackgroundColor = WPFColorExtensions.ConvertToControlColor(_Model.BackgroudColor);
                }
                this.Close();
                MonitorViewSettingChanged?.Invoke(_Model.WellStationId, _Model.MonitorViewItemId);
                MonitorViewChartSettingChanged?.Invoke(_RealTimeCurveChartSetting);
            }
            catch (Exception ex)
            {
                this.ShowError(ex);
            }
        }

        private void btnCancel_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                this.Close();
            }
            catch (Exception ex)
            {
                this.ShowError(ex);
            }
        }
        #endregion
    }
}
