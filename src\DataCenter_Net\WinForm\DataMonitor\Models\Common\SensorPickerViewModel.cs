﻿using SDHD.DC.Utilities;
using SDHD.DC.Utilities.WPF.Extensions;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Media;

namespace SDHD.DC.DataMonitor.Models.Common
{
    public class SensorPickerViewModel : NotifyPropertyChangedModel
    {
        public string WellStationId { get; set; }
        public List<SensorInfo> Sensors { get; set; } = new List<SensorInfo>();
        public class SensorInfo
        {
            public string SensorId { set; get; }
            public string SensorFullName { set; get; }
            public string SensorCategoryDesc { set; get; }
        }
    }
}
