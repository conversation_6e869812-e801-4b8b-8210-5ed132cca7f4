﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SDHD.DC.DataMonitor.Models.Setting
{
    public class RegisterViewModel : NotifyPropertyChangedModel
    {
        private string _UnitName;
        private string _AuthCode;
        private string _VerifyCode;
        public RegisterViewModel()
        {
        }

        public string UnitName
        {
            get { return _UnitName; }
            set
            {
                _UnitName = value;
                OnPropertyChanged(nameof(UnitName));
            }
            
        }

        public string VerifyCode
        {
            get { return _VerifyCode; }
            set
            {
                _VerifyCode = value;
                OnPropertyChanged(nameof(VerifyCode));
            }
        }
        public string AuthCode
        {
            get { return _AuthCode; }
            set
            {
                _AuthCode = value;
                OnPropertyChanged(nameof(AuthCode));
            }
        }
    }
}
