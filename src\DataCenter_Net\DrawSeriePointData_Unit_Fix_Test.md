# DrawSeriePointData 单位修复测试指南

## 🎯 **修复内容**

### **问题描述**
- 鼠标移动时显示的数据点单位使用的是Y轴单位，而不是传感器的实际单位
- 传感器设置更新后，数据点显示的单位没有同步更新

### **修复方案**
1. **实时获取传感器单位**：在 `OnMouseMove` 方法中，直接从 `Global.Messages["SensorInfoList"]` 获取最新的传感器单位
2. **更新数据格式**：将 `SelectedSenStr` 的格式改为 `"传感器名称|时间|数值 单位"`
3. **同步更新Seriespropert**：在传感器设置更新时，同时更新 `Seriespropert.Unit` 字段

## 🔍 **测试步骤**

### **步骤1：验证基本功能**
1. 启动应用程序
2. 打开监控视图，显示曲线图
3. 将鼠标移动到曲线上，观察数据点显示
4. 确认显示的信息包含：
   - 传感器名称
   - 时间戳
   - 数值和正确的单位

### **步骤2：验证单位更新**
1. 在传感器设置中修改某个传感器的单位
2. 保存设置
3. 观察曲线图上的数据点显示
4. 确认数据点显示的单位已经更新为新单位

### **步骤3：验证实时性**
1. 修改传感器单位后，立即将鼠标移动到曲线上
2. 确认数据点显示的是新单位，无需重新打开窗口

### **步骤4：检查日志**
1. 查看应用程序日志
2. 确认有以下日志信息：
   ```
   Updated Seriespropert: Sensor X, Unit: '新单位'
   DrawSeriePointData: Sensor=传感器名称, Time=时间, Value=数值 单位
   ```

## ✅ **预期结果**

### **修复前**
- 数据点显示：`传感器名称|时间|数值 Y轴名称`
- 单位来源：Y轴名称（可能不正确）
- 更新延迟：需要重新打开窗口

### **修复后**
- 数据点显示：`传感器名称|时间|数值 传感器单位`
- 单位来源：传感器的实际单位（从Global.Messages实时获取）
- 更新实时：传感器设置修改后立即生效

## 🐛 **调试信息**

### **关键日志点**
1. **单位获取**：`GetSensorUnit` 方法中的调试信息
2. **名称获取**：`GetSensorName` 方法中的调试信息
3. **数据点绘制**：`DrawSeriePointData` 方法中的调试信息
4. **Series更新**：`UpdateRealTimeCurveChartSeriesUnits` 方法中的日志

### **常见问题排查**
1. **单位显示为空**：检查 `Global.Messages["SensorInfoList"]` 是否正确加载
2. **单位未更新**：检查 `OnSetMessage` 事件是否正确触发
3. **数据格式错误**：检查 `SelectedSenStr` 的格式是否正确

## 📝 **代码变更总结**

### **RealTimeCurveChart.cs**
1. 修改 `OnMouseMove` 方法，使用 `GetSensorUnit` 和 `GetSensorName` 获取最新信息
2. 新增 `GetSensorUnit` 方法，实时从Global.Messages获取传感器单位
3. 新增 `GetSensorName` 方法，实时从Global.Messages获取传感器名称
4. 修改 `DrawSeriePointData` 方法，支持新的数据格式

### **MonitorWindowView_SensorInfoList_Update.cs**
1. 新增 `UpdateRealTimeCurveChartSeriesUnits` 方法，同步更新Seriespropert.Unit
2. 修改 `UpdateCurveLineInfoUnits` 方法，调用新的更新方法

## 🎉 **修复效果**

修复后，您将获得：
- ✅ 正确的传感器单位显示
- ✅ 实时的单位更新
- ✅ 无需重新打开窗口
- ✅ 完整的错误处理和日志记录 