# HD-WTONEsys 应用程序部署说明

## 升级信息
- **原版本**: .NET 6.0
- **新版本**: .NET 8.0 (LTS 长期支持版本)
- **支持周期**: 到 2026年11月
- **升级日期**: 2025年7月15日

## 系统要求

### 操作系统
- Windows 10 版本 1607 或更高版本
- Windows 11 (所有版本)
- Windows Server 2016 或更高版本

### 运行时要求
- **.NET 8.0 Desktop Runtime** (必需)

## 部署步骤

### 1. 安装 .NET 8.0 Desktop Runtime

如果目标电脑没有安装 .NET 8.0 Desktop Runtime，请按以下步骤安装：

1. 访问 Microsoft 官方下载页面：
   ```
   https://dotnet.microsoft.com/download/dotnet/8.0
   ```

2. 在 "Desktop Runtime" 部分，下载 **x64** 版本：
   ```
   windowsdesktop-runtime-8.0.x-win-x64.exe
   ```

3. 运行安装程序，按提示完成安装

### 2. 部署应用程序

1. 将整个 `publish` 文件夹复制到目标电脑
2. 确保保持文件夹结构完整，特别是 `Sys` 配置目录
3. 运行 `check_dependencies.bat` 检查依赖项
4. 如果检查通过，可以直接运行 `HD-WTONEsys.exe`

### 3. 验证部署

运行 `check_dependencies.bat` 脚本，它会：
- 检查 .NET 8.0 Desktop Runtime 是否已安装
- 验证应用程序文件完整性
- 自动启动应用程序（如果检查通过）

## 文件结构

```
publish/
├── HD-WTONEsys.exe          # 主程序文件
├── check_dependencies.bat   # 依赖检查脚本
├── 部署说明.md              # 本说明文件
├── Assets/                  # 资源文件
│   ├── images/
│   └── Warning/
└── Sys/                     # 系统配置文件
    ├── config.json
    ├── ProInfo.pro
    ├── SysConfig.pro
    └── ...
```

## 升级优势

### 性能提升
- 更快的启动时间
- 更好的内存管理
- 优化的垃圾回收

### 安全性
- 最新的安全补丁
- 更强的类型安全
- 改进的异常处理

### 长期支持
- .NET 8.0 是 LTS 版本，支持到 2026年11月
- 定期安全更新和错误修复
- 更好的向后兼容性

## 故障排除

### 常见问题

1. **应用程序无法启动**
   - 确认已安装 .NET 8.0 Desktop Runtime
   - 检查 Windows 版本是否符合要求
   - 运行 `check_dependencies.bat` 进行诊断

2. **缺少配置文件错误**
   - 确保 `Sys` 文件夹及其内容完整复制
   - 检查文件权限，确保应用程序有读写权限

3. **性能问题**
   - 确保系统有足够的内存 (推荐 4GB 以上)
   - 检查防病毒软件是否阻止应用程序运行

### 获取帮助

如果遇到问题，请：
1. 运行 `check_dependencies.bat` 获取详细信息
2. 检查 Windows 事件日志中的错误信息
3. 联系技术支持团队

## 版本历史

- **v2.0** (2025-07-15): 升级到 .NET 8.0，提升性能和安全性
- **v1.x** (之前版本): 基于 .NET 6.0

---

**注意**: 此版本需要 .NET 8.0 Desktop Runtime。如果您的系统中只有 .NET 6.0，应用程序将无法运行。请按照上述步骤安装正确的运行时版本。
