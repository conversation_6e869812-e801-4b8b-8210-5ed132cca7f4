﻿using SDHD.DC.DataMonitor.Utils;
using SDHD.DC.Utilities;
using SDHD.DC.Utilities.Constants;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Media;
using static ProjectStruct.SysBase.Types.NationalStandardGasProductionParameters.Types;

namespace SDHD.DC.DataMonitor.Models.MonitorView
{
    public class DataMonitorViewModel : NotifyPropertyChangedModel
    {
        public string WellStationId { get; set; }
        public string MonitorViewItemId { get; set; }

        private string _CurrentWellStationName;
        public string CurrentWellStationName
        {
            get { return _CurrentWellStationName; }
            set
            {
                if (_CurrentWellStationName != value)
                {
                    _CurrentWellStationName = value;
                    OnPropertyChanged(nameof(CurrentWellStationName));
                }
            }
        }
        private bool _IsDCStarted;
        public bool IsDCStarted
        {
            get { return _IsDCStarted; }
            set
            {
                if (_IsDCStarted != value)
                {
                    _IsDCStarted = value;
                    OnPropertyChanged(nameof(IsDCStarted));
                }
            }
        }


        public string LastSelectedSensorId { get; set; }

        public DateTime? MaxDatePicker { set; get; }
        public DateTime? ShowStartDatePicker { set; get; }
        public string ShowStartTimeHour { set; get; }
        public string ShowStartTimeMinute { set; get; }
        public string ShowStartDate
        {
            get
            {
                return ShowStartDatePicker != null ? $"{ShowStartDatePicker.Value:yyyy-MM-dd}" : string.Empty;
            }
        }
        public DateTime? ShowEndDatePicker { set; get; }
        public string ShowEndTimeHour { set; get; }
        public string ShowEndTimeMinute { set; get; }

        public string ShowEndDate
        {
            get
            {
                return ShowEndDatePicker != null ? $"{ShowEndDatePicker.Value:yyyy-MM-dd}" : string.Empty;
            }
        }


        private int _SensorDataFontSize;
        public int SensorDataFontSize
        {
            get { return _SensorDataFontSize; }
            set
            {
                if (_SensorDataFontSize == value) return;
                _SensorDataFontSize = value;
                OnPropertyChanged(nameof(SensorDataFontSize));
            }
        }


        private bool _IsAutoTimeInterval;
        public bool IsAutoTimeInterval
        {
            get { return _IsAutoTimeInterval; }
            set
            {
                if (_IsAutoTimeInterval == value) return;
                _IsAutoTimeInterval = value;
                OnPropertyChanged(nameof(IsAutoTimeInterval));
            }
        }


        private string _SensorGroup = CodeNames.SensorUserGroup.AllSensors;
        public string SensorGroup
        {
            get { return _SensorGroup; }
            set
            {
                if (_SensorGroup != value)
                {
                    _SensorGroup = value;
                    OnPropertyChanged(nameof(SensorGroup));
                }
            }
        }
        public List<SelectItem> SensorGroups { set; get; } = new()
        {
            new(){ Text="全部传感器",Value="0"},
            new(){ Text="1#流程",Value="1"},
            new(){ Text="2#流程",Value="2"},
            new(){ Text="3#流程",Value="3"},
            new(){ Text="4#流程",Value="4"},
            new(){ Text="5#流程",Value="5"},
            new(){ Text="6#流程",Value="6"},
            new(){ Text="7#流程",Value="7"},
            new(){ Text="8#流程",Value="8"},
            new(){ Text="9#流程",Value="9"},
        };

        public ObservableCollection<SensorInfo> Sensors { set; get; } = new();
        public class SensorInfo : NotifyPropertyChangedModel
        {
            private string _SensorLineColor;
            public string SensorLineColor
            {
                get { return _SensorLineColor; }
                set
                {
                    if (value == _SensorLineColor) return;
                    _SensorLineColor = value;
                    OnPropertyChanged(nameof(SensorLineColor));
                    OnPropertyChanged(nameof(SensorLineColorBrush));
                }
            }
            public SolidColorBrush SensorLineColorBrush
            {
                get
                {
                    return ColorExtensionsWPF.ConvertToBrush(SensorLineColor, true);
                }
            }
            public string SensorId { get; set; }

            private bool _IsShow = false;
            public bool IsShow
            {
                get { return _IsShow; }
                set
                {
                    if (_IsShow == value) return;
                    _IsShow = value;
                    OnPropertyChanged(nameof(IsShow));
                }
            }
            private string _Status;
            public string Status
            {
                get { return _Status; }
                set
                {
                    if (_Status == value) return;
                    _Status = value;
                    OnPropertyChanged(nameof(Status));
                }
            }
            private bool _IsShowColor;
            public bool IsShowColor
            {
                get { return _IsShowColor; }
                set
                {
                    if (_IsShowColor == value) return;
                    _IsShowColor = value;
                    OnPropertyChanged(nameof(IsShowColor));
                }
            }

            private string _StatusIconPath;
            public string StatusIconPath
            {
                get { return _StatusIconPath; }
                set
                {
                    if (_StatusIconPath == value) return;
                    _StatusIconPath = value;
                    OnPropertyChanged(nameof(StatusIconPath));
                }
            }
            private string _SensorName;
            public string SensorName
            {
                get { return _SensorName; }
                set
                {
                    if (_SensorName != value)
                    {
                        _SensorName = value;
                        OnPropertyChanged(nameof(SensorName));
                    }
                }
            }
            private string _DataValue;
            public string DataValue
            {
                get { return _DataValue; }
                set
                {
                    if (_DataValue != value)
                    {
                        _DataValue = value;
                        OnPropertyChanged(nameof(DataValue));
                    }
                }
            }


            private int _DataPrecision;
            public int DataPrecision
            {
                get { return _DataPrecision; }
                set
                {
                    if (_DataPrecision != value)
                    {
                        _DataPrecision = value;
                        OnPropertyChanged(nameof(DataPrecision));
                    }
                }
            }

            private string _DataUnit;
            public string DataUnit
            {
                get { return _DataUnit; }
                set
                {
                    if (_DataUnit != value)
                    {
                        _DataUnit = value;
                        OnPropertyChanged(nameof(DataUnit));
                    }
                }
            }
            public string CategoryName { get; set; }
            public int SeqNo { set; get; }

            public string CategoryUnit { get { return $"{CategoryName}({DataUnit})"; } }

            private bool _IsAlarm = false;
            public bool IsAlarm
            {
                get { return _IsAlarm; }
                set
                {
                    if (_IsAlarm == value) return;
                    _IsAlarm = value;
                    OnPropertyChanged(nameof(IsAlarm));
                }
            }

            private bool _IsNotAlarm = false;
            public bool IsNotAlarm
            {
                get { return _IsNotAlarm; }
                set
                {
                    if (_IsNotAlarm == value) return;
                    _IsNotAlarm = value;
                    OnPropertyChanged(nameof(IsNotAlarm));
                }
            }
        }
    }
}
