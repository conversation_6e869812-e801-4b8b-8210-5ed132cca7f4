@echo off
echo ========================================
echo .NET 8.0 应用程序依赖检查工具
echo ========================================
echo.

echo 正在检查 .NET 8.0 Desktop Runtime...
dotnet --list-runtimes | findstr "Microsoft.WindowsDesktop.App 8."
if %errorlevel% equ 0 (
    echo ✓ .NET 8.0 Desktop Runtime 已安装
) else (
    echo ✗ .NET 8.0 Desktop Runtime 未安装
    echo.
    echo 请下载并安装 .NET 8.0 Desktop Runtime:
    echo https://dotnet.microsoft.com/download/dotnet/8.0
    echo 选择 "Desktop Runtime" 的 x64 版本
    echo.
    pause
    exit /b 1
)

echo.
echo 正在检查应用程序文件...
if exist "HD-WTONEsys.exe" (
    echo ✓ 主程序文件存在
) else (
    echo ✗ 主程序文件不存在
    pause
    exit /b 1
)

echo.
echo 正在检查系统配置文件...
if exist "Sys" (
    echo ✓ 系统配置目录存在
) else (
    echo ✗ 系统配置目录不存在
    pause
    exit /b 1
)

echo.
echo ========================================
echo 所有依赖检查完成！
echo 应用程序应该可以正常运行。
echo ========================================
echo.
echo 按任意键启动应用程序...
pause > nul

echo 正在启动应用程序...
start "" "HD-WTONEsys.exe"
