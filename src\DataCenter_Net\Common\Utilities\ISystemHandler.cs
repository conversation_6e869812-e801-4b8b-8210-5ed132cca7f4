﻿namespace SDHD.DC.Utilities
{
    public interface ISystemHandler
    {
        /// <summary>
        /// 取得硬盘序列号
        /// </summary>
        /// <returns></returns>
        string GetDiskDriveSerialNo(string specifiedDisk = null);
        /// <summary>
        /// 获取CPU序列号
        /// </summary>
        /// <returns></returns>
        string GetCPUSerialNo();
        string GetBaseBoardSerialNo();
        /// <summary>
        /// 获取硬盘ID
        /// </summary>
        /// <returns></returns>
        string GetOSHardDiskID(bool returnOriginalString = false);

        string GetMechineNum();
    }
}
