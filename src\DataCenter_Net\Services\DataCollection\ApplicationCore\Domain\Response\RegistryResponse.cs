﻿using SDHD.DC.Utilities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SDHD.DC.DataCollection.ApplicationCore.Domain.Response
{
    public class RegistryResponse : BizResponse<RegistryResponseContent>
    {

    }
    public class RegistryResponseContent
    {
        public bool IsAuthed { set; get; }
        public string UserName { set; get; }
        public string AccessToken { set; get; }
        public int Permissions { set; get; }
    }
}
