﻿using NPOI.HSSF.Record;
using SDHD.DC.DataCollection.ApplicationCore.Domain.Entities;
using SDHD.DC.DataCollection.ApplicationCore.Domain.Request;
using SDHD.DC.DataCollection.ApplicationCore.Domain.Response;
using SDHD.DC.DataCollection.ApplicationCore.Interfaces.Repository;
using SDHD.DC.DataCollection.ApplicationCore.Interfaces.Service;
using SDHD.DC.Utilities;
using SDHD.DC.Utilities.Constants;
using SDHD.DC.Utilities.Logging;
using SDHD.DC.Utilities.ServiceExtensions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SDHD.DC.DataCollection.ApplicationCore.Services
{
    public class SystemService : ISystemService
    {
        private readonly ISystemRepository _SystemRepository;
        public readonly IRedisRepository _RedisRepository;
        public readonly IWellStationRepository _WellStationRepository;
        public readonly ISystemHandler _SystemHandler;
        public SystemService()
        {
            _SystemRepository = ServiceHelper.GetService<ISystemRepository>();
            _RedisRepository = ServiceHelper.GetService<IRedisRepository>();
            _WellStationRepository = ServiceHelper.GetService<IWellStationRepository>();
            _SystemHandler = ServiceHelper.GetService<ISystemHandler>();
        }
        public async Task<SysConfigResponse> GetSysConfig()
        {
            var response = new SysConfigResponse();
            var sysConfig = await _SystemRepository.GetSysConfig();
            response.Content.UnitName = sysConfig.UnitName;
            response.Content.DefaultWellStationId = sysConfig.DefaultWellStationID;
            response.Success();
            return response;
        }
        public async Task<BizResponse> UpdateSysToDefaultWellStationId(string wellStationId)
        {
            var response = new BizResponse();
            var sysConfig = await _SystemRepository.GetRegisterData();
            sysConfig.lastPro = wellStationId;
            await _SystemRepository.UpdateSysConfig(sysConfig);
            response.Success();
            return response;
        }
        #region register auth
        public async Task<RegistryResponse> Register(RegistryRequest request)
        {
            RegistryResponse response = new RegistryResponse();

            var registerData = request.Content;
            //取消注册验证
            //await _SystemRepository.GetRegisterData();
            //if (string.IsNullOrWhiteSpace(registerData.UserName))
            //{
            //    response.Fail(Errors.UserNameRequired);
            //    return response;
            //}
            //if (string.IsNullOrWhiteSpace(registerData.VerifyCode))
            //{
            //    response.Fail(Errors.VerifyCodeInvalid);
            //    return response;
            //}
            //if (string.IsNullOrWhiteSpace(registerData.VerifyCode))
            //{
            //    response.Fail(Errors.AuthCodeRequired);
            //    return response;
            //}
            //取消注册验证
            //if (registerData.VerifyCode != GenerateVerifyCode())
            //{
            //    response.Fail(Errors.VerifyCodeInvalid);
            //    return response;
            //}
            //if (!VerifyAuthCode(registerData.UserName, registerData.VerifyCode, registerData.AuthCode, out PermissionType sysPermission))
            //{
            //    response.Fail(Errors.AuthCodeInvalid);
            //    return response;
            //}

            //save the register data
            //await _SystemRepository.SaveRegisterData(new Domain.Entities.RegisterData()
            //{
            //    UserName = registerData.UserName,
            //    AuthCode = registerData.AuthCode
            //});
            response.Success();
            response.Content.IsAuthed = true;
            response.Content.UserName = registerData.UserName;
            response.Content.AccessToken = registerData.AuthCode;
            return response;
        }

        public async Task<RegistryResponse> SetRegister(RegistryRequest request)
        {
            RegistryResponse response = new RegistryResponse();
            var registerData = request.Content;
            //await _SystemRepository.GetRegisterData();
            if (string.IsNullOrWhiteSpace(registerData.UserName))
            {
                response.Fail(Errors.UserNameRequired);
                return response;
            }
            if (string.IsNullOrWhiteSpace(registerData.VerifyCode))
            {
                response.Fail(Errors.VerifyCodeInvalid);
                return response;
            }
            if (string.IsNullOrWhiteSpace(registerData.AuthCode))
            {
                response.Fail(Errors.AuthCodeRequired);
                return response;
            }


            //save the register data
            response.Content.IsAuthed = await _SystemRepository.SaveRegisterData(new Domain.Entities.RegisterData()
            {
                UserName = registerData.UserName,
                AuthCode = registerData.AuthCode
            });
            response.Success();
            response.Content.UserName = registerData.UserName;
            response.Content.AccessToken = registerData.AuthCode;
            return response;
        }
        private string GenerateVerifyCode()
        {
            //var hardDiskSerNo = _SystemHandler.GetOSHardDiskID();
            //var cpuSerNo = _SystemHandler.GetCPUSerialNo();
            //string content = cpuSerNo + hardDiskSerNo;
            //content = StringHandler.MD5Encrypt32(content).Substring(8, 16);//only return 16

            string content = _SystemHandler.GetMechineNum();
            return content;
        }
        private bool VerifyAuthCode(string userName, string verifyCode, string authCode, out PermissionType permissions)
        {
            permissions = 0;
            try
            {
                if (string.IsNullOrEmpty(authCode) || !authCode.StartsWith("REG-") || authCode.Length != ("REG-".Length + 50))
                {
                    return false;
                }
                string c = authCode.Substring("REG-".Length);
                c = string.Join("", c.Reverse());
                var sChars = new char[10];
                int sId = 2;
                for (int i = 0; i < sChars.Length; i++)
                {
                    sChars[i] = c[sId];
                    c = c.Remove(sId, 1);
                    sId = sId + 2;
                }
                var s = string.Join("", sChars.Reverse());
                byte[] dataBytes = StringHandler.ConvertToHexBytes(c);
                byte[] pBytes = new byte[4];
                var d = dataBytes.Reverse().ToList();
                pBytes[0] = d[2]; d.RemoveAt(2);
                pBytes[1] = d[6]; d.RemoveAt(6);
                pBytes[2] = d[10]; d.RemoveAt(10);
                pBytes[3] = d[14]; d.RemoveAt(14);
                pBytes = pBytes.Reverse().ToArray();
                uint cP = BitConverter.ToUInt32(pBytes);
                if ((cP & 0x80000001) != 0x80000001)
                {
                    return false;
                }
                uint aP = (cP ^ 0x80000001) >> 1;
                string checkMd5 = BitConverter.ToString(d.ToArray()).Replace("-", "");
                string strP = BitConverter.ToString(pBytes).Replace("-", "");
                string content = string.Join(';', userName, verifyCode, strP, s);
                string md5 = StringHandler.MD5Encrypt32(content);
                if (checkMd5 == md5)
                {
                    permissions = (PermissionType)aP;
                    return true;
                }
            }
            catch
            {
                return false;
            }
            return false;
        }
        public async Task<RegistryDetailResponse> GetRegisterData()
        {
            var response = new RegistryDetailResponse();
            var registerData = await _SystemRepository.GetRegisterData();
            if (registerData != null)
            {
                response.Content.UserName = registerData.UserName;
                response.Content.AuthCode = registerData.AuthCode;
                response.Content.VerifyCode = registerData.machineCode.ToString();
                response.Content.LastPro = registerData.lastPro;
            }
            //response.Content.VerifyCode = GenerateVerifyCode();
            response.Success();
            return response;
        }
        #endregion
        public async Task<ServerConnectTestResponse> TestServerConnection(ServerConnectTestRequest request)
        {
            ServerConnectTestResponse response = new ServerConnectTestResponse();
            var canConnect = await _SystemRepository.TestServerConnection(request.Content.ServerIP, request.Content.Port, request.Content.ConnectionTimeout, out string error);
            response.Content.ConnectEnabled = canConnect;
            if (!canConnect)
            {
                response.Content.ErrorMsg = error;
            }
            response.Success();
            return response;
        }
        public async Task CloseServerConnection()
        {
            await _SystemRepository.CloseServerConnection();
        }
        public async Task<RedisConfigResponse> GetRedisConfig()
        {
            var response = new RedisConfigResponse();
            var config = await _RedisRepository.GetConfig();
            if (config != null)
            {
                var responseContent = response.Content;
                responseContent.IPAddr = config.IPAddr;
                responseContent.Port = config.Port;
                responseContent.Timeout = config.Timeout;
                responseContent.UserName = config.UserName;
                responseContent.Pwd = config.Pwd;
                response.Content = responseContent;
            }
            response.Success();
            return response;
        }
        public async Task<BizResponse> UpdateRedisConfig(RedisConfigUpdateRequest request)
        {
            var response = new RedisConfigResponse();
            var requestContent = request.Content;
            var config = await _RedisRepository.GetConfig();
            bool isAnyServerSettingChanged = false;
            if (config == null)
            {
                config = new Domain.Entities.RedisConfig();
                isAnyServerSettingChanged = true;
            }
            else
            {
                if (config.IPAddr != requestContent.IPAddr
                    || config.Port != requestContent.Port
                    || config.Port != requestContent.Port
                    || config.Timeout != requestContent.Timeout
                    || config.UserName != requestContent.UserName
                    || config.Pwd != requestContent.Pwd)
                {
                    isAnyServerSettingChanged = true;
                }
            }
            if (!isAnyServerSettingChanged)
            {
                response.Success();
                return response;
            }
            config.IPAddr = requestContent.IPAddr;
            config.Port = requestContent.Port;
            config.Timeout = requestContent.Timeout;
            config.UserName = requestContent.UserName;
            config.Pwd = requestContent.Pwd;
            await _RedisRepository.UpdateConfig(config);
            response.Success();
            return response;
        }
        #region sys external
        public async Task<string> GetDefaultWellStationTemplateId()
        {
            var sysExternal = await _SystemRepository.GetSysExternal();

            string defaultWellStationTemplateId = null;
            if (sysExternal != null)
            {
                defaultWellStationTemplateId = sysExternal.DefaultWellStationTemplateId;
            }
            if (string.IsNullOrEmpty(defaultWellStationTemplateId))
            {
                defaultWellStationTemplateId = DefaultValues.WellStationSystemTemplateId;
            }
            return defaultWellStationTemplateId;
        }
        public async Task<BizResponse> UpdateDefaultWellStationTemplateId(string defaultWellStationTemplateId)
        {
            var response = new BizResponse();
            var sysExternal = await _SystemRepository.GetSysExternal();
            if (sysExternal == null)
            {
                sysExternal = new SysExternal();
            }
            sysExternal.DefaultWellStationTemplateId = defaultWellStationTemplateId;
            await _SystemRepository.UpdateSysExternal(sysExternal);
            response.Success();
            return response;
        }
        public async Task<string> GetSysLanguage()
        {
            var sysExternal = await _SystemRepository.GetSysExternal();
            string sysLanguage = null;
            if (sysExternal != null)
            {
                sysLanguage = sysExternal.SysLanguage;
            }
            if (string.IsNullOrEmpty(sysLanguage))
            {
                sysLanguage = DefaultValues.SysLanguage;
            }
            return sysLanguage;
        }
        public async Task<BizResponse> UpdateSysLanguage(string sysLanguage)
        {
            var response = new BizResponse();
            var sysExternal = await _SystemRepository.GetSysExternal();
            if (sysExternal == null)
            {
                sysExternal = new SysExternal();
            }
            sysExternal.SysLanguage = sysLanguage;
            await _SystemRepository.UpdateSysExternal(sysExternal);
            response.Success();
            return response;
        }
        #endregion

        #region 变更代码 Ahri
        public void GetRegisterDataay()
        {
            _SystemRepository.GetRegisterDataay();
        }
        public void GetNationalStandardGasProductionParametersay()
        {
            _SystemRepository.GetNationalStandardGasProductionParametersay();
        }
        public async Task GetNationalStandardGasProductionParametersData()
        {
            await _SystemRepository.GetNationalStandardGasProductionParametersData();
        }
        public List<GasParameterData> GetNationalStandardGasProductionParameters()
        {
            return _SystemRepository.GetNationalStandardGasProductionParameters();
        }
        public List<GasParameterMBData> GetNationalStandardGasProductionParametersMB()
        {
            return _SystemRepository.GetNationalStandardGasProductionParametersMB();
        }
        public List<GasParameterSandData> GetNationalStandardGasProductionParametersSand()
        {
            return _SystemRepository.GetNationalStandardGasProductionParametersSand();
        }
        public bool SetNationalStandardGasProductionParameters(List<GasParameterData> gasParameterDatas)
        {
            return _SystemRepository.SetNationalStandardGasProductionParameters(gasParameterDatas);
        }

        public bool SetNationalStandardGasProductionParametersMB(List<GasParameterMBData> gasParameterDatas)
        {
            return _SystemRepository.SetNationalStandardGasProductionParametersMB(gasParameterDatas);
        }

        public bool SetNationalStandardGasProductionParametersSand(List<GasParameterSandData> gasParameterDatas)
        {
            return _SystemRepository.SetNationalStandardGasProductionParametersSand(gasParameterDatas);
        }

        public bool SetUploadConfig(UploadConfig uploadConfig)
        {
            return _SystemRepository.SetUploadConfig(uploadConfig);
        }

        public bool SetUploadConfigZSH(UploadConfigZSH uploadConfigZSH)
        {
            return _SystemRepository.SetUploadConfigZSH(uploadConfigZSH);
        }

        public async Task<UploadConfig> GetUploadConfig(bool reload = false)
        {
            return await _SystemRepository.GetUploadConfig(reload);
        }


        public async Task<List<Rank>> GetRanks()
        {
            return await _SystemRepository.GetRanks();
        }

        public async Task<bool> UpdateRanks(List<Rank> ranks)
        {
            return await _SystemRepository.UpdateRanks(ranks);
        }
        public List<Menu> GetDefaultMenus(int depth = 1)
        {
            return _SystemRepository.GetDefaultMenus(depth);
        }

        public async Task<List<string>> GetWebUrlConfig()
        {
            return await _SystemRepository.GetWebUrlConfig();
        }

        public async Task<bool> SaveWebUrlConfig(List<string> urls)
        {
            return await _SystemRepository.SaveWebUrlConfig(urls);
        }


        #endregion 变更代码 Ahri
    }
}
