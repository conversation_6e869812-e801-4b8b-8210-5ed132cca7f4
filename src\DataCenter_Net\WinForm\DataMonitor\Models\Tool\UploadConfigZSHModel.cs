﻿using SDHD.DC.DataCollection.ApplicationCore.Domain.Entities;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SDHD.DC.DataMonitor.Models.Tool
{
    public class UploadConfigZSHModel : NotifyPropertyChangedModel
    {
        public string WellStationId { set; get; }
        public string WellNumberKeyWord { set; get; }
        public ObservableCollection<SensorInfo> Sensors { set; get; } = new();
        public string StartDate
        {
            get
            {
                return StartDatePicker != null ? $"{StartDatePicker.Value:yyyy-MM-dd}" : string.Empty;
            }
        }
        public DateTime? StartDatePicker { set; get; }
        public DateTime? MaxDatePicker { set; get; }
        public string StartTimeHour { set; get; }
        public string StartTimeMinute { set; get; }
        public string CurrentUploadDateTime { set; get; }

        private UploadConfigZSH _Process;
        public UploadConfigZSH Process
        {
            get { return _Process; }
            set
            {
                _Process = value;
                OnPropertyChanged(nameof(Process));
            }
        }

        private ObservableCollection<UploadConfigZSH> _ProcessList = new();
        public ObservableCollection<UploadConfigZSH> ProcessList
        {
            get { return _ProcessList; }
            set
            {
                _ProcessList = value;
                OnPropertyChanged(nameof(ProcessList));
            }
        }

        private ObservableCollection<ZSHJHItem> _ZSHJHList = new();
        public ObservableCollection<ZSHJHItem> ZSHJHList
        {
            get { return _ZSHJHList; }
            set
            {
                _ZSHJHList = value;
                OnPropertyChanged(nameof(ZSHJHList));
            }
        }

        private ObservableCollection<ZSHCHItem> _ZSHCHList = new();
        public ObservableCollection<ZSHCHItem> ZSHCHList
        {
            get { return _ZSHCHList; }
            set
            {
                _ZSHCHList = value;
                OnPropertyChanged(nameof(ZSHCHList));
            }
        }

        public static ObservableCollection<string> LBMSList { get { return new ObservableCollection<string> { "套管射孔完井", "页岩气井", "含硫气井", }; } }
        public static ObservableCollection<string> SYFSList { get { return new ObservableCollection<string> { "01-油管", "02-套管", "03-油套混采", }; } }
        public static ObservableCollection<string> CZXMList { get { return new ObservableCollection<string> { "03-关压恢复", "05-关井", "06-试采", "11-配合射孔作业", "12-油嘴控制开井排", "13-针阀控制开井排", "14-系统测试", "15-敞井", "16-临界速度流量计", "17-丹尼尔高级孔板", "18-垫圈流量计求产", "19-双波纹压差求产", "07-配合连油作业", "08-配合压裂作业", "09-开滑套", "10-打捞钥匙", }; } }
    }
}
