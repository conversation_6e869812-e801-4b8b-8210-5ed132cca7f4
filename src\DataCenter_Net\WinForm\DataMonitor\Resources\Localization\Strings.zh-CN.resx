<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AppTitle" xml:space="preserve">
    <value>现场数据采集系统</value>
  </data>
  <data name="Welcome" xml:space="preserve">
    <value>欢迎使用现场数据采集系统</value>
  </data>
  <data name="MenuFile" xml:space="preserve">
    <value>文件</value>
  </data>
  <data name="MenuEdit" xml:space="preserve">
    <value>编辑</value>
  </data>
  <data name="MenuView" xml:space="preserve">
    <value>视图</value>
  </data>
  <data name="MenuTools" xml:space="preserve">
    <value>工具</value>
  </data>
  <data name="MenuHelp" xml:space="preserve">
    <value>帮助</value>
  </data>
  <data name="MenuSettings" xml:space="preserve">
    <value>设置</value>
  </data>
  <data name="MenuExit" xml:space="preserve">
    <value>退出</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="MenuCurrentProject" xml:space="preserve">
    <value>井场</value>
  </data>
  <data name="MenuAlarm" xml:space="preserve">
    <value>报警</value>
  </data>
  <data name="MenuWindow" xml:space="preserve">
    <value>窗口</value>
  </data>
  <data name="MenuDeleteCurrentMonitor" xml:space="preserve">
    <value>删除仪表盘</value>
  </data>
  <data name="ButtonOk" xml:space="preserve">
    <value>确定</value>
  </data>
  <data name="ButtonCancel" xml:space="preserve">
    <value>取消</value>
  </data>
  <data name="ButtonSave" xml:space="preserve">
    <value>保存</value>
  </data>
  <data name="ButtonClose" xml:space="preserve">
    <value>关闭</value>
  </data>
  <data name="ButtonApply" xml:space="preserve">
    <value>应用</value>
  </data>
  <data name="SettingsTitle" xml:space="preserve">
    <value>系统配置</value>
  </data>
  <data name="LanguageSettings" xml:space="preserve">
    <value>语言</value>
  </data>
  <data name="SelectLanguage" xml:space="preserve">
    <value>选择语言：</value>
  </data>
  <data name="LoadingData" xml:space="preserve">
    <value>正在加载数据...</value>
  </data>
  <data name="DataSaved" xml:space="preserve">
    <value>数据保存成功</value>
  </data>
  <data name="ErrorOccurred" xml:space="preserve">
    <value>发生错误</value>
  </data>
  <data name="ConnectionError" xml:space="preserve">
    <value>连接错误</value>
  </data>
  <data name="MenuNewProject" xml:space="preserve">
    <value>新建井场</value>
  </data>
  <data name="MenuOpenHistoryProject" xml:space="preserve">
    <value>打开井场</value>
  </data>
  <data name="MenuRefreshHistoryProject" xml:space="preserve">
    <value>刷新列表</value>
  </data>
  <data name="MenuStartCollect" xml:space="preserve">
    <value>开始采集</value>
  </data>
  <data name="MenuStopCollect" xml:space="preserve">
    <value>停止采集</value>
  </data>
  <data name="MenuNewGraphMonitor" xml:space="preserve">
    <value>新建仪表盘</value>
  </data>
  <data name="MenuOpenGraphMonitor" xml:space="preserve">
    <value>打开仪表盘</value>
  </data>
  <data name="MenuProjectLog" xml:space="preserve">
    <value>运行日志</value>
  </data>
  <data name="MenuExport" xml:space="preserve">
    <value>导出</value>
  </data>
  <data name="MenuExportPanorama" xml:space="preserve">
    <value>完整数据集</value>
  </data>
  <data name="MenuExportTimePeriod" xml:space="preserve">
    <value>时段数据</value>
  </data>
  <data name="MenuHistoricalDataQuery" xml:space="preserve">
    <value>历史分析</value>
  </data>
  <data name="MenuSensorSettings" xml:space="preserve">
    <value>传感器</value>
  </data>
  <data name="MenuProjectSettings" xml:space="preserve">
    <value>井场设置</value>
  </data>
  <data name="MenuMeasurementUnitSettings" xml:space="preserve">
    <value>单位系统</value>
  </data>
  <data name="MenuUnitTypeListSettings" xml:space="preserve">
    <value>单位类型设置</value>
  </data>
  <data name="MenuTCPNetworkDeviceSettings" xml:space="preserve">
    <value>TCP/IP设备</value>
  </data>
  <data name="MenuRedisServiceSettings" xml:space="preserve">
    <value>Redis服务</value>
  </data>
  <data name="MenuModBusDeviceSettings" xml:space="preserve">
    <value>ModBus设备</value>
  </data>
  <data name="MenuModBusTCPDataSettings" xml:space="preserve">
    <value>ModBus TCP</value>
  </data>
  <data name="MenuS7PointTableSettings" xml:space="preserve">
    <value>S7地址</value>
  </data>
  <data name="MenuInterlockRuleSettings" xml:space="preserve">
    <value>安全联锁</value>
  </data>
  <data name="MenuGBGasProductionParameterSettings" xml:space="preserve">
    <value>GB流量参数</value>
  </data>
  <data name="MenuUSGasProductionParameterSettings" xml:space="preserve">
    <value>US流量参数</value>
  </data>
  <data name="MenuSandVolumeParameterSettings" xml:space="preserve">
    <value>砂量分析</value>
  </data>
  <data name="MenuLanguageSettings" xml:space="preserve">
    <value>语言</value>
  </data>
  <data name="MenuAlarmSettings" xml:space="preserve">
    <value>报警阈值</value>
  </data>
  <data name="MenuAlarmInformation" xml:space="preserve">
    <value>当前报警</value>
  </data>
  <data name="MenuAlarmLog" xml:space="preserve">
    <value>报警历史</value>
  </data>
  <data name="MenuVerticalWindowArrangement" xml:space="preserve">
    <value>垂直布局</value>
  </data>
  <data name="MenuHorizontalWindowArrangement" xml:space="preserve">
    <value>水平布局</value>
  </data>
  <data name="MenuCloseAllWindows" xml:space="preserve">
    <value>全部关闭</value>
  </data>
  <data name="MenuSetAsFloatingWindow" xml:space="preserve">
    <value>浮动窗口</value>
  </data>
  <data name="MenuConfigureWebWindow" xml:space="preserve">
    <value>Web界面</value>
  </data>
  <data name="MenuRegister" xml:space="preserve">
    <value>许可证</value>
  </data>
  <data name="ButtonStartCollection" xml:space="preserve">
    <value>开始</value>
  </data>
  <data name="ButtonStopCollection" xml:space="preserve">
    <value>停止</value>
  </data>
  <data name="ButtonSensorSettings" xml:space="preserve">
    <value>传感器</value>
  </data>
  <data name="ButtonDataMonitor" xml:space="preserve">
    <value>监控</value>
  </data>
  <data name="Button3DFlowDiagram" xml:space="preserve">
    <value>3D视图</value>
  </data>
  <data name="Button2DFlowDiagram" xml:space="preserve">
    <value>2D视图</value>
  </data>
  <data name="ButtonLaserPlatform" xml:space="preserve">
    <value>激光系统</value>
  </data>
  <data name="LabelServiceNotStarted" xml:space="preserve">
    <value>服务未启动</value>
  </data>
  <data name="LabelSoftwareNotRegistered" xml:space="preserve">
    <value>需要许可证</value>
  </data>
  <data name="ButtonStartService" xml:space="preserve">
    <value>启动服务</value>
  </data>
  <data name="LabelCurrentProject" xml:space="preserve">
    <value>井场:</value>
  </data>
  <!-- Help Menu Items -->
  <data name="MenuRegister" xml:space="preserve">
    <value>注册</value>
  </data>
  <data name="MenuLanguageSettings" xml:space="preserve">
    <value>语言设置</value>
  </data>
  <data name="MenuLanguageEnglish" xml:space="preserve">
    <value>英文</value>
  </data>
  <data name="MenuLanguageChinese" xml:space="preserve">
    <value>中文</value>
  </data>
  <data name="MenuLanguageRussian" xml:space="preserve">
    <value>俄文</value>
  </data>
  <!-- AlarmListControl Column Headers -->
  <data name="AlarmSensorName" xml:space="preserve">
    <value>传感器名称</value>
  </data>
  <data name="AlarmLevel" xml:space="preserve">
    <value>报警等级</value>
  </data>
  <data name="AlarmCondition" xml:space="preserve">
    <value>条件</value>
  </data>
  <data name="AlarmValue" xml:space="preserve">
    <value>报警值</value>
  </data>
  <data name="AlarmRealTimeValue" xml:space="preserve">
    <value>实时值</value>
  </data>
  <data name="AlarmContent" xml:space="preserve">
    <value>报警内容</value>
  </data>
  <data name="AlarmStatus" xml:space="preserve">
    <value>状态</value>
  </data>
  <data name="AlarmStartTime" xml:space="preserve">
    <value>开始时间</value>
  </data>
  <data name="AlarmStopPlay" xml:space="preserve">
    <value>停止播放</value>
  </data>
  
  <!-- Alarm Level Names -->
  <data name="AlarmLevelCritical" xml:space="preserve">
    <value>严重</value>
  </data>
  <data name="AlarmLevelHigh" xml:space="preserve">
    <value>高</value>
  </data>
  <data name="AlarmLevelMedium" xml:space="preserve">
    <value>中等</value>
  </data>
  <data name="AlarmLevelLow" xml:space="preserve">
    <value>低</value>
  </data>
  
  <!-- Alarm Condition Symbols -->
  <data name="ConditionGreaterThan" xml:space="preserve">
    <value>&gt;</value>
  </data>
  <data name="ConditionGreaterEqual" xml:space="preserve">
    <value>&gt;=</value>
  </data>
  <data name="ConditionLessThan" xml:space="preserve">
    <value>&lt;</value>
  </data>
  <data name="ConditionLessEqual" xml:space="preserve">
    <value>&lt;=</value>
  </data>
  <data name="ConditionEqual" xml:space="preserve">
    <value>=</value>
  </data>
  <data name="ConditionNotEqual" xml:space="preserve">
    <value>&lt;&gt;</value>
  </data>
  
  <!-- Warning Type Descriptions -->
  <data name="WarningTypeLessThanLower" xml:space="preserve">
    <value>小于下限</value>
  </data>
  <data name="WarningTypeGreaterThanUpper" xml:space="preserve">
    <value>大于上限</value>
  </data>
  <data name="WarningTypeLessEqualLower" xml:space="preserve">
    <value>&lt;=下限</value>
  </data>
  <data name="WarningTypeGreaterEqualUpper" xml:space="preserve">
    <value>&gt;=上限</value>
  </data>
  <data name="WarningTypeEqualValue" xml:space="preserve">
    <value>等于值</value>
  </data>
  <data name="WarningTypeUnknown" xml:space="preserve">
    <value>未知类型</value>
  </data>

  <!-- SensorAlarmConfigControl Strings -->
  <data name="SensorAlarmConfigList" xml:space="preserve">
    <value>传感器报警配置列表</value>
  </data>
  <data name="AddMenuItem" xml:space="preserve">
    <value>添加</value>
  </data>
  <data name="DeleteMenuItem" xml:space="preserve">
    <value>删除</value>
  </data>
  <data name="ColumnSN" xml:space="preserve">
    <value>序列号</value>
  </data>
  <data name="ColumnID" xml:space="preserve">
    <value>编号</value>
  </data>
  <data name="ColumnName" xml:space="preserve">
    <value>名称</value>
  </data>
  <data name="SensorInfo" xml:space="preserve">
    <value>传感器信息</value>
  </data>
  <data name="LabelSN" xml:space="preserve">
    <value>序列号:</value>
  </data>
  <data name="LabelID" xml:space="preserve">
    <value>编号:</value>
  </data>
  <data name="LabelName" xml:space="preserve">
    <value>名称:</value>
  </data>
  <data name="ThresholdAlarmRules" xml:space="preserve">
    <value>阈值报警规则</value>
  </data>
  <data name="ColumnSeverity" xml:space="preserve">
    <value>严重程度</value>
  </data>
  <data name="ColumnOperator" xml:space="preserve">
    <value>运算符</value>
  </data>
  <data name="ColumnThreshold" xml:space="preserve">
    <value>阈值</value>
  </data>
  <data name="ColumnDeadband" xml:space="preserve">
    <value>死区</value>
  </data>
  <data name="ColumnMessage" xml:space="preserve">
    <value>消息</value>
  </data>
  <data name="TrendAlarmRules" xml:space="preserve">
    <value>趋势报警规则</value>
  </data>
  <data name="ColumnRateOfChange" xml:space="preserve">
    <value>变化率</value>
  </data>
  <data name="ColumnTimeWindow" xml:space="preserve">
    <value>时间窗口(秒)</value>
  </data>
  <data name="ColumnDirection" xml:space="preserve">
    <value>方向</value>
  </data>
  
  <!-- ThresholdAlarmRuleEditor Strings -->
  <data name="LabelAlarmLevel" xml:space="preserve">
    <value>报警级别:</value>
  </data>
  <data name="LabelComparisonOperator" xml:space="preserve">
    <value>比较运算符:</value>
  </data>
  <data name="LabelThreshold" xml:space="preserve">
    <value>阈值:</value>
  </data>
  <data name="LabelDeadband" xml:space="preserve">
    <value>死区值:</value>
  </data>
  <data name="LabelAlarmMessage" xml:space="preserve">
    <value>报警消息:</value>
  </data>
  <data name="OperatorGreaterThan" xml:space="preserve">
    <value>大于 (&gt;)</value>
  </data>
  <data name="OperatorGreaterEqual" xml:space="preserve">
    <value>大于等于 (&gt;=)</value>
  </data>
  <data name="OperatorLessThan" xml:space="preserve">
    <value>小于 (&lt;)</value>
  </data>
  <data name="OperatorLessEqual" xml:space="preserve">
    <value>小于等于 (&lt;=)</value>
  </data>
  <data name="OperatorEqual" xml:space="preserve">
    <value>等于 (=)</value>
  </data>
  <data name="OperatorNotEqual" xml:space="preserve">
    <value>不等于 (&lt;&gt;)</value>
  </data>
  
  <!-- TrendAlarmRuleEditor Strings -->
  <data name="LabelRateOfChange" xml:space="preserve">
    <value>变化率:</value>
  </data>
  <data name="LabelTimeWindow" xml:space="preserve">
    <value>时间窗口(秒):</value>
  </data>
  <data name="LabelTrendDirection" xml:space="preserve">
    <value>趋势方向:</value>
  </data>
  <data name="DirectionRising" xml:space="preserve">
    <value>上升</value>
  </data>
  <data name="DirectionFalling" xml:space="preserve">
    <value>下降</value>
  </data>
  <data name="DirectionBoth" xml:space="preserve">
    <value>双向</value>
  </data>
  
  <!-- UserLoginWindow Strings -->
  <data name="UserLoginTitle" xml:space="preserve">
    <value>用户登录</value>
  </data>
  <data name="LabelUsername" xml:space="preserve">
    <value>用户名：</value>
  </data>
  <data name="LabelPassword" xml:space="preserve">
    <value>密码：</value>
  </data>
  
  <!-- SensorListWindow Strings -->
  <data name="SensorListTitle" xml:space="preserve">
    <value>传感器列表</value>
  </data>
  <data name="ButtonAddModule" xml:space="preserve">
    <value>手动添加模块</value>
  </data>
  <data name="ButtonEditModule" xml:space="preserve">
    <value>编辑模块属性</value>
  </data>
  <data name="ButtonDeleteSensor" xml:space="preserve">
    <value>删除传感器</value>
  </data>
  <data name="ButtonViewDeviceList" xml:space="preserve">
    <value>查看设备列表</value>
  </data>
  <data name="ButtonViewDeviceStatus" xml:space="preserve">
    <value>查看设备状态</value>
  </data>
  <data name="ColumnModuleSN" xml:space="preserve">
    <value>模块编号</value>
  </data>
  <data name="ColumnSensorAlias" xml:space="preserve">
    <value>传感器别名</value>
  </data>
  <data name="ColumnType" xml:space="preserve">
    <value>类型</value>
  </data>
  <data name="ColumnIsEnabled" xml:space="preserve">
    <value>是否启用</value>
  </data>
  <data name="ColumnMeasureUnit" xml:space="preserve">
    <value>计量单位</value>
  </data>
  <data name="ColumnRangeMin" xml:space="preserve">
    <value>量程Min</value>
  </data>
  <data name="ColumnRangeMax" xml:space="preserve">
    <value>量程Max</value>
  </data>
  <data name="ColumnCalculationMethod" xml:space="preserve">
    <value>计算方式</value>
  </data>
  <data name="ColumnDefaultColor" xml:space="preserve">
    <value>默认颜色</value>
  </data>
  
  <!-- WellStationSettingWindow Strings -->
  <data name="WellStationSettingTitle" xml:space="preserve">
    <value>井场设置</value>
  </data>
  <data name="LabelWellStationID" xml:space="preserve">
    <value>井场ID：</value>
  </data>
  <data name="LabelCreateTime" xml:space="preserve">
    <value>创建时间：</value>
  </data>
  <data name="LabelWellStationName" xml:space="preserve">
    <value>井场名称：</value>
  </data>
  <data name="LabelSamplingTime" xml:space="preserve">
    <value>采样时间：</value>
  </data>
  <data name="LabelDefaultIs" xml:space="preserve">
    <value>（默认为</value>
  </data>
  <data name="LabelCloseParenthesis" xml:space="preserve">
    <value>）</value>
  </data>
  <data name="LabelCollectIntervalNote" xml:space="preserve">
    <value>若修改了采集时间频率，需要重启软件和服务，才可生效。</value>
  </data>
  
  <!-- DataMonitorWindow Strings -->
  <data name="DataMonitorTitle" xml:space="preserve">
    <value>数据监视</value>
  </data>
  <data name="LabelStartTime" xml:space="preserve">
    <value>开始时间：</value>
  </data>
  <data name="LabelEndTime" xml:space="preserve">
    <value>结束时间：</value>
  </data>
  <data name="ButtonShow" xml:space="preserve">
    <value>显示</value>
  </data>
  <data name="MenuDisplaySensorInChart" xml:space="preserve">
    <value>在图中显示该传感器</value>
  </data>
  <data name="MenuRemoveFromChart" xml:space="preserve">
    <value>从图中移除</value>
  </data>
  <data name="MenuSetColor" xml:space="preserve">
    <value>设置颜色</value>
  </data>
  <data name="MenuMoveUp" xml:space="preserve">
    <value>向上移动</value>
  </data>
  <data name="MenuMoveDown" xml:space="preserve">
    <value>向下移动</value>
  </data>
  <data name="MenuSensorSettings" xml:space="preserve">
    <value>传感器设置</value>
  </data>
  <data name="MenuModifyLastValue" xml:space="preserve">
    <value>修改最后一个值</value>
  </data>
  <data name="MenuHideSensor" xml:space="preserve">
    <value>隐藏该传感器</value>
  </data>
  <data name="MenuShowSensor" xml:space="preserve">
    <value>不隐藏该传感器</value>
  </data>
  <data name="MenuShowAllHiddenSensors" xml:space="preserve">
    <value>显示(隐藏传感器)</value>
  </data>
  <data name="MenuHideAllHiddenSensors" xml:space="preserve">
    <value>不显示(隐藏传感器)</value>
  </data>
  <data name="MenuIndependentWindow" xml:space="preserve">
    <value>独立窗口显示</value>
  </data>
  <data name="ColumnStatus" xml:space="preserve">
    <value>状态</value>
  </data>
  <data name="ColumnCurrentValue" xml:space="preserve">
    <value>当前值</value>
  </data>
  
  <!-- UserListWindow Strings -->
  <data name="UserManagementTitle" xml:space="preserve">
    <value>用户管理</value>
  </data>
  <data name="ButtonAdd" xml:space="preserve">
    <value>添加</value>
  </data>
  <data name="ColumnUsername" xml:space="preserve">
    <value>用户名</value>
  </data>
  <data name="ColumnRankName" xml:space="preserve">
    <value>权限名称</value>
  </data>
  
  <!-- NewWellStationWindow Strings -->
  <data name="NewWellStationTitle" xml:space="preserve">
    <value>新建井场</value>
  </data>
  
  <!-- SensorDetailSettingWindow Strings -->
  <data name="SensorSettingTitle" xml:space="preserve">
    <value>传感器设置</value>
  </data>
  <data name="LabelProperties" xml:space="preserve">
    <value>的属性</value>
  </data>
  <data name="LabelCheckConnection" xml:space="preserve">
    <value>核对现场连接情况，然后进行传感器设置。</value>
  </data>
  <data name="LabelSamplingTime" xml:space="preserve">
    <value>采样时间：</value>
  </data>
  <data name="LabelSamplingTimeNote" xml:space="preserve">
    <value>默认0，则使用井场采样率。(使用填入10的整数倍）</value>
  </data>
  <data name="LabelSensorNaming" xml:space="preserve">
    <value>传感器命名</value>
  </data>
  <data name="LabelDataType" xml:space="preserve">
    <value>数据类型：</value>
  </data>
  <data name="LabelHighFrequencyDevice" xml:space="preserve">
    <value>高频数据设备</value>
  </data>
  <data name="LabelModuleSN" xml:space="preserve">
    <value>模块编号：</value>
  </data>
  <data name="LabelModuleSNNote" xml:space="preserve">
    <value>数模转换器上的硬件编号</value>
  </data>
  <data name="LabelSensorType" xml:space="preserve">
    <value>传感器类型：</value>
  </data>
  <data name="LabelAccumulateLiquid" xml:space="preserve">
    <value>在该传感器上累计移液量</value>
  </data>
  <data name="LabelBy" xml:space="preserve">
    <value>按</value>
  </data>
  <data name="LabelAccumulate" xml:space="preserve">
    <value>累计</value>
  </data>
  <data name="LabelSensorRange" xml:space="preserve">
    <value>传感器量程：</value>
  </data>
  <data name="LabelTo" xml:space="preserve">
    <value>至</value>
  </data>
  <data name="LabelADCRange" xml:space="preserve">
    <value>数模转换输出模块范围：</value>
  </data>
  <data name="LabelMeasureUnit" xml:space="preserve">
    <value>计量单位：</value>
  </data>
  <data name="LabelUserGroup" xml:space="preserve">
    <value>用户分组：</value>
  </data>
  <data name="LabelValidThreshold" xml:space="preserve">
    <value>有效临界值：</value>
  </data>
  <data name="LabelEnableValidThreshold" xml:space="preserve">
    <value>启用有效临界值</value>
  </data>
  <data name="LabelSerialNumber" xml:space="preserve">
    <value>序号：</value>
  </data>
  <data name="LabelSensorAdditionalProperties" xml:space="preserve">
    <value>传感器附加属性：</value>
  </data>
  <data name="LabelID" xml:space="preserve">
    <value>ID：</value>
  </data>
  <data name="LabelEnableModule" xml:space="preserve">
    <value>启用模块</value>
  </data>
  <data name="LabelHideSensor" xml:space="preserve">
    <value>隐藏传感器</value>
  </data>
  <data name="LabelStoreProductionParams" xml:space="preserve">
    <value>存储产量中间参数</value>
  </data>
  <data name="LabelEnableModuleNote" xml:space="preserve">
    <value>提示：只有启用模块后，才能采集并保存数据。</value>
  </data>
  <data name="LabelIcon" xml:space="preserve">
    <value>图标：</value>
  </data>
  <data name="LabelDecimalPlaces" xml:space="preserve">
    <value>数据小数点位：</value>
  </data>
  <data name="LabelDefaultLineColor" xml:space="preserve">
    <value>默认线条颜色：</value>
  </data>
  <data name="LabelMultiValueIndex" xml:space="preserve">
    <value>多值索引：</value>
  </data>
  <data name="LabelSensorCalibration" xml:space="preserve">
    <value>传感器标定</value>
  </data>
  <data name="LabelReading1" xml:space="preserve">
    <value>读数1：</value>
  </data>
  <data name="LabelReading2" xml:space="preserve">
    <value>读数2：</value>
  </data>
  <data name="LabelReading3" xml:space="preserve">
    <value>读数3：</value>
  </data>
  <data name="LabelReading4" xml:space="preserve">
    <value>读数4：</value>
  </data>
  <data name="LabelReading5" xml:space="preserve">
    <value>读数5：</value>
  </data>
  <data name="LabelReading6" xml:space="preserve">
    <value>读数6：</value>
  </data>
  <data name="LabelReading7" xml:space="preserve">
    <value>读数7：</value>
  </data>
  <data name="LabelActualValue" xml:space="preserve">
    <value>实际值：</value>
  </data>
  <data name="ButtonRead" xml:space="preserve">
    <value>读取</value>
  </data>
  <data name="LabelCalibrationNote" xml:space="preserve">
    <value>标定过程为根据现场标准检验台对该传感器各个点的测试取值，即前面为该传感器在校准台的当前读数，后面为实际的校准读数。</value>
  </data>
  <data name="LabelFormulaNote" xml:space="preserve">
    <value>计算公式中，"Input"表示传感器读数，可直接使用其它传感器的名称。</value>
  </data>
  <data name="LabelCommonFormulas" xml:space="preserve">
    <value>常用公式：</value>
  </data>
  <data name="LabelFormulaEditor" xml:space="preserve">
    <value>计算公式编写区（注意：公式区分大小写）：</value>
  </data>
  <data name="LabelFormulaAnalysis" xml:space="preserve">
    <value>公式分析状态以及计算校对：</value>
  </data>
  <data name="MenuInsertFunction" xml:space="preserve">
    <value>插入函数...</value>
  </data>
  <data name="MenuInsertSensorVariable" xml:space="preserve">
    <value>插入传感器变量...</value>
  </data>
  <data name="MenuInsertInput" xml:space="preserve">
    <value>插入Input（表示当前传感器读数）</value>
  </data>
  <data name="MenuInsertEnvVar" xml:space="preserve">
    <value>插入环境变量...</value>
  </data>
  <data name="ButtonPrevious" xml:space="preserve">
    <value>上一步</value>
  </data>
  <data name="ButtonNext" xml:space="preserve">
    <value>下一步</value>
  </data>
  <data name="ButtonComplete" xml:space="preserve">
    <value>完成</value>
  </data>
  
  <!-- SysLogWindow Strings -->
  <data name="SysLogTitle" xml:space="preserve">
    <value>操作日志</value>
  </data>
  <data name="ButtonSelectRange" xml:space="preserve">
    <value>选择范围</value>
  </data>
  <data name="ColumnLogList" xml:space="preserve">
    <value>日志列表</value>
  </data>
  <data name="ColumnTimeRange" xml:space="preserve">
    <value>时间范围</value>
  </data>
  <data name="ColumnTime" xml:space="preserve">
    <value>时间</value>
  </data>
  <data name="ColumnMessage" xml:space="preserve">
    <value>信息</value>
  </data>
  
  <!-- WellStationLogListWindow Strings -->
  <data name="WellStationLogTitle" xml:space="preserve">
    <value>井场日志</value>
  </data>
  <data name="ColumnWellStationLog" xml:space="preserve">
    <value>井场日志</value>
  </data>
  <data name="LabelLog" xml:space="preserve">
    <value>日志：</value>
  </data>
  <data name="ButtonAdd" xml:space="preserve">
    <value>添加</value>
  </data>
  <data name="ButtonModify" xml:space="preserve">
    <value>修改</value>
  </data>
  <data name="ButtonCancelModify" xml:space="preserve">
    <value>取消修改</value>
  </data>
  <data name="ButtonDelete" xml:space="preserve">
    <value>删除</value>
  </data>
  <data name="ButtonExportToExcel" xml:space="preserve">
    <value>导出为Excel</value>
  </data>
  
  <!-- WorkingStateListWindow Strings -->
  <data name="WorkingStateModifyTitle" xml:space="preserve">
    <value>工况修改</value>
  </data>
  <data name="MenuAddWorkingState" xml:space="preserve">
    <value>新增工况</value>
  </data>
  <data name="MenuInsertWorkingState" xml:space="preserve">
    <value>插入工况</value>
  </data>
  <data name="MenuDeleteWorkingState" xml:space="preserve">
    <value>删除工况</value>
  </data>
  <data name="ColumnWorkingStateProcess" xml:space="preserve">
    <value>工况流程</value>
  </data>
  <data name="ColumnStartTime" xml:space="preserve">
    <value>开始时间</value>
  </data>
  <data name="ColumnEndTime" xml:space="preserve">
    <value>结束时间</value>
  </data>
  <data name="LabelWorkingStateName" xml:space="preserve">
    <value>工况名称：</value>
  </data>
  <data name="LabelStartTime" xml:space="preserve">
    <value>开始时间：</value>
  </data>
  <data name="LabelEndTime" xml:space="preserve">
    <value>结束时间：</value>
  </data>
  <data name="ButtonSave" xml:space="preserve">
    <value>保存</value>
  </data>
  <data name="ButtonClearTime" xml:space="preserve">
    <value>清空时间</value>
  </data>
  
  <!-- DataRemarkListWindow Strings -->
  <data name="DataRemarkQueryExportTitle" xml:space="preserve">
    <value>数据标注查询及导出</value>
  </data>
  <data name="LabelKeyword" xml:space="preserve">
    <value>关键字：</value>
  </data>
  <data name="LabelSearchHint" xml:space="preserve">
    <value>(提示：空值时查询所有)</value>
  </data>
  <data name="ButtonSearch" xml:space="preserve">
    <value>查询</value>
  </data>
  <data name="ColumnSensor" xml:space="preserve">
    <value>传感器</value>
  </data>
  <data name="ColumnRemarkInfo" xml:space="preserve">
    <value>标注信息</value>
  </data>
  
  <!-- DataRemarkAddWindow Strings -->
  <data name="DataRemarkTitle" xml:space="preserve">
    <value>数据批注</value>
  </data>
  <data name="LabelDataTime" xml:space="preserve">
    <value>数据时间：</value>
  </data>
  <data name="LabelTextDescription" xml:space="preserve">
    <value>文本说明</value>
  </data>
  <data name="ButtonConfirmTextRemark" xml:space="preserve">
    <value>确定 (该标注为文本标注)</value>
  </data>
  
  <!-- WebWindowSetting Strings -->
  <data name="WebWindowSettingTitle" xml:space="preserve">
    <value>配置Web窗口</value>
  </data>
  <data name="LabelWebsite" xml:space="preserve">
    <value>网址：</value>
  </data>
  <data name="ColumnWebsite" xml:space="preserve">
    <value>网址</value>
  </data>
  <data name="ButtonOpen" xml:space="preserve">
    <value>打开</value>
  </data>
  
  <!-- RedisConfigSettingWindow Strings -->
  <data name="RedisConfigSettingTitle" xml:space="preserve">
    <value>Redis服务连接设置</value>
  </data>
  <data name="LabelServerIP" xml:space="preserve">
    <value>服务端IP地址：</value>
  </data>
  <data name="LabelPort" xml:space="preserve">
    <value>端口号：</value>
  </data>
  <data name="LabelConnectionTimeout" xml:space="preserve">
    <value>连接超时时间：</value>
  </data>
  <data name="LabelTimeoutUnit" xml:space="preserve">
    <value>(单位：秒；当值为0，则使用默认值。)</value>
  </data>
  <data name="LabelUsername" xml:space="preserve">
    <value>用户名：</value>
  </data>
  <data name="LabelPassword" xml:space="preserve">
    <value>密码：</value>
  </data>
  <data name="LabelShowPassword" xml:space="preserve">
    <value>显示密码</value>
  </data>
  <data name="LabelRestartServiceHint" xml:space="preserve">
    <value>提示：修改后请重启服务！</value>
  </data>
  
  <!-- AboutUsWindow Strings -->
  <data name="AboutSystemTitle" xml:space="preserve">
    <value>关于系统</value>
  </data>
  <data name="LabelTechnicalSupport" xml:space="preserve">
    <value>技术支持：</value>
  </data>
  <data name="LabelProductName" xml:space="preserve">
    <value>产品名称：</value>
  </data>
  <data name="LabelSoftwareVersion" xml:space="preserve">
    <value>软件版本：</value>
  </data>
  
  <!-- ExitAppWindow Strings -->
  <data name="ExitAppTitle" xml:space="preserve">
    <value>提示</value>
  </data>
  <data name="LabelConfirmExit" xml:space="preserve">
    <value>确定要退出软件吗？</value>
  </data>
  
  <!-- UserEditWindow Strings -->
  <data name="UserEditTitle" xml:space="preserve">
    <value>修改用户</value>
  </data>
  <data name="LabelRealName" xml:space="preserve">
    <value>姓名：</value>
  </data>
  <data name="LabelOptional" xml:space="preserve">
    <value>（非必填项）</value>
  </data>
  
  <!-- UserCreateWindow Strings -->
  <data name="UserCreateTitle" xml:space="preserve">
    <value>添加新用户</value>
  </data>
  <data name="LabelNewUsername" xml:space="preserve">
    <value>新用户名：</value>
  </data>
  <data name="LabelUsernameHint" xml:space="preserve">
    <value>（由数字和字母组成，长度范围3-30位）</value>
  </data>
  <data name="LabelPassword" xml:space="preserve">
    <value>密码：</value>
  </data>
  <data name="LabelPasswordHint" xml:space="preserve">
    <value>（由数字、字母、下划线组，长度范围3-30位）</value>
  </data>
  <data name="LabelConfirmPassword" xml:space="preserve">
    <value>确认密码：</value>
  </data>
  <data name="LabelPermissionLevel" xml:space="preserve">
    <value>权限等级：</value>
  </data>
  
  <!-- UserChangePasswordWindow Strings -->
  <data name="UserChangePasswordTitle" xml:space="preserve">
    <value>修改密码</value>
  </data>
  <data name="LabelOldPassword" xml:space="preserve">
    <value>旧密码：</value>
  </data>
  <data name="LabelNewPassword" xml:space="preserve">
    <value>新密码：</value>
  </data>
  <data name="LabelConfirmNewPassword" xml:space="preserve">
    <value>确认新密码：</value>
  </data>
  
  <!-- AlarmListWindow Strings -->
  <data name="AlarmListTitle" xml:space="preserve">
    <value>报警设置列表</value>
  </data>
  <data name="LabelDataType" xml:space="preserve">
    <value>数据类型：</value>
  </data>
  <data name="LabelDataName" xml:space="preserve">
    <value>数据名称：</value>
  </data>
  <data name="ButtonFilter" xml:space="preserve">
    <value>过滤</value>
  </data>
  <data name="ColumnIndex" xml:space="preserve">
    <value>序号</value>
  </data>
  <data name="ColumnName" xml:space="preserve">
    <value>名称</value>
  </data>
  <data name="ColumnLowAlarm" xml:space="preserve">
    <value>低报</value>
  </data>
  <data name="ColumnLowLowAlarm" xml:space="preserve">
    <value>低低报</value>
  </data>
  <data name="ColumnLowLowLowAlarm" xml:space="preserve">
    <value>低低低报</value>
  </data>
  <data name="ColumnHighAlarm" xml:space="preserve">
    <value>高报</value>
  </data>
  <data name="ColumnHighHighAlarm" xml:space="preserve">
    <value>高高报</value>
  </data>
  <data name="ColumnHighHighHighAlarm" xml:space="preserve">
    <value>高高高报</value>
  </data>
  <data name="ColumnEqualAlarm" xml:space="preserve">
    <value>等报</value>
  </data>
  
  <!-- AlarmAddWindow Strings -->
  <data name="AlarmAddTitle" xml:space="preserve">
    <value>报警设置</value>
  </data>
  <data name="LabelCurrentValue" xml:space="preserve">
    <value>当前值：</value>
  </data>
  <data name="LabelAlarmContent" xml:space="preserve">
    <value>报警内容：</value>
  </data>
  
  <!-- TCPIPDeviceSettingWindow Strings -->
  <data name="TCPIPDeviceSettingTitle" xml:space="preserve">
    <value>网络设备设置</value>
  </data>
  <data name="LabelConnectMode" xml:space="preserve">
    <value>连接类型</value>
  </data>
  <data name="LabelDeviceType" xml:space="preserve">
    <value>设备类型</value>
  </data>
  <data name="LabelWorkMode" xml:space="preserve">
    <value>工作模式</value>
  </data>
  <data name="LabelLocalPort" xml:space="preserve">
    <value>本地端口</value>
  </data>
  <data name="LabelRemotePort" xml:space="preserve">
    <value>远程端口</value>
  </data>
  <data name="LabelBaudRate" xml:space="preserve">
    <value>波特率</value>
  </data>
  <data name="LabelPLCAddress" xml:space="preserve">
    <value>PLC地址</value>
  </data>
  <data name="LabelMODBUSPoint" xml:space="preserve">
    <value>MODBUS点表</value>
  </data>
  <data name="LabelGDBoxSelect" xml:space="preserve">
    <value>固井箱子选择</value>
  </data>
  <data name="LabelGDBox1" xml:space="preserve">
    <value>1号箱</value>
  </data>
  <data name="LabelGDBox2" xml:space="preserve">
    <value>2号箱</value>
  </data>
  <data name="LabelGDBox3" xml:space="preserve">
    <value>3号箱</value>
  </data>
  <data name="LabelGDBox4" xml:space="preserve">
    <value>4号箱</value>
  </data>
  <data name="LabelGDBox5" xml:space="preserve">
    <value>5号箱</value>
  </data>
  <data name="LabelS7Point" xml:space="preserve">
    <value>S7点表</value>
  </data>
  <data name="ButtonEdit" xml:space="preserve">
    <value>修改</value>
  </data>
  <data name="ButtonSaveAndExit" xml:space="preserve">
    <value>保存并退出</value>
  </data>
  <data name="ButtonCancelAndExit" xml:space="preserve">
    <value>取消并退出</value>
  </data>
  
  <!-- RegisterWindow Strings -->
  <data name="RegisterTitle" xml:space="preserve">
    <value>软件授权</value>
  </data>
  <data name="LabelUnitName" xml:space="preserve">
    <value>使用单位名称：</value>
  </data>
  <data name="LabelVerifyCode" xml:space="preserve">
    <value>验证码：</value>
  </data>
  <data name="LabelRegisterHint" xml:space="preserve">
    <value>请将以上信息提交给供应商，供应商会返回授权码，在下面输入授权码即可获取授权开始使用软件.</value>
  </data>
  <data name="LabelAuthCode" xml:space="preserve">
    <value>使用授权码：</value>
  </data>
  
  <!-- RankMenuWindow Strings -->
  <data name="RankMenuSettingTitle" xml:space="preserve">
    <value>权限菜单设置</value>
  </data>
  
  <!-- GetDeviceList Strings -->
  <data name="GetDeviceListTitle" xml:space="preserve">
    <value>数据源列表</value>
  </data>
  <data name="LabelID" xml:space="preserve">
    <value>ID:</value>
  </data>
  <data name="LabelSN" xml:space="preserve">
    <value>SN:</value>
  </data>
  <data name="LabelFixedValue" xml:space="preserve">
    <value>固定值:</value>
  </data>
  <data name="ButtonManualAdd" xml:space="preserve">
    <value>手动添加</value>
  </data>
  
  <!-- FloatInputWindow Strings -->
  <data name="FloatInputTitle" xml:space="preserve">
    <value>输入浮点数</value>
  </data>
  <data name="LabelInputFloat" xml:space="preserve">
    <value>请输入一个浮点数:</value>
  </data>
  
  <!-- GasParameterSetting Strings -->
  <data name="GasParameterSettingTitle" xml:space="preserve">
    <value>国标天然气标准气产量计算参数设置</value>
  </data>
  <data name="LabelParameterSettings" xml:space="preserve">
    <value>参数设置</value>
  </data>
  <data name="LabelParameterName" xml:space="preserve">
    <value>参数名称</value>
  </data>
  <data name="LabelParameterValue" xml:space="preserve">
    <value>参数值</value>
  </data>
  <data name="LabelParameterUnit" xml:space="preserve">
    <value>单位</value>
  </data>
  <data name="LabelParameterDescription" xml:space="preserve">
    <value>参数描述</value>
  </data>
  <data name="LabelParameterList" xml:space="preserve">
    <value>参数列表</value>
  </data>
  <data name="ButtonAdd" xml:space="preserve">
    <value>添加</value>
  </data>
  <data name="ButtonClear" xml:space="preserve">
    <value>清空</value>
  </data>
  
  <!-- ChemicalComponentSettingWindow Strings -->
  <data name="ChemicalComponentSettingTitle" xml:space="preserve">
    <value>设定天然气组分</value>
  </data>
  <data name="LabelComponentSettings" xml:space="preserve">
    <value>组分设置</value>
  </data>
  <data name="LabelComponentName" xml:space="preserve">
    <value>组分名称</value>
  </data>
  <data name="LabelComponentValue" xml:space="preserve">
    <value>组分值</value>
  </data>
  <data name="LabelComponentUnit" xml:space="preserve">
    <value>单位</value>
  </data>
  <data name="LabelComponentList" xml:space="preserve">
    <value>组分列表</value>
  </data>
  
  <!-- SettingWindow Strings -->
  <data name="SettingWindowTitle" xml:space="preserve">
    <value>设置窗口</value>
  </data>
  <data name="ButtonConfirm" xml:space="preserve">
    <value>确认</value>
  </data>
  
  <!-- WebWindow Strings -->
  <data name="WebWindowTitle" xml:space="preserve">
    <value>Web窗口</value>
  </data>
  
  <!-- AIServiceSetting Strings -->
  <data name="AIServiceSettingTitle" xml:space="preserve">
    <value>配置AI接入服务</value>
  </data>
  <data name="LabelIPAddress" xml:space="preserve">
    <value>IP地址：</value>
  </data>
  <data name="LabelEnableService" xml:space="preserve">
    <value>是否启动：</value>
  </data>
  <data name="LabelRestartHint" xml:space="preserve">
    <value>(注：须手动重启后生效)</value>
  </data>
  
  <!-- SensorPickerWindow Strings -->
  <data name="SensorPickerTitle" xml:space="preserve">
    <value>选择传感器</value>
  </data>
  <data name="ColumnSensorName" xml:space="preserve">
    <value>传感器名称</value>
  </data>
  <data name="ColumnType" xml:space="preserve">
    <value>类型</value>
  </data>
  
  <!-- GasParameterSetting Additional Strings -->
  <data name="LabelCurrentProcess" xml:space="preserve">
    <value>当前流程：</value>
  </data>
  <data name="LabelProcessNumber" xml:space="preserve">
    <value>流程编号：</value>
  </data>
  <data name="LabelDensityRatio" xml:space="preserve">
    <value>天然气密度和干空气密度比：</value>
  </data>
  <data name="LabelDensityRatioHint" xml:space="preserve">
    <value>（气比重）0-0.75</value>
  </data>
  <data name="LabelAtmosphericPressure" xml:space="preserve">
    <value>井场大气压：</value>
  </data>
  <data name="LabelCO2MoleFraction" xml:space="preserve">
    <value>天然气中CO2的摩尔分数：</value>
  </data>
  <data name="LabelN2MoleFraction" xml:space="preserve">
    <value>天然气N2的摩尔分数：</value>
  </data>
  <data name="LabelStandardPipeDiameter" xml:space="preserve">
    <value>标准管道直径：</value>
  </data>
  <data name="LabelStandardWellPlateDiameter" xml:space="preserve">
    <value>标准孔板直径：</value>
  </data>
  <data name="LabelPipeMaterial" xml:space="preserve">
    <value>管道材质：</value>
  </data>
  <data name="LabelOrificeMaterial" xml:space="preserve">
    <value>孔板材质：</value>
  </data>
  <data name="LabelProcessList" xml:space="preserve">
    <value>流程列表</value>
  </data>
  
  <!-- GetDeviceList Additional Strings -->
  <data name="LabelManualInput" xml:space="preserve">
    <value>手动输入</value>
  </data>
  <data name="LabelAvailableDevices" xml:space="preserve">
    <value>可用设备</value>
  </data>
  <data name="LabelSelectedDevices" xml:space="preserve">
    <value>已选设备</value>
  </data>
  
  <!-- RankMenuWindow Additional Strings -->
  <data name="LabelRankManagement" xml:space="preserve">
    <value>权限管理</value>
  </data>
  <data name="LabelRankName" xml:space="preserve">
    <value>权限名称：</value>
  </data>
  <data name="LabelMenuPermissions" xml:space="preserve">
    <value>菜单权限</value>
  </data>
  <data name="LabelPermission" xml:space="preserve">
    <value>权限</value>
  </data>
  
  <!-- TCPIPDeviceSettingWindow Additional Strings -->
  <data name="LabelDeviceList" xml:space="preserve">
    <value>设备列表</value>
  </data>
  <data name="LabelDeviceSettings" xml:space="preserve">
    <value>设备设置</value>
  </data>
  <data name="LabelTCPParameters" xml:space="preserve">
    <value>TCP连接参数</value>
  </data>
  <data name="LabelPort" xml:space="preserve">
    <value>端口</value>
  </data>
  <data name="TextYes" xml:space="preserve">
    <value>是</value>
  </data>
  <data name="TextNo" xml:space="preserve">
    <value>否</value>
  </data>
  <data name="MessagePleaseSelectSensorToEdit" xml:space="preserve">
    <value>请先选中需要编辑的传感器！</value>
  </data>
  <data name="MessageNoItemSelectedToDelete" xml:space="preserve">
    <value>未选中要删除的项！</value>
  </data>
  <data name="MessageConfirmDeleteSensor" xml:space="preserve">
    <value>确定要删除该传感器&lt;{0}&gt;吗？</value>
  </data>
  <data name="TitlePrompt" xml:space="preserve">
    <value>提示</value>
  </data>
  <data name="LogDeleteSensor" xml:space="preserve">
    <value>删除传感器，Id:{0},{1}</value>
  </data>
  <data name="SensorDetailConfigTitle" xml:space="preserve">
    <value>传感器设置</value>
  </data>
  <data name="TextProperties" xml:space="preserve">
    <value>的属性</value>
  </data>
  <data name="LabelCheckConnectionAndSetup" xml:space="preserve">
    <value>核对现场连接情况，然后进行传感器设置。</value>
  </data>
  <data name="LabelSamplingTime" xml:space="preserve">
    <value>采样时间：</value>
  </data>
  <data name="LabelSamplingTimeNote" xml:space="preserve">
    <value>默认0，则使用井场采样率。(使用填入10的整数倍）</value>
  </data>
  <data name="LabelSensorNaming" xml:space="preserve">
    <value>传感器命名</value>
  </data>
  <data name="LabelDataType" xml:space="preserve">
    <value>数据类型：</value>
  </data>
  <data name="CheckBoxHighFrequencyDevice" xml:space="preserve">
    <value>高频数据设备</value>
  </data>
  <data name="LabelModuleNumber" xml:space="preserve">
    <value>模块编号：</value>
  </data>
  <data name="LabelHardwareNumberOnADC" xml:space="preserve">
    <value>数模转换器上的硬件编号</value>
  </data>
  <data name="LabelSensorType" xml:space="preserve">
    <value>传感器类型：</value>
  </data>
  <data name="CheckBoxAccumulateDisplacement" xml:space="preserve">
    <value>在该传感器上累计移液量</value>
  </data>
  <data name="LabelBy" xml:space="preserve">
    <value>按</value>
  </data>
  <data name="LabelAccumulate" xml:space="preserve">
    <value>累计</value>
  </data>
  <data name="LabelSensorRange" xml:space="preserve">
    <value>传感器量程：</value>
  </data>
  <data name="TextTo" xml:space="preserve">
    <value>至</value>
  </data>
  <data name="LabelADCOutputRange" xml:space="preserve">
    <value>数模转换输出模块范围：</value>
  </data>
  <data name="LabelMeasureUnit" xml:space="preserve">
    <value>计量单位：</value>
  </data>
  <data name="LabelUserGroup" xml:space="preserve">
    <value>用户分组：</value>
  </data>
  <data name="LabelValidThreshold" xml:space="preserve">
    <value>有效临界值：</value>
  </data>
  <data name="CheckBoxEnableValidThreshold" xml:space="preserve">
    <value>启用有效临界值</value>
  </data>
  <data name="LabelSerialNumber" xml:space="preserve">
    <value>序号：</value>
  </data>
  <data name="LabelSensorAdditionalProperties" xml:space="preserve">
    <value>传感器附加属性：</value>
  </data>
  <data name="CheckBoxEnableModule" xml:space="preserve">
    <value>启用模块</value>
  </data>
  <data name="CheckBoxHideSensor" xml:space="preserve">
    <value>隐藏传感器</value>
  </data>
  <data name="CheckBoxStoreProductionParams" xml:space="preserve">
    <value>存储产量中间参数</value>
  </data>
  <data name="LabelModuleEnableNote" xml:space="preserve">
    <value>提示：只有启用模块后，才能采集并保存数据。</value>
  </data>
  <data name="LabelIcon" xml:space="preserve">
    <value>图标：</value>
  </data>
  <data name="LabelDecimalPrecision" xml:space="preserve">
    <value>数据小数点位：</value>
  </data>
  <data name="LabelDefaultLineColor" xml:space="preserve">
    <value>默认线条颜色：</value>
  </data>
  <data name="LabelMultiValueIndex" xml:space="preserve">
    <value>多值索引：</value>
  </data>
  <data name="LabelSensorCalibration" xml:space="preserve">
    <value>传感器标定</value>
  </data>
  <data name="LabelReading1" xml:space="preserve">
    <value>读数1：</value>
  </data>
  <data name="ButtonRead" xml:space="preserve">
    <value>读取</value>
  </data>
  <data name="LabelActualValue" xml:space="preserve">
    <value>实际值：</value>
  </data>
  <data name="LabelFormulaInputNote" xml:space="preserve">
    <value>计算公式中，"Input"表示传感器读数，可直接使用其它传感器的名称。</value>
  </data>
  <data name="LabelCommonFormulas" xml:space="preserve">
    <value>常用公式：</value>
  </data>
  <data name="LabelFormulaEditArea" xml:space="preserve">
    <value>计算公式编写区（注意：公式区分大小写）：</value>
  </data>
  <data name="MenuInsertFunction" xml:space="preserve">
    <value>插入函数...</value>
  </data>
  <data name="MenuInsertSensorVariable" xml:space="preserve">
    <value>插入传感器变量...</value>
  </data>
  <data name="MenuInsertInput" xml:space="preserve">
    <value>插入Input（表示当前传感器读数）</value>
  </data>
  <data name="MenuInsertEnvironmentVariable" xml:space="preserve">
    <value>插入环境变量...</value>
  </data>
  <data name="LabelFormulaAnalysisStatus" xml:space="preserve">
    <value>公式分析状态以及计算校对：</value>
  </data>
  <data name="ButtonPrevStep" xml:space="preserve">
    <value>上一步</value>
  </data>
  <data name="ButtonNextStep" xml:space="preserve">
    <value>下一步</value>
  </data>
  <data name="ButtonComplete" xml:space="preserve">
    <value>完成</value>
  </data>
  <data name="ButtonCancel" xml:space="preserve">
    <value>取消</value>
  </data>
  <data name="TextCorrect" xml:space="preserve">
    <value>正确</value>
  </data>
  <data name="TextPleaseSelect" xml:space="preserve">
    <value>--选择--</value>
  </data>
  <data name="LabelSetupSN" xml:space="preserve">
    <value>设置SN：</value>
  </data>
  <data name="LabelSetupVirtualSensor" xml:space="preserve">
    <value>设置虚拟传感器：</value>
  </data>
  <data name="MessageSensorNameCannotDuplicate" xml:space="preserve">
    <value>传感器名称不能重复</value>
  </data>
  <data name="MessageValidThresholdCannotBeEmpty" xml:space="preserve">
    <value>有效临界值不能为空！</value>
  </data>
  <data name="MessageValidThresholdFormatIncorrect" xml:space="preserve">
    <value>有效临界值格式不正确！</value>
  </data>
  <data name="MessageFormulaInvalidPleaseModify" xml:space="preserve">
    <value>计算公式无效，请重新修改！</value>
  </data>
  <data name="MessageSoftwareNotRegistered" xml:space="preserve">
    <value>软件未注册，请先注册！</value>
  </data>
  <data name="LogAddSensor" xml:space="preserve">
    <value>新增传感器，Id:{0},{1}</value>
  </data>
  <data name="LogModifySensor" xml:space="preserve">
    <value>修改传感器，Id:{0},{1}</value>
  </data>
  <data name="TextVirtualSensor" xml:space="preserve">
    <value>虚拟传感器</value>
  </data>
  <data name="MeasureUnitSettingTitle" xml:space="preserve">
    <value>计量单位设置</value>
  </data>
  <data name="LabelMeasureUnitAdvancedWarning" xml:space="preserve">
    <value>计量单位属于高级设置，请不要轻易修改计量单位</value>
  </data>
  <data name="ColumnSymbol" xml:space="preserve">
    <value>符号</value>
  </data>
  <data name="ColumnExchangeRelationship" xml:space="preserve">
    <value>换算关系</value>
  </data>
  <data name="ColumnDefaultRange" xml:space="preserve">
    <value>默认量程</value>
  </data>
  <data name="ColumnDefaultDisplayRange" xml:space="preserve">
    <value>默认显示范围</value>
  </data>
  <data name="ColumnDefaultColor" xml:space="preserve">
    <value>默认颜色</value>
  </data>
  <data name="LabelUnitSymbol" xml:space="preserve">
    <value>单位符号：如℃</value>
  </data>
  <data name="LabelUnitCategory" xml:space="preserve">
    <value>单位分类：如温度、压力等</value>
  </data>
  <data name="LabelExchangeRelationship" xml:space="preserve">
    <value>换算关系：</value>
  </data>
  <data name="LabelBaseUnit" xml:space="preserve">
    <value>基础单位：</value>
  </data>
  <data name="LabelExchangeMethod" xml:space="preserve">
    <value>换算方法：</value>
  </data>
  <data name="LabelDefaultRange" xml:space="preserve">
    <value>默认量程：</value>
  </data>
  <data name="LabelDefaultDisplayRange" xml:space="preserve">
    <value>默认显示范围：</value>
  </data>
  <data name="LabelDefaultColor" xml:space="preserve">
    <value>默认颜色：</value>
  </data>
  <data name="LabelDefaultIcon" xml:space="preserve">
    <value>默认图标：</value>
  </data>
  <data name="LabelCoefficient" xml:space="preserve">
    <value>系数：</value>
  </data>
  <data name="LabelCalculationFormula" xml:space="preserve">
    <value>计算公式：</value>
  </data>
  <data name="TextSystemTemplate" xml:space="preserve">
    <value>系统模板</value>
  </data>
  <data name="MessageConfirmDeleteUnitSymbol" xml:space="preserve">
    <value>您确定要删除此单位符号吗？</value>
  </data>
  <data name="LogDeleteMeasureUnit" xml:space="preserve">
    <value>删除计量单位&lt;{0}&gt;</value>
  </data>
  <data name="TextAdd" xml:space="preserve">
    <value>添加</value>
  </data>
  <data name="TextModify" xml:space="preserve">
    <value>修改</value>
  </data>
  <data name="LogAddMeasureUnit" xml:space="preserve">
    <value>添加计量单位&lt;{0}&gt;</value>
  </data>
  <data name="LogModifyMeasureUnit" xml:space="preserve">
    <value>修改计量单位&lt;{0}&gt;</value>
  </data>
  <data name="TCPIPDeviceSettingTitle" xml:space="preserve">
    <value>网络设备设置</value>
  </data>
  <data name="LabelConnectionType" xml:space="preserve">
    <value>连接类型</value>
  </data>
  <data name="LabelDeviceType" xml:space="preserve">
    <value>设备类型</value>
  </data>
  <data name="LabelWorkMode" xml:space="preserve">
    <value>工作模式</value>
  </data>
  <data name="LabelIP" xml:space="preserve">
    <value>IP</value>
  </data>
  <data name="LabelLocalPort" xml:space="preserve">
    <value>本地端口</value>
  </data>
  <data name="LabelRemotePort" xml:space="preserve">
    <value>远程端口</value>
  </data>
  <data name="LabelBaudRate" xml:space="preserve">
    <value>波特率</value>
  </data>
  <data name="LabelPLCAddress" xml:space="preserve">
    <value>PLC地址:</value>
  </data>
  <data name="LabelMODBUSPointTable" xml:space="preserve">
    <value>MODBUS点表:</value>
  </data>
  <data name="LabelCementingBoxSelection" xml:space="preserve">
    <value>固井箱子选择:</value>
  </data>
  <data name="LabelBox1" xml:space="preserve">
    <value>1号箱</value>
  </data>
  <data name="LabelBox2" xml:space="preserve">
    <value>2号箱</value>
  </data>
  <data name="LabelBox3" xml:space="preserve">
    <value>3号箱</value>
  </data>
  <data name="LabelBox4" xml:space="preserve">
    <value>4号箱</value>
  </data>
  <data name="LabelBox5" xml:space="preserve">
    <value>5号箱</value>
  </data>
  <data name="LabelS7PointTable" xml:space="preserve">
    <value>S7点表:</value>
  </data>
  <data name="ButtonEdit" xml:space="preserve">
    <value>修改</value>
  </data>
  <data name="ButtonSaveAndExit" xml:space="preserve">
    <value>保存&amp;退出</value>
  </data>
  <data name="ButtonCancelAndExit" xml:space="preserve">
    <value>取消&amp;退出</value>
  </data>
  <data name="ColumnConnectionType" xml:space="preserve">
    <value>连接类型</value>
  </data>
  <data name="ColumnDeviceType" xml:space="preserve">
    <value>设备类型</value>
  </data>
  <data name="DevConnectModeTCPIP" xml:space="preserve">
    <value>TCPIP连接</value>
  </data>
  <data name="DevConnectModeCOM" xml:space="preserve">
    <value>COM串口连接</value>
  </data>
  <data name="DevConnectModePLCS7" xml:space="preserve">
    <value>PLCS7连接</value>
  </data>
  <data name="DevTypeInterfaceBox" xml:space="preserve">
    <value>接口箱</value>
  </data>
  <data name="DevTypeMODBUSPointTable" xml:space="preserve">
    <value>MODBUS点表</value>
  </data>
  <data name="DevTypeCementingBox" xml:space="preserve">
    <value>固井箱子</value>
  </data>
  <data name="DevTypeCementingAshTank" xml:space="preserve">
    <value>固井灰罐</value>
  </data>
  <data name="DevTypeSandNoise" xml:space="preserve">
    <value>砂噪音</value>
  </data>
  <data name="DevTypeMODBUSOutput" xml:space="preserve">
    <value>MODBUS输出</value>
  </data>
  <data name="DevTypePLCS7PointTable" xml:space="preserve">
    <value>PLCS7点表</value>
  </data>
  <data name="DevTypeWirelessSensor" xml:space="preserve">
    <value>无线传感器</value>
  </data>
  <data name="TextUnknownConnectionType" xml:space="preserve">
    <value>未知连接类型</value>
  </data>
  <data name="TextUnknownDeviceType" xml:space="preserve">
    <value>未知设备类型</value>
  </data>
  <data name="TextUnknown" xml:space="preserve">
    <value>未知</value>
  </data>
  <data name="MessagePleaseSelectMODBUSPointTable" xml:space="preserve">
    <value>请选择MODBUS点表</value>
  </data>
  <data name="MessagePleaseSelectS7PointTable" xml:space="preserve">
    <value>请选择S7点表</value>
  </data>
  <data name="ModBusDataSettingTitle" xml:space="preserve">
    <value>ModBus数据设置</value>
  </data>
  <data name="MessagePleaseSelectSensor" xml:space="preserve">
    <value>请先选择一个传感器。</value>
  </data>
  <data name="GeneralModBusDeviceSettingTitle" xml:space="preserve">
    <value>通用ModBus设备设置</value>
  </data>
  <data name="MenuAddDevice" xml:space="preserve">
    <value>添加设备</value>
  </data>
  <data name="MenuCopyDevice" xml:space="preserve">
    <value>复制设备</value>
  </data>
  <data name="MenuDeleteDevice" xml:space="preserve">
    <value>删除设备</value>
  </data>
  <data name="LabelDeviceName" xml:space="preserve">
    <value>设备名称：</value>
  </data>
  <data name="LabelDeviceSN" xml:space="preserve">
    <value>设备SN：</value>
  </data>
  <data name="LabelRTU" xml:space="preserve">
    <value>RTU</value>
  </data>
  <data name="LabelDataItem" xml:space="preserve">
    <value>数据项</value>
  </data>
  <data name="LabelUnit" xml:space="preserve">
    <value>单位</value>
  </data>
  <data name="LabelDeviceID" xml:space="preserve">
    <value>设备ID</value>
  </data>
  <data name="LabelAddress" xml:space="preserve">
    <value>地址</value>
  </data>
  <data name="LabelOffset" xml:space="preserve">
    <value>偏移量</value>
  </data>
  <data name="LabelFunctionCode" xml:space="preserve">
    <value>功能码</value>
  </data>
  <data name="LabelDisplayType" xml:space="preserve">
    <value>展示类型：</value>
  </data>
  <data name="LabelDisplayLevel" xml:space="preserve">
    <value>展示级：</value>
  </data>
  <data name="LabelDataFormat" xml:space="preserve">
    <value>数据格式：</value>
  </data>
  <data name="LabelDataBits" xml:space="preserve">
    <value>数据位</value>
  </data>
  <data name="LabelGroupNumber" xml:space="preserve">
    <value>分组号</value>
  </data>
  <data name="LabelInterlockingRelationship" xml:space="preserve">
    <value>联锁关系</value>
  </data>
  <data name="ButtonModify" xml:space="preserve">
    <value>修改</value>
  </data>
  <data name="ButtonImportExcelPointTable" xml:space="preserve">
    <value>导入Excel点表</value>
  </data>
  <data name="MenuAddDataItem" xml:space="preserve">
    <value>新增数据项</value>
  </data>
  <data name="MenuDeleteDataItem" xml:space="preserve">
    <value>删除数据项</value>
  </data>
  <data name="MenuInsertDataItem" xml:space="preserve">
    <value>插入数据项</value>
  </data>
  <data name="MenuCopyDataItem" xml:space="preserve">
    <value>复制数据项</value>
  </data>
  <data name="MenuPasteDataItem" xml:space="preserve">
    <value>粘贴数据项</value>
  </data>
  <data name="ColumnAddressHex" xml:space="preserve">
    <value>地址(Hex)</value>
  </data>
  <data name="ColumnAddress4X" xml:space="preserve">
    <value>地址(4X)</value>
  </data>
  <data name="TextNewDevice" xml:space="preserve">
    <value>新设备</value>
  </data>
  <data name="GasParameterSettingTitle" xml:space="preserve">
    <value>美标天然气标准气产量计算参数设置</value>
  </data>
  <data name="LabelCurrentProcess" xml:space="preserve">
    <value>当前流程：</value>
  </data>
  <data name="LabelProcessNumber" xml:space="preserve">
    <value>流程编号：</value>
  </data>
  <data name="LabelGasSpecificGravity" xml:space="preserve">
    <value>天然气密度和干空气密度比：</value>
  </data>
  <data name="LabelGasSpecificGravityNote" xml:space="preserve">
    <value>（气比重）</value>
  </data>
  <data name="LabelPipeDiameter" xml:space="preserve">
    <value>管道直径：</value>
  </data>
  <data name="LabelOrificeDiameter" xml:space="preserve">
    <value>孔板直径：</value>
  </data>
  <data name="LabelDegFTgr" xml:space="preserve">
    <value>DegFTgr：</value>
  </data>
  <data name="LabelPsiaPgr" xml:space="preserve">
    <value>PsiaPgr：</value>
  </data>
  <data name="LabelDegFTb" xml:space="preserve">
    <value>DegFTb：</value>
  </data>
  <data name="LabelPsiaPB" xml:space="preserve">
    <value>PsiaPB：</value>
  </data>
  <data name="LabelAtmosphericPressure" xml:space="preserve">
    <value>AtmosphericPressure：</value>
  </data>
  <data name="LabelInsentropicExponent" xml:space="preserve">
    <value>InsentropicExponent：</value>
  </data>
  <data name="LabelCompressibilityOfAirAtStdCond" xml:space="preserve">
    <value>CompressibilityOfAirAtStdCond：</value>
  </data>
  <data name="LabelOrificePlateThermalExpansionCofe" xml:space="preserve">
    <value>OrificePlateThermalExpansionCofe：</value>
  </data>
  <data name="LabelMeterRunThermalExpansionCofe" xml:space="preserve">
    <value>MeterRunThermalExpansionCofe：</value>
  </data>
  <data name="LabelDynamicViscosity" xml:space="preserve">
    <value>DynamicViscosity：</value>
  </data>
  <data name="LabelProcessList" xml:space="preserve">
    <value>流程列表</value>
  </data>
  <data name="ProcessNumberFormat" xml:space="preserve">
    <value>{0}号流程</value>
  </data>
  <data name="MessageInvalidProcessNumber" xml:space="preserve">
    <value>数据项：流程编号不正确！</value>
  </data>
  <data name="MessageProcessNumberExists" xml:space="preserve">
    <value>数据项：流程编号已存在！</value>
  </data>
  <data name="MessageModifySuccess" xml:space="preserve">
    <value>修改成功!</value>
  </data>
  <data name="MessageConfirmSaveChanges" xml:space="preserve">
    <value>已修改未保存，是否确认修改并保存？</value>
  </data>
  <data name="MessageNoChangesDetected" xml:space="preserve">
    <value>未检测到变更，请先添加或修改!</value>
  </data>
  <data name="MessageSaveResult" xml:space="preserve">
    <value>保存{0}!</value>
  </data>
  <data name="TextSuccess" xml:space="preserve">
    <value>成功</value>
  </data>
  <data name="TextFailed" xml:space="preserve">
    <value>失败</value>
  </data>
  <data name="MessageConfirmDelete" xml:space="preserve">
    <value>是否删除{0}？</value>
  </data>
  <data name="S7SettingWindowTitle" xml:space="preserve">
    <value>S7点表设置</value>
  </data>
  <data name="LabelPLCDeviceList" xml:space="preserve">
    <value>PLC设备列表</value>
  </data>
  <data name="LabelRack" xml:space="preserve">
    <value>Rack</value>
  </data>
  <data name="LabelSlot" xml:space="preserve">
    <value>Slot</value>
  </data>
  <data name="LabelSN" xml:space="preserve">
    <value>SN</value>
  </data>
  <data name="ButtonImportExcel" xml:space="preserve">
    <value>导入Excel</value>
  </data>
  <data name="ButtonSaveAndExit" xml:space="preserve">
    <value>保存&amp;退出</value>
  </data>
  <data name="ButtonCancelAndExit" xml:space="preserve">
    <value>取消&amp;退出</value>
  </data>
  <data name="LabelSequenceNumber" xml:space="preserve">
    <value>序号</value>
  </data>
  <data name="LabelStartAddress" xml:space="preserve">
    <value>起始地址</value>
  </data>
  <data name="LabelBit" xml:space="preserve">
    <value>位</value>
  </data>
  <data name="LabelDataBlock" xml:space="preserve">
    <value>数据块</value>
  </data>
  <data name="LabelDBNumber" xml:space="preserve">
    <value>DB号</value>
  </data>
  <data name="LabelOperationType" xml:space="preserve">
    <value>操作类型</value>
  </data>
  <data name="LabelConversionRange" xml:space="preserve">
    <value>转换范围</value>
  </data>
  <data name="MessageDuplicateNameRenamed" xml:space="preserve">
    <value>名称有重复,已帮您重新取名以确保名称不重复,原名称：{0}   更改为:{1}</value>
  </data>
  <data name="MessageUnknownDataGridClicked" xml:space="preserve">
    <value>未知 DataGrid 被点击</value>
  </data>
  <data name="MessagePleaseSelectPLCDevice" xml:space="preserve">
    <value>请选择PLC设备</value>
  </data>
  <data name="FilterExcelFiles" xml:space="preserve">
    <value>Excel 文件 (*.xlsx)|*.xlsx</value>
  </data>
  <data name="MessageDataFormatError" xml:space="preserve">
    <value>错误：必填项数据格式不正确，请检查!</value>
  </data>
  <data name="MessageDataImportCompleted" xml:space="preserve">
    <value>提示：数据导入完成！</value>
  </data>
  <data name="MessageRowError" xml:space="preserve">
    <value>第{0}行出错:{1}</value>
  </data>
  <data name="InterlockConfigWindowTitle" xml:space="preserve">
    <value>联锁规则数据设置</value>
  </data>
  <data name="LabelRuleGroupList" xml:space="preserve">
    <value>规则组列表</value>
  </data>
  <data name="LabelRuleList" xml:space="preserve">
    <value>规则列表</value>
  </data>
  <data name="LabelIndex" xml:space="preserve">
    <value>索引</value>
  </data>
  <data name="LabelThreshold" xml:space="preserve">
    <value>阈值</value>
  </data>
  <data name="LabelFormula" xml:space="preserve">
    <value>公式</value>
  </data>
  <data name="LabelDeviceList" xml:space="preserve">
    <value>设备列表</value>
  </data>
  <data name="MessagePleaseSelectRuleGroup" xml:space="preserve">
    <value>请选择规则组</value>
  </data>
  <data name="TextNewRuleGroup" xml:space="preserve">
    <value>新规则组</value>
  </data>
  <data name="TextDeviceSN" xml:space="preserve">
    <value>设备SN</value>
  </data>
  <data name="TextFormula" xml:space="preserve">
    <value>公式</value>
  </data>
  <data name="MessageRuleGroupNameRenamed" xml:space="preserve">
    <value>规则组名称有重复,已帮您重新取名以确保名称不重复,原名称：[{0}]更改为：[{1}]</value>
  </data>
  <data name="MonitorViewSettingWindowTitle" xml:space="preserve">
    <value>监视窗口属性设置</value>
  </data>
  <data name="LabelBasicSettings" xml:space="preserve">
    <value>基本设置</value>
  </data>
  <data name="LabelMonitorSchemeName" xml:space="preserve">
    <value>监视方案名称：</value>
  </data>
  <data name="LabelCurveWidth" xml:space="preserve">
    <value>曲线宽度：</value>
  </data>
  <data name="LabelSmallGridCount" xml:space="preserve">
    <value>小格子数：</value>
  </data>
  <data name="LabelBigGridCount" xml:space="preserve">
    <value>大格子数：</value>
  </data>
  <data name="LabelYAxisInterval" xml:space="preserve">
    <value>Y轴间隔：</value>
  </data>
  <data name="LabelDisplaySettings" xml:space="preserve">
    <value>显示设置</value>
  </data>
  <data name="LabelFontSize" xml:space="preserve">
    <value>字体大小：</value>
  </data>
  <data name="LabelBackgroundColor" xml:space="preserve">
    <value>背景颜色：</value>
  </data>
  <data name="LabelTitleColor" xml:space="preserve">
    <value>标题颜色：</value>
  </data>
  <data name="LabelTableLineColor" xml:space="preserve">
    <value>表格线颜色：</value>
  </data>
  <data name="LabelBigGridLineColor" xml:space="preserve">
    <value>大格线颜色：</value>
  </data>
  <data name="LabelAdvancedSettings" xml:space="preserve">
    <value>高级设置</value>
  </data>
  <data name="LabelDataSamplingInterval" xml:space="preserve">
    <value>数据抽点间隔(秒)：</value>
  </data>
  <data name="TextUnknown" xml:space="preserve">
    <value>未知</value>
  </data>
  <data name="TextNewMonitorScheme" xml:space="preserve">
    <value>新监视方案</value>
  </data>
  <data name="MessageMonitorSchemeNameRequired" xml:space="preserve">
    <value>请输入监视方案名称</value>
  </data>
  <data name="MessageCurveWidthFormatError" xml:space="preserve">
    <value>曲线宽度格式错误</value>
  </data>
  <data name="MessageCurveWidthMinValue" xml:space="preserve">
    <value>曲线宽度不能小于1</value>
  </data>
  <data name="MessageSmallGridCountFormatError" xml:space="preserve">
    <value>小格子数格式错误</value>
  </data>
  <data name="MessageSmallGridCountMinValue" xml:space="preserve">
    <value>小格子数不能小于{0}</value>
  </data>
  <data name="MessageBigGridCountFormatError" xml:space="preserve">
    <value>大格子数格式错误</value>
  </data>
  <data name="MessageBigGridCountMinValue" xml:space="preserve">
    <value>大格子数不能小于{0}</value>
  </data>
  <data name="MessageYAxisIntervalFormatError" xml:space="preserve">
    <value>Y轴间隔格式错误</value>
  </data>
  <data name="MessageYAxisIntervalMinValue" xml:space="preserve">
    <value>Y轴间隔不能小于{0}</value>
  </data>
  <data name="MessageFontSizeRequired" xml:space="preserve">
    <value>请输入字体大小</value>
  </data>
  <data name="MessageFontSizeFormatError" xml:space="preserve">
    <value>字体大小格式错误</value>
  </data>
  <data name="MessageFontSizeMinValue" xml:space="preserve">
    <value>字体大小不能小于1</value>
  </data>
  <data name="MessageSamplingIntervalRequired" xml:space="preserve">
    <value>请输入数据抽点间隔</value>
  </data>
  <data name="MessageSamplingIntervalFormatError" xml:space="preserve">
    <value>数据抽点间隔格式错误</value>
  </data>
  <data name="MessageSamplingIntervalMinValue" xml:space="preserve">
    <value>数据抽点间隔不能小于0</value>
  </data>
  <data name="EnvironmentVariableSettingWindowTitle" xml:space="preserve">
    <value>井场环境变量设置</value>
  </data>
  <data name="LabelEnvironmentVariableName" xml:space="preserve">
    <value>环境变量名称</value>
  </data>
  <data name="LabelEnvironmentVariableValue" xml:space="preserve">
    <value>环境变量值</value>
  </data>
  <data name="MessageEnvironmentVariableDescription" xml:space="preserve">
    <value>环境变量的值，在使用了这些变量的公式中起作用！</value>
  </data>
  <data name="MessagePleaseUnderstandBeforeModifying" xml:space="preserve">
    <value>请了解仔细后再修改。</value>
  </data>
  <data name="MessageDoNotModifyNameUsually" xml:space="preserve">
    <value>通常情况下请不要修改名称！</value>
  </data>
  <data name="ButtonAddOrModify" xml:space="preserve">
    <value>添加/修改</value>
  </data>
  <data name="ButtonNationalStandardGasParameterSetting" xml:space="preserve">
    <value>国标气产量参数设置</value>
  </data>
  <data name="LabelNationalStandard" xml:space="preserve">
    <value>执行标准：GB/T 21446-2008</value>
  </data>
  <data name="ButtonUSAStandardGasParameterSetting" xml:space="preserve">
    <value>美标气产量参数设置</value>
  </data>
  <data name="LabelUSAStandard" xml:space="preserve">
    <value>执行标准：API MPMS 14.2</value>
  </data>
  <data name="ButtonSandParameterSetting" xml:space="preserve">
    <value>砂量参数设置</value>
  </data>
  <data name="MessageSaveWillTakeEffectImmediately" xml:space="preserve">
    <value>提示：保存后将立即生效！</value>
  </data>
  <data name="TextSystemTemplate" xml:space="preserve">
    <value>系统模板</value>
  </data>
  <data name="MessagePleaseSelectItemToDelete" xml:space="preserve">
    <value>请先选中需要删除的项！</value>
  </data>
  <data name="MessageConfirmDeleteEnvironmentVariable" xml:space="preserve">
    <value>您确定要删除此环境变量吗？</value>
  </data>
  <data name="MessageConfirmSaveAndApply" xml:space="preserve">
    <value>您确定要保存并生效吗？</value>
  </data>
  <data name="LogModifyEnvironmentVariableSettings" xml:space="preserve">
    <value>修改环境变量设置</value>
  </data>
  <data name="SandParameterSettingWindowTitle" xml:space="preserve">
    <value>砂量参数设置</value>
  </data>
  <data name="LabelWorkflow" xml:space="preserve">
    <value>流程：</value>
  </data>
  <data name="LabelPipelineMedium" xml:space="preserve">
    <value>管道内介质：</value>
  </data>
  <data name="TextAutomatic" xml:space="preserve">
    <value>自动</value>
  </data>
  <data name="LabelGasCompressionCoefficient" xml:space="preserve">
    <value>气体压缩系数：</value>
  </data>
  <data name="LabelPipelineInnerDiameter" xml:space="preserve">
    <value>管道内径（mm）：</value>
  </data>
  <data name="LabelBackgroundNoise" xml:space="preserve">
    <value>背景噪音：</value>
  </data>
  <data name="LabelStableRawTimeRange" xml:space="preserve">
    <value>稳定Raw时间范围：</value>
  </data>
  <data name="TextSeconds" xml:space="preserve">
    <value>秒</value>
  </data>
  <data name="LabelNoiseIncreaseRange" xml:space="preserve">
    <value>判定噪声增加范围：</value>
  </data>
  <data name="TextPercent" xml:space="preserve">
    <value>%</value>
  </data>
  <data name="MessagePipelineTemperaturePressureInstruction" xml:space="preserve">
    <value>管线温度和压力，请在编辑公式的时候，以参数的形式引入。</value>
  </data>
  <data name="MessageIntroductionMethod1" xml:space="preserve">
    <value>引入方法一：直接使用值，表示固定值</value>
  </data>
  <data name="MessageIntroductionMethod2" xml:space="preserve">
    <value>引入方法二：填写传感器名称，表示引入传感器的值</value>
  </data>
  <data name="SelectEnvironmentVariableWindowTitle" xml:space="preserve">
    <value>选择环境变量</value>
  </data>
  <data name="LabelKeyword" xml:space="preserve">
    <value>关键字:</value>
  </data>
  <data name="ButtonSearch" xml:space="preserve">
    <value>搜索</value>
  </data>
  <data name="LabelLogType" xml:space="preserve">
    <value>日志类型:</value>
  </data>
  <data name="LabelTimeRange" xml:space="preserve">
    <value>时间范围:</value>
  </data>
  <data name="TextTo" xml:space="preserve">
    <value>至</value>
  </data>
  <data name="TextToday" xml:space="preserve">
    <value>今天</value>
  </data>
  <data name="TextLastThreeDays" xml:space="preserve">
    <value>最近三天</value>
  </data>
  <data name="TextLastWeek" xml:space="preserve">
    <value>最近一周</value>
  </data>
  <data name="TextLastMonth" xml:space="preserve">
    <value>最近一个月</value>
  </data>
  <data name="TextCustom" xml:space="preserve">
    <value>自定义</value>
  </data>
  <data name="LabelTime" xml:space="preserve">
    <value>时间</value>
  </data>
  <data name="LabelType" xml:space="preserve">
    <value>类型</value>
  </data>
  <data name="LabelMessage" xml:space="preserve">
    <value>消息</value>
  </data>
  <data name="MenuCopy" xml:space="preserve">
    <value>复制</value>
  </data>
  <data name="MenuExportToFile" xml:space="preserve">
    <value>导出到文件...</value>
  </data>
  <data name="MessageErrorLoadingLogs" xml:space="preserve">
    <value>加载日志时出错</value>
  </data>
  <data name="TextError" xml:space="preserve">
    <value>错误</value>
  </data>
  <data name="MessageErrorReadingLogFile" xml:space="preserve">
    <value>读取日志文件时出错</value>
  </data>
  <data name="LabelLevel" xml:space="preserve">
    <value>级别</value>
  </data>
  <data name="LabelSource" xml:space="preserve">
    <value>来源</value>
  </data>
  <data name="FilterCSVAndTextFiles" xml:space="preserve">
    <value>CSV文件 (*.csv)|*.csv|文本文件 (*.txt)|*.txt|所有文件 (*.*)|*.*</value>
  </data>
  <data name="TitleExportLogs" xml:space="preserve">
    <value>导出日志</value>
  </data>
  <data name="MessageLogsExportedSuccessfully" xml:space="preserve">
    <value>日志已成功导出到</value>
  </data>
  <data name="TextExportSuccess" xml:space="preserve">
    <value>导出成功</value>
  </data>
  <data name="MessageErrorExportingLogs" xml:space="preserve">
    <value>导出日志时出错</value>
  </data>
  <data name="LabelReading2" xml:space="preserve">
    <value>读数2：</value>
  </data>
  <data name="LabelReading3" xml:space="preserve">
    <value>读数3：</value>
  </data>
  <data name="LabelReading4" xml:space="preserve">
    <value>读数4：</value>
  </data>
  <data name="LabelReading5" xml:space="preserve">
    <value>读数5：</value>
  </data>
  <data name="LabelReading6" xml:space="preserve">
    <value>读数6：</value>
  </data>
  <data name="LabelReading7" xml:space="preserve">
    <value>读数7：</value>
  </data>
  <data name="MessageCalibrationProcess" xml:space="preserve">
    <value>标定过程为根据现场标准检验台对该传感器各个点的测试取值，即前面为该传感器在校准台的当前读数，后面为实际的校准读数。</value>
  </data>
  <data name="MessageFormulaInputDescription" xml:space="preserve">
    <value>计算公式中，"Input"表示传感器读数，可直接使用其它传感器的名称。</value>
  </data>
  <data name="LabelFormulaEditingArea" xml:space="preserve">
    <value>计算公式编写区（注意：公式区分大小写）：</value>
  </data>
  <data name="MenuInsertEnvironmentVariable" xml:space="preserve">
    <value>插入环境变量...</value>
  </data>
  <data name="LabelFormulaAnalysisStatus" xml:space="preserve">
    <value>公式分析状态以及计算校对：</value>
  </data>
  <data name="ButtonPreviousStep" xml:space="preserve">
    <value>上一步</value>
  </data>
  <data name="ButtonNextStep" xml:space="preserve">
    <value>下一步</value>
  </data>
  <data name="SensorDataHistoryWindowTitle" xml:space="preserve">
    <value>传感器历史数据查询及导出</value>
  </data>
  <data name="LabelStartTime" xml:space="preserve">
    <value>开始时间：</value>
  </data>
  <data name="LabelEndTime" xml:space="preserve">
    <value>结束时间：</value>
  </data>
  <data name="LabelStartPoint" xml:space="preserve">
    <value>起点</value>
  </data>
  <data name="LabelEndPoint" xml:space="preserve">
    <value>终点</value>
  </data>
  <data name="LabelStartValue" xml:space="preserve">
    <value>起点值</value>
  </data>
  <data name="LabelEndValue" xml:space="preserve">
    <value>终点值</value>
  </data>
  <data name="LabelRandomFluctuation" xml:space="preserve">
    <value>随机变化幅度</value>
  </data>
  <data name="ButtonInsertData" xml:space="preserve">
    <value>插入数据</value>
  </data>
  <data name="LabelSamplingInterval" xml:space="preserve">
    <value>抽点：</value>
  </data>
  <data name="ButtonQuery" xml:space="preserve">
    <value>查询</value>
  </data>
  <data name="ButtonExportAsTextFile" xml:space="preserve">
    <value>导出为文本文件</value>
  </data>
  <data name="ButtonExportAsExcel" xml:space="preserve">
    <value>导出为Excel</value>
  </data>
  <data name="LabelFirstPage" xml:space="preserve">
    <value>首页</value>
  </data>
  <data name="LabelPreviousPage" xml:space="preserve">
    <value>上一页</value>
  </data>
  <data name="LabelNextPage" xml:space="preserve">
    <value>下一页</value>
  </data>
  <data name="LabelLastPage" xml:space="preserve">
    <value>末页</value>
  </data>
  <data name="LabelPage" xml:space="preserve">
    <value>第</value>
  </data>
  <data name="LabelPageSuffix" xml:space="preserve">
    <value>页，</value>
  </data>
  <data name="LabelTotal" xml:space="preserve">
    <value>共</value>
  </data>
  <data name="LabelRecords" xml:space="preserve">
    <value>条</value>
  </data>
  <data name="LabelJumpTo" xml:space="preserve">
    <value>跳转到</value>
  </data>
  <data name="LabelPageUnit" xml:space="preserve">
    <value>页</value>
  </data>
  <data name="ButtonJump" xml:space="preserve">
    <value>跳转</value>
  </data>
  <data name="LabelSelectSensor" xml:space="preserve">
    <value>选择传感器：</value>
  </data>
  <data name="LabelProgress" xml:space="preserve">
    <value>进度</value>
  </data>
  <data name="TextPageSize100K" xml:space="preserve">
    <value>每页10万条</value>
  </data>
  <data name="TextPageSize500K" xml:space="preserve">
    <value>每页50万条</value>
  </data>
  <data name="TextPageSize1M" xml:space="preserve">
    <value>每页100万条</value>
  </data>
  <data name="LabelModify" xml:space="preserve">
    <value>修改</value>
  </data>
  <data name="MessageJumpPageNumberRequired" xml:space="preserve">
    <value>跳转页码不能为空！</value>
  </data>
  <data name="MessageJumpPageNumberInvalid" xml:space="preserve">
    <value>跳转页码格式不正确！</value>
  </data>
  <data name="ButtonQuerying" xml:space="preserve">
    <value>查询中...</value>
  </data>
  <data name="MessagePleaseSelectSensor" xml:space="preserve">
    <value>请选择传感器！</value>
  </data>
  <data name="MessagePleaseSelectStartDate" xml:space="preserve">
    <value>请选择开始日期！</value>
  </data>
  <data name="MessagePleaseSelectEndDate" xml:space="preserve">
    <value>请选择结束日期！</value>
  </data>
  <data name="MessageSamplingIntervalRequired" xml:space="preserve">
    <value>抽点不能为空！</value>
  </data>
  <data name="MessageSamplingIntervalInvalid" xml:space="preserve">
    <value>抽点值格式不正确！</value>
  </data>
  <data name="MonitorWindowViewTitle" xml:space="preserve">
    <value>数据监控窗口</value>
  </data>
  <data name="ButtonShow" xml:space="preserve">
    <value>显示</value>
  </data>
  <data name="MenuDisplaySensorInChart" xml:space="preserve">
    <value>在图中显示该传感器</value>
  </data>
  <data name="MenuRemoveFromChart" xml:space="preserve">
    <value>从图中移除</value>
  </data>
  <data name="MenuSetColor" xml:space="preserve">
    <value>设置颜色</value>
  </data>
  <data name="MenuMoveUp" xml:space="preserve">
    <value>向上移动</value>
  </data>
  <data name="MenuMoveDown" xml:space="preserve">
    <value>向下移动</value>
  </data>
  <data name="MenuSensorSettings" xml:space="preserve">
    <value>传感器设置</value>
  </data>
  <data name="MenuModifyLastValue" xml:space="preserve">
    <value>修改最后一个值</value>
  </data>
  <data name="MenuHideSensor" xml:space="preserve">
    <value>隐藏该传感器</value>
  </data>
  <data name="MenuUnhideSensor" xml:space="preserve">
    <value>不隐藏该传感器</value>
  </data>
  <data name="MenuShowHiddenSensors" xml:space="preserve">
    <value>显示(隐藏传感器)</value>
  </data>
  <data name="MenuHideAllHiddenSensors" xml:space="preserve">
    <value>不显示(隐藏传感器)</value>
  </data>
  <data name="MenuIndependentWindow" xml:space="preserve">
    <value>独立窗口显示</value>
  </data>
  <data name="LabelCurrentValue" xml:space="preserve">
    <value>当前值</value>
  </data>
  <data name="SectionBasicInfo" xml:space="preserve">
    <value>基本信息</value>
  </data>
  <data name="SectionSensorConfig" xml:space="preserve">
    <value>传感器配置</value>
  </data>
  <data name="SectionAdvancedSettings" xml:space="preserve">
    <value>高级设置</value>
  </data>
  <data name="SectionDisplaySettings" xml:space="preserve">
    <value>显示设置</value>
  </data>
  <data name="SectionCalculationFormula" xml:space="preserve">
    <value>计算公式</value>
  </data>

  <!-- SensorAlarmConfigControl 国际化资源 -->
  <data name="SensorAlarmConfig_AddSensor" xml:space="preserve">
    <value>+ 添加传感器</value>
  </data>
  <data name="SensorAlarmConfig_DeleteSelected" xml:space="preserve">
    <value>- 删除选中</value>
  </data>
  <data name="SensorAlarmConfig_Title" xml:space="preserve">
    <value>传感器警报配置</value>
  </data>
  <data name="SensorAlarmConfig_SensorName" xml:space="preserve">
    <value>传感器名称</value>
  </data>
  <data name="SensorAlarmConfig_SensorSN" xml:space="preserve">
    <value>传感器SN</value>
  </data>
  <data name="SensorAlarmConfig_SensorID" xml:space="preserve">
    <value>传感器ID</value>
  </data>
  <data name="SensorAlarmConfig_Operation" xml:space="preserve">
    <value>操作</value>
  </data>
  <data name="SensorAlarmConfig_AddSensorTooltip" xml:space="preserve">
    <value>添加传感器</value>
  </data>
  <data name="SensorAlarmConfig_DeleteSensorTooltip" xml:space="preserve">
    <value>删除传感器</value>
  </data>
  <data name="SensorAlarmConfig_RuleCount" xml:space="preserve">
    <value>{0} 条规则</value>
  </data>
  <data name="SensorAlarmConfig_AddRuleTooltip" xml:space="preserve">
    <value>添加规则</value>
  </data>
  <data name="SensorAlarmConfig_DeleteRuleTooltip" xml:space="preserve">
    <value>删除规则</value>
  </data>

  <!-- 阈值报警规则 -->
  <data name="SensorAlarmConfig_ThresholdRules" xml:space="preserve">
    <value>阈值报警规则</value>
  </data>
  <data name="SensorAlarmConfig_Severity" xml:space="preserve">
    <value>严重程度</value>
  </data>
  <data name="SensorAlarmConfig_Operator" xml:space="preserve">
    <value>运算符</value>
  </data>
  <data name="SensorAlarmConfig_Threshold" xml:space="preserve">
    <value>阈值</value>
  </data>
  <data name="SensorAlarmConfig_Deadband" xml:space="preserve">
    <value>死区</value>
  </data>
  <data name="SensorAlarmConfig_Message" xml:space="preserve">
    <value>消息</value>
  </data>

  <!-- 严重程度选项 -->
  <data name="SensorAlarmConfig_SeverityCritical" xml:space="preserve">
    <value>严重</value>
  </data>
  <data name="SensorAlarmConfig_SeverityHigh" xml:space="preserve">
    <value>高</value>
  </data>
  <data name="SensorAlarmConfig_SeverityMedium" xml:space="preserve">
    <value>中等</value>
  </data>
  <data name="SensorAlarmConfig_SeverityLow" xml:space="preserve">
    <value>低</value>
  </data>

  <!-- 趋势报警规则 -->
  <data name="SensorAlarmConfig_TrendRules" xml:space="preserve">
    <value>趋势报警规则</value>
  </data>
  <data name="SensorAlarmConfig_RateOfChange" xml:space="preserve">
    <value>变化率</value>
  </data>
  <data name="SensorAlarmConfig_TimeWindowSeconds" xml:space="preserve">
    <value>时间窗口(秒)</value>
  </data>
  <data name="SensorAlarmConfig_Direction" xml:space="preserve">
    <value>方向</value>
  </data>

  <!-- 方向选项 -->
  <data name="SensorAlarmConfig_DirectionRising" xml:space="preserve">
    <value>上升</value>
  </data>
  <data name="SensorAlarmConfig_DirectionFalling" xml:space="preserve">
    <value>下降</value>
  </data>
  <data name="SensorAlarmConfig_DirectionBoth" xml:space="preserve">
    <value>双向</value>
  </data>

  <!-- 模式报警规则 -->
  <data name="SensorAlarmConfig_PatternRules" xml:space="preserve">
    <value>模式报警规则</value>
  </data>
  <data name="SensorAlarmConfig_Pattern" xml:space="preserve">
    <value>模式</value>
  </data>
  <data name="SensorAlarmConfig_Tolerance" xml:space="preserve">
    <value>容差</value>
  </data>

  <!-- 关联报警规则 -->
  <data name="SensorAlarmConfig_CorrelationRules" xml:space="preserve">
    <value>关联报警规则</value>
  </data>
  <data name="SensorAlarmConfig_RelatedSensor" xml:space="preserve">
    <value>关联传感器</value>
  </data>
  <data name="SensorAlarmConfig_Expression" xml:space="preserve">
    <value>表达式</value>
  </data>

  <!-- 运算符选项 -->
  <data name="SensorAlarmConfig_OperatorGreaterThan" xml:space="preserve">
    <value>&gt;</value>
  </data>
  <data name="SensorAlarmConfig_OperatorGreaterThanOrEqual" xml:space="preserve">
    <value>&gt;=</value>
  </data>
  <data name="SensorAlarmConfig_OperatorLessThan" xml:space="preserve">
    <value>&lt;</value>
  </data>
  <data name="SensorAlarmConfig_OperatorLessThanOrEqual" xml:space="preserve">
    <value>&lt;=</value>
  </data>
  <data name="SensorAlarmConfig_OperatorEqual" xml:space="preserve">
    <value>=</value>
  </data>
  <data name="SensorAlarmConfig_OperatorNotEqual" xml:space="preserve">
    <value>!=</value>
  </data>

  <!-- 规则数量格式 -->
  <data name="SensorAlarmConfig_RuleCountFormat" xml:space="preserve">
    <value>{0} 条规则</value>
  </data>

  <!-- 规则类别名称 -->
  <data name="SensorAlarmConfig_ThresholdRuleCategory" xml:space="preserve">
    <value>阈值规则</value>
  </data>
  <data name="SensorAlarmConfig_TrendRuleCategory" xml:space="preserve">
    <value>趋势规则</value>
  </data>
  <data name="SensorAlarmConfig_PatternRuleCategory" xml:space="preserve">
    <value>模式规则</value>
  </data>
  <data name="SensorAlarmConfig_CorrelationRuleCategory" xml:space="preserve">
    <value>关联规则</value>
  </data>

  <!-- 默认值和消息 -->
  <data name="SensorAlarmConfig_NewSensor" xml:space="preserve">
    <value>新传感器</value>
  </data>
  <data name="SensorAlarmConfig_UnknownSensor" xml:space="preserve">
    <value>未知传感器</value>
  </data>
  <data name="SensorAlarmConfig_NewRule" xml:space="preserve">
    <value>新{0}</value>
  </data>
  <data name="SensorAlarmConfig_PleaseSelectSensor" xml:space="preserve">
    <value>请选择要删除的传感器</value>
  </data>
  <data name="SensorAlarmConfig_Tip" xml:space="preserve">
    <value>提示</value>
  </data>
  <data name="SensorAlarmConfig_NewAlarmMessage" xml:space="preserve">
    <value>新阈值警报</value>
  </data>
  <data name="SensorAlarmConfig_EditThresholdRule" xml:space="preserve">
    <value>编辑阈值规则</value>
  </data>
  <data name="SensorAlarmConfig_UnknownRuleType" xml:space="preserve">
    <value>未知的规则类型</value>
  </data>
  <data name="SensorAlarmConfig_Error" xml:space="preserve">
    <value>错误</value>
  </data>
  <data name="SensorAlarmConfig_AddRuleTooltip" xml:space="preserve">
    <value>添加规则</value>
  </data>

  <!-- RealTimeDeviceListWindow -->
  <data name="RealTimeDeviceList_Title" xml:space="preserve">
    <value>实时设备列表</value>
  </data>
  <data name="RealTimeDeviceList_Refresh" xml:space="preserve">
    <value>🔄 刷新</value>
  </data>
  <data name="RealTimeDeviceList_DeviceList" xml:space="preserve">
    <value>实时设备列表</value>
  </data>
  <data name="RealTimeDeviceList_DeviceCount" xml:space="preserve">
    <value> ({0} 个设备)</value>
  </data>
  <data name="RealTimeDeviceList_DeviceSN" xml:space="preserve">
    <value>设备SN</value>
  </data>
  <data name="RealTimeDeviceList_IPAddress" xml:space="preserve">
    <value>IP地址</value>
  </data>
  <data name="RealTimeDeviceList_DeviceType" xml:space="preserve">
    <value>设备类型</value>
  </data>
  <data name="RealTimeDeviceList_Status" xml:space="preserve">
    <value>状态</value>
  </data>
  <data name="RealTimeDeviceList_Value" xml:space="preserve">
    <value>数值</value>
  </data>
  <data name="RealTimeDeviceList_Timeout" xml:space="preserve">
    <value>超时</value>
  </data>
  <data name="RealTimeDeviceList_ChannelName" xml:space="preserve">
    <value>通道名称</value>
  </data>
  <data name="RealTimeDeviceList_LoadingChannels" xml:space="preserve">
    <value>正在加载通道信息...</value>
  </data>
  <data name="RealTimeDeviceList_ChannelPrefix" xml:space="preserve">
    <value>通道{0}</value>
  </data>
  <data name="RealTimeDeviceList_DataTypeAnalog" xml:space="preserve">
    <value>模拟量</value>
  </data>
  <data name="RealTimeDeviceList_DataTypeDigital" xml:space="preserve">
    <value>数字量</value>
  </data>
  <data name="RealTimeDeviceList_DataTypePulse" xml:space="preserve">
    <value>脉冲量</value>
  </data>
  <data name="RealTimeDeviceList_DataTypeUnknown" xml:space="preserve">
    <value>未知</value>
  </data>
  <data name="RealTimeDeviceList_SensorOccupied" xml:space="preserve">
    <value>传感器: {0}</value>
  </data>
  <data name="RealTimeDeviceList_Yes" xml:space="preserve">
    <value>是</value>
  </data>
  <data name="RealTimeDeviceList_No" xml:space="preserve">
    <value>否</value>
  </data>
  <data name="RealTimeDeviceList_Unknown" xml:space="preserve">
    <value>未知</value>
  </data>

  <!-- RealTimeDeviceConnectionListWindow -->
  <data name="RealTimeDeviceConnection_Title" xml:space="preserve">
    <value>设备连接状态列表</value>
  </data>
  <data name="RealTimeDeviceConnection_IPAddress" xml:space="preserve">
    <value>IP地址</value>
  </data>
  <data name="RealTimeDeviceConnection_Status" xml:space="preserve">
    <value>状态</value>
  </data>
  <data name="RealTimeDeviceConnection_Online" xml:space="preserve">
    <value>在线</value>
  </data>
  <data name="RealTimeDeviceConnection_Offline" xml:space="preserve">
    <value>离线</value>
  </data>
  <data name="RealTimeDeviceConnection_Timeout" xml:space="preserve">
    <value>超时</value>
  </data>
  <data name="RealTimeDeviceConnection_Normal" xml:space="preserve">
    <value>正常</value>
  </data>
  <data name="RealTimeDeviceConnection_Occupied" xml:space="preserve">
    <value>已占用</value>
  </data>
  <data name="RealTimeDeviceConnection_Free" xml:space="preserve">
    <value>空闲</value>
  </data>
</root>