﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SDHD.DC.DataMonitor.Models.Setting
{
    public class ChemicalComponentSettingViewModel : NotifyPropertyChangedModel
    {
        public string WindowTitle { set; get; }
        public string WellStationId { set; get; }

        private ChemicalComponentInfo _ChemicalComponent = new ChemicalComponentInfo();
        public ChemicalComponentInfo ChemicalComponent
        {
            get { return _ChemicalComponent; }
            set
            {
                _ChemicalComponent = value;
                OnPropertyChanged(nameof(ChemicalComponent));
            }
        }
        public ObservableCollection<ChemicalComponentInfo> ChemicalComponents { set; get; } = new ObservableCollection<ChemicalComponentInfo>();

        public class ChemicalComponentInfo : NotifyPropertyChangedModel
        {
            private string _ChemicalComponentId;
            public string ChemicalComponentId
            {
                get { return _ChemicalComponentId; }
                set
                {
                    if (_ChemicalComponentId == value) return;
                    _ChemicalComponentId = value;
                    OnPropertyChanged(nameof(ChemicalComponentId));
                }
            }
            private string _IndexNo;
            public string IndexNo
            {
                get { return _IndexNo; }
                set
                {
                    if (_IndexNo == value) return;
                    _IndexNo = value;
                    OnPropertyChanged(nameof(IndexNo));
                }
            }
            private string _Name;
            public string Name
            {
                get { return _Name; }
                set
                {
                    if (_Name == value) return;
                    _Name = value;
                    OnPropertyChanged(nameof(Name));
                }
            }
            private string _MoleValue;
            public string MoleValue
            {
                get { return _MoleValue; }
                set
                {
                    if (_MoleValue == value) return;
                    _MoleValue = value;
                    OnPropertyChanged(nameof(MoleValue));
                }
            }
        }
    }
}
