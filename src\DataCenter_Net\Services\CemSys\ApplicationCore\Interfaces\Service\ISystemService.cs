﻿using SDHD.DC.CemSys.ApplicationCore.Domain.Request;
using SDHD.DC.CemSys.ApplicationCore.Domain.Response;
using SDHD.DC.Utilities;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace SDHD.DC.CemSys.ApplicationCore.Interfaces.Service
{
    public interface ISystemService
    {
        Task<SysConfigResponse> GetSysConfig();
        Task<BizResponse> UpdateSysToDefaultWellStationId(string wellStationId);
        Task<RegistryResponse> Register(RegistryRequest request);
        Task<RegistryResponse> SetRegister(RegistryRequest request);
        Task<RegistryDetailResponse> GetRegisterData();
        Task<ServerConnectTestResponse> TestServerConnection(ServerConnectTestRequest request);
        Task CloseServerConnection();
        Task<string> GetDefaultWellStationTemplateId();
        Task<BizResponse> UpdateDefaultWellStationTemplateId(string defaultWellStationTemplateId);
    }
}
