using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Threading;
using System.Windows;
using System.Resources;
using System.Reflection;
using System.ComponentModel;

namespace SDHD.DC.DataMonitor.Resources.Localization
{
    /// <summary>
    /// Manages application language settings and localization
    /// </summary>
    public class LanguageManager : INotifyPropertyChanged
    {
        #region Singleton

        private static readonly Lazy<LanguageManager> _instance = new Lazy<LanguageManager>(() => new LanguageManager());

        /// <summary>
        /// Gets the singleton instance of LanguageManager
        /// </summary>
        public static LanguageManager Instance => _instance.Value;

        private LanguageManager()
        {
            // Initialize with default language and load saved preference if available
            CurrentLanguage = "zh-CN"; // Default to Chinese
            
            // Try to load saved language preference
            LoadLanguagePreference();
            
            // Set the current UI culture to the loaded language
            Thread.CurrentThread.CurrentUICulture = new CultureInfo(CurrentLanguage);
            
            // Initialize resource manager for the Strings.resx file
            ResourceManager = new ResourceManager("SDHD.DC.DataMonitor.Resources.Localization.Strings", 
                                                 Assembly.GetExecutingAssembly());
        }

        #endregion

        #region Properties

        private string _currentLanguage;
        /// <summary>
        /// Gets or sets the current language code
        /// </summary>
        public string CurrentLanguage
        {
            get => _currentLanguage;
            private set
            {
                if (_currentLanguage != value)
                {
                    _currentLanguage = value;
                    OnPropertyChanged(nameof(CurrentLanguage));
                }
            }
        }

        /// <summary>
        /// Resource manager for accessing localized strings
        /// </summary>
        public ResourceManager ResourceManager { get; private set; }

        /// <summary>
        /// Available languages in the application
        /// </summary>
        public List<LanguageInfo> AvailableLanguages { get; } = new List<LanguageInfo>
        {
            new LanguageInfo { DisplayName = "English", LanguageCode = "en-US" },
            new LanguageInfo { DisplayName = "中文", LanguageCode = "zh-CN" },
            new LanguageInfo { DisplayName = "Русский", LanguageCode = "ru-RU" }
        };

        #endregion

        #region Public Methods

        /// <summary>
        /// Gets a localized string from the resource file
        /// </summary>
        /// <param name="key">Resource key</param>
        /// <returns>Localized string</returns>
        public string GetString(string key)
        {
            try
            {
                return ResourceManager.GetString(key, CultureInfo.CurrentUICulture) ?? key;
            }
            catch
            {
                return key;
            }
        }

        /// <summary>
        /// Changes the application's current language
        /// </summary>
        /// <param name="languageCode">Language code (e.g., "en-US", "zh-CN")</param>
        public void ChangeLanguage(string languageCode)
        {
            if (string.IsNullOrEmpty(languageCode))
                throw new ArgumentNullException(nameof(languageCode));

            // Validate if the language is supported
            bool isValidLanguage = false;
            foreach (var lang in AvailableLanguages)
            {
                if (lang.LanguageCode.Equals(languageCode, StringComparison.OrdinalIgnoreCase))
                {
                    isValidLanguage = true;
                    break;
                }
            }

            if (!isValidLanguage)
                throw new ArgumentException($"Language '{languageCode}' is not supported.");

            // Set the current UI culture
            Thread.CurrentThread.CurrentUICulture = new CultureInfo(languageCode);

            // Update the current language
            CurrentLanguage = languageCode;

            // Save the selected language
            SaveLanguagePreference(languageCode);

            // Notify the application about language change
            OnLanguageChanged(EventArgs.Empty);
        }

        /// <summary>
        /// Loads the saved language preference from config/lan.ini file
        /// </summary>
        public void LoadLanguagePreference()
        {
            try
            {
                // Ensure config directory exists
                string configDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Sys");
                if (!Directory.Exists(configDir))
                {
                    return; // If directory doesn't exist, use default language
                }

                string languageFile = Path.Combine(configDir, "lan.ini");
                if (File.Exists(languageFile))
                {
                    string languageCode = File.ReadAllText(languageFile).Trim();
                    
                    // Validate the loaded language code
                    foreach (var lang in AvailableLanguages)
                    {
                        if (lang.LanguageCode.Equals(languageCode, StringComparison.OrdinalIgnoreCase))
                        {
                            CurrentLanguage = languageCode;
                            break;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Log the error if needed, but don't throw
                Console.WriteLine($"Error loading language preference: {ex.Message}");
                // Continue with default language
            }
        }

        /// <summary>
        /// Saves the current language preference to config/lan.ini file
        /// </summary>
        private void SaveLanguagePreference(string languageCode)
        {
            try
            {
                // Ensure config directory exists
                string configDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Sys");
                if (!Directory.Exists(configDir))
                {
                    Directory.CreateDirectory(configDir);
                }

                string languageFile = Path.Combine(configDir, "lan.ini");
                File.WriteAllText(languageFile, languageCode);
            }
            catch (Exception ex)
            {
                // Log the error if needed, but don't throw to prevent disrupting the UI
                Console.WriteLine($"Error saving language preference: {ex.Message}");
            }
        }

        #endregion

        #region Events

        /// <summary>
        /// Event raised when the language is changed
        /// </summary>
        public event EventHandler LanguageChanged;

        /// <summary>
        /// Raises the LanguageChanged event
        /// </summary>
        protected virtual void OnLanguageChanged(EventArgs e)
        {
            LanguageChanged?.Invoke(this, e);
        }

        #region INotifyPropertyChanged Implementation

        /// <summary>
        /// Event raised when a property value changes
        /// </summary>
        public event PropertyChangedEventHandler PropertyChanged;

        /// <summary>
        /// Raises the PropertyChanged event
        /// </summary>
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion

        #endregion
    }

    /// <summary>
    /// Represents language information including display name and language code
    /// </summary>
    public class LanguageInfo
    {
        /// <summary>
        /// Display name of the language (e.g., "English", "����")
        /// </summary>
        public string DisplayName { get; set; }

        /// <summary>
        /// Language code (e.g., "en-US", "zh-CN")
        /// </summary>
        public string LanguageCode { get; set; }
    }
}