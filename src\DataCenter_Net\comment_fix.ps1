# Comment out UserHandler references
$content = Get-Content 'WinForm\DataMonitor\MainWindow.xaml.cs' -Raw

# Comment out LogOut call
$content = $content -replace 'UserHandler\.Instance\.LogOut\(\);', '// UserHandler.Instance.LogOut();'

# Comment out RefreshLoginUser call and add simple assignment
$content = $content -replace '_CurrentUser = UserHandler\.Instance\.RefreshLoginUser\(\);', '// _CurrentUser = UserHandler.Instance.RefreshLoginUser();' + [Environment]::NewLine + '            _CurrentUser = null;'

# Save file
$content | Set-Content 'WinForm\DataMonitor\MainWindow.xaml.cs'

Write-Host "Commented out UserHandler references"
