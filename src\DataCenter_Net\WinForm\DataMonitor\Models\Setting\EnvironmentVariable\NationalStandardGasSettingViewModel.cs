﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Media;

namespace SDHD.DC.DataMonitor.Models.Setting.EnvironmentVariable
{
    internal class NationalStandardGasSettingViewModel : NotifyPropertyChangedModel
    {
        public string WellStationId { set; get; }
        public string CurrentWorkflowType { set; get; }
        private NationalStandardGasInfo _Record = new NationalStandardGasInfo();
        public NationalStandardGasInfo Record
        {
            get { return _Record; }
            set
            {
                _Record = value;
                OnPropertyChanged(nameof(Record));
            }
        }
        public List<SelectItem> WorkflowTypes { set; get; } = new List<SelectItem>();
        public List<NationalStandardGasInfo> NationalStandardGases { set; get; } = new List<NationalStandardGasInfo>();
        public List<SelectItem> PipelineMaterials { set; get; } = new List<SelectItem>();
        public List<SelectItem> OrificeMaterials { set; get; } = new List<SelectItem>();
        public class NationalStandardGasInfo : NotifyPropertyChangedModel
        {
            private string _WorkflowType;
            public string WorkflowType
            {
                get { return _WorkflowType; }
                set
                {
                    if (_WorkflowType == value) return;
                    _WorkflowType = value;
                    OnPropertyChanged(nameof(WorkflowType));
                }
            }
            private string _SpecificGravity;
            public string SpecificGravity
            {
                get { return _SpecificGravity; }
                set
                {
                    if (_SpecificGravity == value) return;
                    _SpecificGravity = value;
                    OnPropertyChanged(nameof(SpecificGravity));
                }
            }

            private string _WellStationAirPressure;
            public string WellStationAirPressure
            {
                get { return _WellStationAirPressure; }
                set
                {
                    if (_WellStationAirPressure == value) return;
                    _WellStationAirPressure = value;
                    OnPropertyChanged(nameof(WellStationAirPressure));
                }
            }

            private string _CO2MoleValue;
            public string CO2MoleValue
            {
                get { return _CO2MoleValue; }
                set
                {
                    if (_CO2MoleValue == value) return;
                    _CO2MoleValue = value;
                    OnPropertyChanged(nameof(CO2MoleValue));
                }
            }

            private string _N2MoleValue;
            public string N2MoleValue
            {
                get { return _N2MoleValue; }
                set
                {
                    if (_N2MoleValue == value) return;
                    _N2MoleValue = value;
                    OnPropertyChanged(nameof(N2MoleValue));
                }
            }

            private string _StdPipelineRunDiameter;
            public string StdPipelineRunDiameter
            {
                get { return _StdPipelineRunDiameter; }
                set
                {
                    if (_StdPipelineRunDiameter == value) return;
                    _StdPipelineRunDiameter = value;
                    OnPropertyChanged(nameof(StdPipelineRunDiameter));
                }
            }

            private string _StdOrificeRunDiameter;
            public string StdOrificeRunDiameter
            {
                get { return _StdOrificeRunDiameter; }
                set
                {
                    if (_StdOrificeRunDiameter == value) return;
                    _StdOrificeRunDiameter = value;
                    OnPropertyChanged(nameof(StdOrificeRunDiameter));
                }
            }

            private string _PipelineMaterial;
            public string PipelineMaterial
            {
                get { return _PipelineMaterial; }
                set
                {
                    if (_PipelineMaterial == value) return;
                    _PipelineMaterial = value;
                    OnPropertyChanged(nameof(PipelineMaterial));
                }
            }

            private string _OrificeMaterial;
            public string OrificeMaterial
            {
                get { return _OrificeMaterial; }
                set
                {
                    if (_OrificeMaterial == value) return;
                    _OrificeMaterial = value;
                    OnPropertyChanged(nameof(OrificeMaterial));
                }
            }
        }
    }
}
