[{"category": "EnvVairableWorkflowType", "codeName": "0", "codeValue": "1#"}, {"category": "EnvVairableWorkflowType", "codeName": "1", "codeValue": "2#"}, {"category": "EnvVairableWorkflowType", "codeName": "2", "codeValue": "3#"}, {"category": "EnvVairableWorkflowType", "codeName": "3", "codeValue": "4#"}, {"category": "EnvVairableWorkflowType", "codeName": "4", "codeValue": "5#"}, {"category": "EnvVairableWorkflowType", "codeName": "6", "codeValue": "7#"}, {"category": "EnvVairableWorkflowType", "codeName": "7", "codeValue": "8#"}, {"category": "EnvVairableWorkflowType", "codeName": "8", "codeValue": "9#"}, {"category": "EnvVairableWorkflowType", "codeName": "9", "codeValue": "10#"}, {"category": "EnvVairableWorkflowType", "codeName": "10", "codeValue": "11#"}, {"category": "EnvVairableWorkflowType", "codeName": "11", "codeValue": "12#"}, {"category": "EnvVairableWorkflowType", "codeName": "12", "codeValue": "13#"}, {"category": "EnvVairableWorkflowType", "codeName": "13", "codeValue": "14#"}, {"category": "EnvVairableWorkflowType", "codeName": "14", "codeValue": "15#"}, {"category": "EnvVairableWorkflowType", "codeName": "15", "codeValue": "16#"}]