﻿using SDHD.DC.DataCollection.ApplicationCore.Domain.Entities;
using System.Collections.Generic;
using System.Collections.ObjectModel;

namespace SDHD.DC.DataMonitor.Models.Setting
{
    public class AlarmListViewModel : NotifyPropertyChangedModel
    {
        public AlarmAssociation AlarmAssociation { get; set; }

        private string _Keyword = "";
        public string Keyword
        {
            get { return _Keyword; }
            set
            {
                _Keyword = value;
                OnPropertyChanged(nameof(Keyword));
            }
        }

        private string _ShowType = "-1";
        public string ShowType
        {
            get { return _ShowType; }
            set
            {
                _ShowType = value;
                OnPropertyChanged(nameof(ShowType));
            }
        }

        public List<SelectItem> ShowTypes { get; set; } = new()
        {
            new(){ Text="全部",Value="-1" },
            new(){ Text="浮点型",Value="1" },
            new(){ Text="离散型",Value="2" },
        };


        private ObservableCollection<AlarmInfo> _AlarmInfos = new();
        public ObservableCollection<AlarmInfo> AlarmInfos
        {
            get { return _AlarmInfos; }
            set
            {
                _AlarmInfos = value;
                OnPropertyChanged(nameof(AlarmInfos));
            }
        }

        public class AlarmInfo : NotifyPropertyChangedModel
        {
            public string Id { get; set; } = "";
            public string Sn { get; set; } = "";
            public string Name { get; set; } = "";
            public string IndexNo { get; set; } = "-1";
            public string AlarmDBao { get; set; } = "";
            public string AlarmDDBao { get; set; } = "";
            public string AlarmDDDBao { get; set; } = "";
            public string AlarmGBao { get; set; } = "";
            public string AlarmGGBao { get; set; } = "";
            public string AlarmGGGBao { get; set; } = "";
            public string AlarmDengBao { get; set; } = "";
            public string AlarmDBaoStr { get; set; } = "";
            public string AlarmDDBaoStr { get; set; } = "";
            public string AlarmDDDBaoStr { get; set; } = "";
            public string AlarmGBaoStr { get; set; } = "";
            public string AlarmGGBaoStr { get; set; } = "";
            public string AlarmGGGBaoStr { get; set; } = "";
            public string AlarmDengBaoStr { get; set; } = "";
            public int DeviceItemDataType { get; set; } = -1;

            /*
            private string _Name = "";
            public string Name
            {
                get { return _Name; }
                set
                {
                    if (_Name == value) return;
                    _Name = value;
                    OnPropertyChanged(nameof(Name));
                }
            }

            private string _IndexNo = "-1";
            public string IndexNo
            {
                get { return _IndexNo; }
                set
                {
                    if (_IndexNo == value) return;
                    _IndexNo = value;
                    OnPropertyChanged(nameof(IndexNo));
                }
            }

            private string _AlarmDBao = "";
            public string AlarmDBao
            {
                get { return _AlarmDBao; }
                set
                {
                    if (_AlarmDBao == value) return;
                    _AlarmDBao = value;
                    OnPropertyChanged(nameof(AlarmDBao));
                }
            }

            private string _AlarmDDBao = "";
            public string AlarmDDBao
            {
                get { return _AlarmDDBao; }
                set
                {
                    if (_AlarmDDBao == value) return;
                    _AlarmDDBao = value;
                    OnPropertyChanged(nameof(AlarmDDBao));
                }
            }

            private string _AlarmDDDBao = "";
            public string AlarmDDDBao
            {
                get { return _AlarmDDDBao; }
                set
                {
                    if (_AlarmDDDBao == value) return;
                    _AlarmDDDBao = value;
                    OnPropertyChanged(nameof(AlarmDDDBao));
                }
            }

            private string _AlarmGBao = "";
            public string AlarmGBao
            {
                get { return _AlarmGBao; }
                set
                {
                    if (_AlarmGBao == value) return;
                    _AlarmGBao = value;
                    OnPropertyChanged(nameof(AlarmGBao));
                }
            }

            private string _AlarmGGBao = "";
            public string AlarmGGBao
            {
                get { return _AlarmGGBao; }
                set
                {
                    if (_AlarmGGBao == value) return;
                    _AlarmGGBao = value;
                    OnPropertyChanged(nameof(AlarmGGBao));
                }
            }

            private string _AlarmGGGBao = "";
            public string AlarmGGGBao
            {
                get { return _AlarmGGGBao; }
                set
                {
                    if (_AlarmGGGBao == value) return;
                    _AlarmGGGBao = value;
                    OnPropertyChanged(nameof(AlarmGGGBao));
                }
            }

            private string _AlarmDengBao = "";
            public string AlarmDengBao
            {
                get { return _AlarmDengBao; }
                set
                {
                    if (_AlarmDengBao == value) return;
                    _AlarmDengBao = value;
                    OnPropertyChanged(nameof(AlarmDengBao));
                }
            }

            private string _AlarmDBaoStr = "";
            public string AlarmDBaoStr
            {
                get { return _AlarmDBaoStr; }
                set
                {
                    if (_AlarmDBaoStr == value) return;
                    _AlarmDBaoStr = value;
                    OnPropertyChanged(nameof(AlarmDBaoStr));
                }
            }

            private string _AlarmDDBaoStr = "";
            public string AlarmDDBaoStr
            {
                get { return _AlarmDDBaoStr; }
                set
                {
                    if (_AlarmDDBaoStr == value) return;
                    _AlarmDDBaoStr = value;
                    OnPropertyChanged(nameof(AlarmDDBaoStr));
                }
            }

            private string _AlarmDDDBaoStr = "";
            public string AlarmDDDBaoStr
            {
                get { return _AlarmDDDBaoStr; }
                set
                {
                    if (_AlarmDDDBaoStr == value) return;
                    _AlarmDDDBaoStr = value;
                    OnPropertyChanged(nameof(AlarmDDDBaoStr));
                }
            }

            private string _AlarmGBaoStr = "";
            public string AlarmGBaoStr
            {
                get { return _AlarmGBaoStr; }
                set
                {
                    if (_AlarmGBaoStr == value) return;
                    _AlarmGBaoStr = value;
                    OnPropertyChanged(nameof(AlarmGBaoStr));
                }
            }

            private string _AlarmGGBaoStr = "";
            public string AlarmGGBaoStr
            {
                get { return _AlarmGGBaoStr; }
                set
                {
                    if (_AlarmGGBaoStr == value) return;
                    _AlarmGGBaoStr = value;
                    OnPropertyChanged(nameof(AlarmGGBaoStr));
                }
            }

            private string _AlarmGGGBaoStr = "";
            public string AlarmGGGBaoStr
            {
                get { return _AlarmGGGBaoStr; }
                set
                {
                    if (_AlarmGGGBaoStr == value) return;
                    _AlarmGGGBaoStr = value;
                    OnPropertyChanged(nameof(AlarmGGGBaoStr));
                }
            }

            private string _AlarmDengBaoStr = "";
            public string AlarmDengBaoStr
            {
                get { return _AlarmDengBaoStr; }
                set
                {
                    if (_AlarmDengBaoStr == value) return;
                    _AlarmDengBaoStr = value;
                    OnPropertyChanged(nameof(AlarmDengBaoStr));
                }
            }

            */

        }


    }
}
