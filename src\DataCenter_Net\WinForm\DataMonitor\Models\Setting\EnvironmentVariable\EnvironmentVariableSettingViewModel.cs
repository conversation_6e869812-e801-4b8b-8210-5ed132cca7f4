﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Media;

namespace SDHD.DC.DataMonitor.Models.Setting.EnvironmentVariable
{
    internal class EnvironmentVariableSettingViewModel : NotifyPropertyChangedModel
    {
        public string WindowTitle { set; get; }
        public string WellStationId { set; get; }

        private EnvironmentVariableInfo _EnvironmentVariable = new EnvironmentVariableInfo();
        public EnvironmentVariableInfo EnvironmentVariable
        {
            get { return _EnvironmentVariable; }
            set
            {
                _EnvironmentVariable = value;
                OnPropertyChanged(nameof(EnvironmentVariable));
            }
        }
        public ObservableCollection<EnvironmentVariableInfo> EnvironmentVariables { get; set; } = new ObservableCollection<EnvironmentVariableInfo>();

        public class EnvironmentVariableInfo : NotifyPropertyChangedModel
        {
            public string Id { get; set; }

            private string _Name;
            public string Name
            {
                get { return _Name; }
                set
                {
                    if (_Name == value) return;
                    _Name = value;
                    OnPropertyChanged(nameof(Name));
                }
            }

            private string _Value;
            public string Value
            {
                get { return _Value; }
                set
                {
                    if (_Value == value) return;
                    _Value = value;
                    OnPropertyChanged(nameof(Value));
                }
            }

        }
    }
}
