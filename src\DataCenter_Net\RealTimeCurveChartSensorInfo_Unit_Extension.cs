using System;

namespace SDHD.DC.DataMonitor.UserControls.Chart
{
    /// <summary>
    /// RealTimeCurveChartSensorInfo 类的扩展，添加 Unit 字段支持
    /// 这个扩展确保传感器信息包含单位字段，用于数据点绘制时的单位显示
    /// </summary>
    public partial class RealTimeCurveChartSensorInfo
    {
        /// <summary>
        /// 传感器单位信息
        /// 用于在DrawSeriePointData时显示正确的单位，而不是使用坐标轴单位
        /// 这个字段应该直接从 sensorInfo.Unitstr 获取，确保与传感器设置同步
        /// </summary>
        public string Unit { get; set; } = "";

        /// <summary>
        /// 更新传感器信息，包括名称和单位
        /// 这个方法确保名称和单位同时更新，保持数据一致性
        /// </summary>
        /// <param name="name">传感器名称</param>
        /// <param name="unit">传感器单位</param>
        public void UpdateSensorInfo(string name, string unit)
        {
            this.Name = name ?? "";
            this.Unit = unit ?? "";
        }

        /// <summary>
        /// 从SensorInfo对象更新传感器信息
        /// 直接从protobuf的SensorInfo对象获取最新的名称和单位信息
        /// </summary>
        /// <param name="sensorInfo">protobuf的SensorInfo对象</param>
        public void UpdateFromSensorInfo(ProjectStruct.SensorInfo sensorInfo)
        {
            if (sensorInfo != null)
            {
                this.Name = sensorInfo.Name ?? "";
                this.Unit = sensorInfo.Unitstr ?? "";
            }
        }

        /// <summary>
        /// 获取用于显示的完整传感器信息字符串
        /// 格式：传感器名称 (单位)
        /// </summary>
        /// <returns>格式化的传感器信息字符串</returns>
        public string GetDisplayText()
        {
            if (string.IsNullOrEmpty(Unit))
            {
                return Name ?? "";
            }
            return $"{Name ?? ""} ({Unit})";
        }

        /// <summary>
        /// 检查传感器信息是否有效
        /// 有效的传感器信息应该有名称，单位可以为空
        /// </summary>
        /// <returns>是否有效</returns>
        public bool IsValid()
        {
            return !string.IsNullOrEmpty(Name);
        }
    }
}

/// <summary>
/// 扩展方法类，为RealTimeCurveChartSensorInfo提供便捷的操作方法
/// </summary>
public static class RealTimeCurveChartSensorInfoExtensions
{
    /// <summary>
    /// 批量更新传感器信息列表的单位
    /// 从SensorInfoList中获取最新的单位信息并更新到RealTimeCurveChartSensorInfo列表
    /// </summary>
    /// <param name="sensorInfos">要更新的传感器信息列表</param>
    /// <param name="sensorInfoList">最新的SensorInfoList</param>
    /// <returns>更新的传感器数量</returns>
    public static int UpdateUnitsFromSensorInfoList(
        this System.Collections.Generic.List<SDHD.DC.DataMonitor.UserControls.Chart.RealTimeCurveChartSensorInfo> sensorInfos,
        ProjectStruct.SensorInfoList sensorInfoList)
    {
        if (sensorInfos == null || sensorInfoList?.Items == null)
            return 0;

        int updatedCount = 0;

        foreach (var sensorInfo in sensorInfos)
        {
            if (sensorInfo.SensorId >= 0 && sensorInfo.SensorId < sensorInfoList.Items.Count)
            {
                var latestInfo = sensorInfoList.Items[sensorInfo.SensorId];
                string newName = latestInfo.Name ?? "";
                string newUnit = latestInfo.Unitstr ?? "";

                bool hasChanges = false;

                if (sensorInfo.Name != newName)
                {
                    sensorInfo.Name = newName;
                    hasChanges = true;
                }

                if (sensorInfo.Unit != newUnit)
                {
                    sensorInfo.Unit = newUnit;
                    hasChanges = true;
                }

                if (hasChanges)
                {
                    updatedCount++;
                }
            }
        }

        return updatedCount;
    }

    /// <summary>
    /// 验证传感器信息列表中的单位字段是否与SensorInfoList同步
    /// 用于调试和验证数据一致性
    /// </summary>
    /// <param name="sensorInfos">要验证的传感器信息列表</param>
    /// <param name="sensorInfoList">参考的SensorInfoList</param>
    /// <returns>不同步的传感器数量</returns>
    public static int ValidateUnitSynchronization(
        this System.Collections.Generic.List<SDHD.DC.DataMonitor.UserControls.Chart.RealTimeCurveChartSensorInfo> sensorInfos,
        ProjectStruct.SensorInfoList sensorInfoList)
    {
        if (sensorInfos == null || sensorInfoList?.Items == null)
            return 0;

        int mismatchCount = 0;

        foreach (var sensorInfo in sensorInfos)
        {
            if (sensorInfo.SensorId >= 0 && sensorInfo.SensorId < sensorInfoList.Items.Count)
            {
                var latestInfo = sensorInfoList.Items[sensorInfo.SensorId];
                string expectedUnit = latestInfo.Unitstr ?? "";

                if (sensorInfo.Unit != expectedUnit)
                {
                    mismatchCount++;
                    System.Diagnostics.Debug.WriteLine(
                        $"Unit mismatch for Sensor {sensorInfo.SensorId}: " +
                        $"Current='{sensorInfo.Unit}', Expected='{expectedUnit}'");
                }
            }
        }

        return mismatchCount;
    }
}

/// <summary>
/// 数据点绘制时的单位处理辅助类
/// 提供从SeriesProperty获取单位而不是从坐标轴获取单位的方法
/// 重点：实时从Global.Messages获取最新的传感器单位信息
/// </summary>
public static class DrawSeriePointDataUnitHelper
{
    /// <summary>
    /// 从RealTimeCurveChartSensorInfo获取数据点显示用的单位
    /// 在DrawSeriePointData方法中使用，确保显示的是传感器的实际单位而不是坐标轴单位
    /// 优先从Global.Messages获取最新单位，确保单位信息是最新的
    /// </summary>
    /// <param name="sensorInfo">传感器信息对象</param>
    /// <param name="fallbackUnit">备用单位（如果传感器单位为空时使用）</param>
    /// <returns>用于显示的单位字符串</returns>
    public static string GetDisplayUnit(SDHD.DC.DataMonitor.UserControls.Chart.RealTimeCurveChartSensorInfo sensorInfo, string fallbackUnit = "")
    {
        if (sensorInfo == null)
            return fallbackUnit ?? "";

        // 🎯 关键修复：实时从Global.Messages获取最新的传感器单位
        string latestUnit = GetLatestSensorUnit(sensorInfo.SensorId);
        if (!string.IsNullOrEmpty(latestUnit))
            return latestUnit;

        // 备选1：使用传感器自身的单位
        if (!string.IsNullOrEmpty(sensorInfo.Unit))
            return sensorInfo.Unit;

        // 备选2：如果传感器单位为空，使用备用单位
        return fallbackUnit ?? "";
    }

    /// <summary>
    /// 实时从Global.Messages中获取指定传感器的最新单位信息
    /// 这是解决单位不更新问题的关键方法
    /// </summary>
    /// <param name="sensorId">传感器ID</param>
    /// <returns>最新的传感器单位，如果获取失败返回null</returns>
    public static string GetLatestSensorUnit(int sensorId)
    {
        try
        {
            // 从Global.Messages获取最新的SensorInfoList
            if (Commiunication.Global.Messages.TryGetValue("SensorInfoList", out var sensorMsg) &&
                sensorMsg is ProjectStruct.SensorInfoList sensorInfoList)
            {
                // 根据SensorId查找对应的传感器信息
                if (sensorId >= 0 && sensorId < sensorInfoList.Items.Count)
                {
                    var latestSensorInfo = sensorInfoList.Items[sensorId];
                    return latestSensorInfo.Unitstr ?? "";
                }
            }
        }
        catch (Exception ex)
        {
            // 记录错误但不抛出异常，避免影响绘制
            System.Diagnostics.Debug.WriteLine($"Error getting latest sensor unit for sensor {sensorId}: {ex.Message}");
        }

        return null;
    }

    /// <summary>
    /// 实时从Global.Messages中获取指定传感器的最新名称信息
    /// 配合单位获取，确保名称也是最新的
    /// </summary>
    /// <param name="sensorId">传感器ID</param>
    /// <returns>最新的传感器名称，如果获取失败返回null</returns>
    public static string GetLatestSensorName(int sensorId)
    {
        try
        {
            // 从Global.Messages获取最新的SensorInfoList
            if (Commiunication.Global.Messages.TryGetValue("SensorInfoList", out var sensorMsg) &&
                sensorMsg is ProjectStruct.SensorInfoList sensorInfoList)
            {
                // 根据SensorId查找对应的传感器信息
                if (sensorId >= 0 && sensorId < sensorInfoList.Items.Count)
                {
                    var latestSensorInfo = sensorInfoList.Items[sensorId];
                    return latestSensorInfo.Name ?? "";
                }
            }
        }
        catch (Exception ex)
        {
            // 记录错误但不抛出异常，避免影响绘制
            System.Diagnostics.Debug.WriteLine($"Error getting latest sensor name for sensor {sensorId}: {ex.Message}");
        }

        return null;
    }

    /// <summary>
    /// 格式化数据点的显示文本
    /// 包含数值和单位，用于鼠标悬停或数据标签显示
    /// 使用最新的传感器单位信息，确保显示的单位是最新的
    /// </summary>
    /// <param name="value">数据值</param>
    /// <param name="sensorInfo">传感器信息</param>
    /// <param name="precision">小数位数</param>
    /// <returns>格式化的显示文本</returns>
    public static string FormatDataPointText(double value, SDHD.DC.DataMonitor.UserControls.Chart.RealTimeCurveChartSensorInfo sensorInfo, int precision = 2)
    {
        if (sensorInfo == null)
            return value.ToString($"F{precision}");

        // 🎯 使用实时获取的最新单位信息
        string unit = GetDisplayUnit(sensorInfo);
        string formattedValue = value.ToString($"F{precision}");

        if (string.IsNullOrEmpty(unit))
            return formattedValue;

        return $"{formattedValue} {unit}";
    }

    /// <summary>
    /// 格式化数据点的显示文本（重载版本）
    /// 直接传入传感器ID，实时获取最新的传感器信息
    /// 这是推荐的使用方式，确保获取到最新的传感器单位
    /// </summary>
    /// <param name="value">数据值</param>
    /// <param name="sensorId">传感器ID</param>
    /// <param name="precision">小数位数</param>
    /// <param name="fallbackUnit">备用单位</param>
    /// <returns>格式化的显示文本</returns>
    public static string FormatDataPointText(double value, int sensorId, int precision = 2, string fallbackUnit = "")
    {
        // 🎯 直接从Global.Messages获取最新单位，确保是最新的
        string unit = GetLatestSensorUnit(sensorId);
        if (string.IsNullOrEmpty(unit))
            unit = fallbackUnit;

        string formattedValue = value.ToString($"F{precision}");

        if (string.IsNullOrEmpty(unit))
            return formattedValue;

        return $"{formattedValue} {unit}";
    }

    /// <summary>
    /// 创建数据点的工具提示文本
    /// 包含传感器名称、数值和单位
    /// 使用最新的传感器信息，确保显示的名称和单位都是最新的
    /// </summary>
    /// <param name="sensorInfo">传感器信息</param>
    /// <param name="value">数据值</param>
    /// <param name="timestamp">时间戳（可选）</param>
    /// <param name="precision">数值精度</param>
    /// <returns>工具提示文本</returns>
    public static string CreateTooltipText(
        SDHD.DC.DataMonitor.UserControls.Chart.RealTimeCurveChartSensorInfo sensorInfo,
        double value,
        DateTime? timestamp = null,
        int precision = 2)
    {
        if (sensorInfo == null)
            return value.ToString($"F{precision}");

        // 🎯 使用实时获取的最新传感器信息
        string latestName = GetLatestSensorName(sensorInfo.SensorId);
        string sensorName = !string.IsNullOrEmpty(latestName) ? latestName : (sensorInfo.Name ?? "Unknown Sensor");

        string unit = GetDisplayUnit(sensorInfo);
        string formattedValue = value.ToString($"F{precision}");

        string result = $"{sensorName}: {formattedValue}";

        if (!string.IsNullOrEmpty(unit))
            result += $" {unit}";

        if (timestamp.HasValue)
            result += $"\n{timestamp.Value:yyyy-MM-dd HH:mm:ss}";

        return result;
    }

    /// <summary>
    /// 创建数据点的工具提示文本（重载版本）
    /// 直接传入传感器ID，实时获取最新的传感器信息
    /// 这是推荐的使用方式，确保获取到最新的传感器名称和单位
    /// </summary>
    /// <param name="sensorId">传感器ID</param>
    /// <param name="value">数据值</param>
    /// <param name="timestamp">时间戳（可选）</param>
    /// <param name="precision">数值精度</param>
    /// <param name="fallbackName">备用传感器名称</param>
    /// <param name="fallbackUnit">备用单位</param>
    /// <returns>工具提示文本</returns>
    public static string CreateTooltipText(
        int sensorId,
        double value,
        DateTime? timestamp = null,
        int precision = 2,
        string fallbackName = "Unknown Sensor",
        string fallbackUnit = "")
    {
        // 🎯 直接从Global.Messages获取最新信息，确保是最新的
        string sensorName = GetLatestSensorName(sensorId);
        if (string.IsNullOrEmpty(sensorName))
            sensorName = fallbackName;

        string unit = GetLatestSensorUnit(sensorId);
        if (string.IsNullOrEmpty(unit))
            unit = fallbackUnit;

        string formattedValue = value.ToString($"F{precision}");
        string result = $"{sensorName}: {formattedValue}";

        if (!string.IsNullOrEmpty(unit))
            result += $" {unit}";

        if (timestamp.HasValue)
            result += $"\n{timestamp.Value:yyyy-MM-dd HH:mm:ss}";

        return result;
    }

    /// <summary>
    /// 专门为OnMouseMove函数提供的单位获取方法
    /// 解决"SelectedSenStr += $" {YAxisproperts[SelectedYAxis].Name}""问题
    /// 优先从传感器属性获取单位，而不是从坐标轴获取
    /// </summary>
    /// <param name="sensorId">传感器ID</param>
    /// <param name="fallbackYAxisUnit">备用的坐标轴单位</param>
    /// <returns>用于OnMouseMove显示的单位字符串</returns>
    public static string GetUnitForMouseMove(int sensorId, string fallbackYAxisUnit = "")
    {
        try
        {
            // 🎯 第一优先：实时从Global.Messages获取最新的传感器单位
            string latestUnit = GetLatestSensorUnit(sensorId);
            if (!string.IsNullOrEmpty(latestUnit))
            {
                return latestUnit;
            }

            // 第二优先：使用备用的坐标轴单位（保持兼容性）
            if (!string.IsNullOrEmpty(fallbackYAxisUnit))
            {
                return fallbackYAxisUnit;
            }
        }
        catch (Exception ex)
        {
            // 记录错误但不抛出异常，避免影响鼠标移动事件
            System.Diagnostics.Debug.WriteLine($"Error in GetUnitForMouseMove for sensor {sensorId}: {ex.Message}");
        }

        return "";
    }

    /// <summary>
    /// 为OnMouseMove函数格式化完整的传感器信息字符串
    /// 包含传感器名称、数值和单位
    /// </summary>
    /// <param name="sensorId">传感器ID</param>
    /// <param name="value">传感器数值</param>
    /// <param name="precision">数值精度</param>
    /// <param name="fallbackName">备用传感器名称</param>
    /// <param name="fallbackYAxisUnit">备用坐标轴单位</param>
    /// <returns>格式化的传感器信息字符串</returns>
    public static string FormatSensorInfoForMouseMove(
        int sensorId,
        double value,
        int precision = 2,
        string fallbackName = "",
        string fallbackYAxisUnit = "")
    {
        try
        {
            // 获取传感器名称（优先使用最新的）
            string sensorName = GetLatestSensorName(sensorId);
            if (string.IsNullOrEmpty(sensorName))
            {
                sensorName = !string.IsNullOrEmpty(fallbackName) ? fallbackName : $"Sensor {sensorId}";
            }

            // 获取传感器单位（优先使用最新的）
            string sensorUnit = GetUnitForMouseMove(sensorId, fallbackYAxisUnit);

            // 格式化数值
            string formattedValue = value.ToString($"F{precision}");

            // 构建完整的显示字符串
            string result = $"{sensorName}: {formattedValue}";

            if (!string.IsNullOrEmpty(sensorUnit))
            {
                result += $" {sensorUnit}";
            }

            return result;
        }
        catch (Exception ex)
        {
            // 备用显示方式
            System.Diagnostics.Debug.WriteLine($"Error in FormatSensorInfoForMouseMove for sensor {sensorId}: {ex.Message}");
            return $"Sensor {sensorId}: {value:F{precision}}";
        }
    }
}
