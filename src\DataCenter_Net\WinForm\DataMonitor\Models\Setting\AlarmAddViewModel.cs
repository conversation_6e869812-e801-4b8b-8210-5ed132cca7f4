﻿using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO.Packaging;

namespace SDHD.DC.DataMonitor.Models.Setting
{
    public class AlarmAddViewModel : NotifyPropertyChangedModel
    {
        private string _RealTimeValue;
        public string RealTimeValue
        {
            get { return _RealTimeValue; }
            set
            {
                _RealTimeValue = value;
                OnPropertyChanged(nameof(RealTimeValue));
            }
        }


        private AlarmListViewModel.AlarmInfo _AlarmInfo = new();
        public AlarmListViewModel.AlarmInfo AlarmInfo
        {
            get { return _AlarmInfo; }
            set
            {
                _AlarmInfo = value;
                OnPropertyChanged(nameof(AlarmInfo));
            }
        }

        public bool UnDengBao { get { return !OnlyDengBao; } }
        public bool OnlyDengBao { get { return AlarmInfo.DeviceItemDataType > 4; } }

        
    }
}
