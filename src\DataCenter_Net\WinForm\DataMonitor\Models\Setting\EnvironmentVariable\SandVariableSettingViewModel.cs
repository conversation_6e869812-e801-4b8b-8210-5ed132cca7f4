﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Media;

namespace SDHD.DC.DataMonitor.Models.Setting.EnvironmentVariable
{
    internal class SandVariableSettingViewModel : NotifyPropertyChangedModel
    {
        public string WellStationId { set; get; }

        public string CurrentWorkflowType { set; get; }

        private SandVariableInfo _Record = new SandVariableInfo();
        public SandVariableInfo Record
        {
            get { return _Record; }
            set
            {
                _Record = value;
                OnPropertyChanged(nameof(Record));
            }
        }



        public List<SelectItem> WorkflowTypes { set; get; } = new List<SelectItem>();
        public List<SelectItem> PipelineMediums { set; get; } = new List<SelectItem>();
        public List<SandVariableInfo> SandVariables { set; get; } = new List<SandVariableInfo>();
        public class SandVariableInfo : NotifyPropertyChangedModel
        {
            private string _WorkflowType;
            public string WorkflowType
            {
                get { return _WorkflowType; }
                set
                {
                    if (value == _WorkflowType) return;
                    _WorkflowType = value;
                    OnPropertyChanged(nameof(WorkflowType));
                }
            }
            private string _PipelineMedium;
            public string PipelineMedium
            {
                get { return _PipelineMedium; }
                set
                {
                    if (value == _PipelineMedium) return;
                    _PipelineMedium = value;
                    OnPropertyChanged(nameof(PipelineMedium));
                }
            }
            private bool _IsAutoPipelineMedium;
            public bool IsAutoPipelineMedium
            {
                get { return _IsAutoPipelineMedium; }
                set
                {
                    if (value == _IsAutoPipelineMedium) return;
                    _IsAutoPipelineMedium = value;
                    OnPropertyChanged(nameof(IsAutoPipelineMedium));
                }
            }

            private bool _IsNotGas = true;
            public bool IsNotGas
            {
                get { return _IsNotGas; }
                set
                {
                    if (_IsNotGas == value) return;
                    _IsNotGas = value;
                    OnPropertyChanged(nameof(IsNotGas));
                }
            }
            private string _GasExpressCoefficient;
            public string GasExpressCoefficient
            {
                get { return _GasExpressCoefficient; }
                set
                {
                    if (_GasExpressCoefficient == value) return;
                    _GasExpressCoefficient = value;
                    OnPropertyChanged(nameof(GasExpressCoefficient));
                }
            }

            private string _PipelineInnerDiameter;
            public string PipelineInnerDiameter
            {
                get { return _PipelineInnerDiameter; }
                set
                {
                    if (_PipelineInnerDiameter == value) return;
                    _PipelineInnerDiameter = value;
                    OnPropertyChanged(nameof(PipelineInnerDiameter));
                }
            }
            private string _BackgroudNoise;
            public string BackgroudNoise
            {
                get { return _BackgroudNoise; }
                set
                {
                    if (_BackgroudNoise == value) return;
                    _BackgroudNoise = value;
                    OnPropertyChanged(nameof(BackgroudNoise));
                }
            }

            private bool _IsAutoBackgroudNoise;
            public bool IsAutoBackgroudNoise
            {
                get { return _IsAutoBackgroudNoise; }
                set
                {
                    if (_IsAutoBackgroudNoise == value) return;
                    _IsAutoBackgroudNoise = value;
                    OnPropertyChanged(nameof(IsAutoBackgroudNoise));
                }
            }

            private string _StableRawTimeRange;
            public string StableRawTimeRange
            {
                get { return _StableRawTimeRange; }
                set
                {
                    if (_StableRawTimeRange == value) return;
                    _StableRawTimeRange = value;
                    OnPropertyChanged(nameof(StableRawTimeRange));
                }
            }

            private string _NoiseIncreaseRange;
            public string NoiseIncreaseRange
            {
                get { return _NoiseIncreaseRange; }
                set
                {
                    if (_NoiseIncreaseRange == value) return;
                    _NoiseIncreaseRange = value;
                    OnPropertyChanged(nameof(NoiseIncreaseRange));
                }
            }
        }
    }
}
