﻿using Commiunication;
using ProjectStruct;
using SDHD.DC.DataCollection.ApplicationCore.Domain.Request;
using SDHD.DC.DataCollection.ApplicationCore.Interfaces.Service;
using SDHD.DC.DataCollection.ApplicationCore.Domain.Entities;
using SDHD.DC.DataMonitor.Models;
using SDHD.DC.DataMonitor.Models.WellStation;
using SDHD.DC.DataMonitor.Resources.Localization;
using SDHD.DC.DataMonitor.Utils;
using SDHD.DC.Utilities;
using SDHD.DC.Utilities.Constants;
using SDHD.DC.Utilities.Logging;
using SDHD.DC.Utilities.ServiceExtensions;
using SDHD.DC.Utilities.WPF;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Timers;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using static ProjectStruct.SysBase.Types;
using static ProjectStruct.SysBase.Types.deviceBase.Types;

namespace SDHD.DC.DataMonitor.Views.WellStation
{
    public partial class RealTimeDeviceListWindow : Window
    {
        private ExtTimer _Timer;
        IDeviceService _DeviceService;
        private int _TimeTotalCounter = 0;
        private const int _SecondCounter = 1;
        private const int TimeTotalMax = 72000;
        RealTimeDeviceListViewModel _Model;
        private string _WellStationId;

        // 🎯 新增：从Global.Messages获取数据的相关字段
        private moduleList _ModuleList;
        private moduleItemList _ModuleItemList;
        private SysBase.Types.ProBase.Types.SensorInfoList _SensorInfoList;

        public Action<string, string, string, DeviceItemInfo> DeviceSelectedEvent;

        /// <summary>
        /// 🎯 新增：设备选择事件
        /// </summary>
        public event Action<RealTimeDeviceListViewModel.DeviceInfo> DeviceSelected;

        /// <summary>
        /// 🎯 新增：通道选择事件
        /// </summary>
        public event Action<DeviceChannelInfo> ChannelSelected;

        public RealTimeDeviceListWindow(string fromWellStationId)
        {
            InitializeComponent();
            _WellStationId = fromWellStationId;
            _Model = new();
            _Timer = new(TimeSpan.FromMilliseconds(1000), OnTimerTick);
            _DeviceService = ServiceHelper.GetService<IDeviceService>();
            
            // 🎯 修正：订阅Global消息事件 - 应该订阅OnMessageReceived
            Global.OnMessageReceived += OnGlobalMessageReceived;
            
            Init();
            this.DataContext = _Model;
        }

        /// <summary>
        /// 🎯 修正：处理Global消息接收事件 - 增量更新策略
        /// 当设备数据更新时，智能更新设备列表，保持用户界面状态
        /// </summary>
        private void OnGlobalMessageReceived(Google.Protobuf.IMessage msg)
        {
            try
            {
                // 在UI线程中处理消息
                this.Dispatcher.BeginInvoke(() =>
                {
                    if (msg is moduleItemList moduleItemList)
                    {
                        // moduleItemList更新：检查是否需要添加新设备项
                        UpdateDeviceStructure(moduleItemList);
                    }
                    else if (msg is moduleList moduleList)
                    {
                        // moduleList更新：实时更新设备数据值
                        UpdateDeviceValues(moduleList);
                    }
                    else if (msg is SysBase.Types.ProBase.Types.SensorInfoList sensorInfoList)
                    {
                        // SensorInfoList更新：更新传感器相关信息
                        UpdateSensorInfo(sensorInfoList);
                    }
                });
            }
            catch (Exception ex)
            {
                Logger.Write(ex);
                Debug.WriteLine($"🎯 OnGlobalMessageReceived异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 🎯 新增：从Global.Messages刷新数据
        /// 获取最新的设备和传感器信息
        /// </summary>
        private void RefreshGlobalMessages()
        {
            try
            {
                _ModuleList = Global.Messages.GetValueOrDefault("moduleList") as moduleList;
                _ModuleItemList = Global.Messages.GetValueOrDefault("moduleItemList") as moduleItemList;
                _SensorInfoList = Global.Messages.GetValueOrDefault("SensorInfoList") as SysBase.Types.ProBase.Types.SensorInfoList;

                Debug.WriteLine($"Global Messages - ModuleList: {_ModuleList?.Moudels?.Count ?? 0}, ModuleItemList: {_ModuleItemList?.Moudels?.Count ?? 0}, SensorInfoList: {_SensorInfoList?.Items?.Count ?? 0}");
            }
            catch (Exception ex)
            {
                Logger.Write(ex);
            }
        }

        /// <summary>
        /// 🎯 新增：增量更新设备结构 - 基于moduleItemList
        /// 只有当设备不存在时才添加，避免重复创建和界面闪烁
        /// </summary>
        private void UpdateDeviceStructure(moduleItemList moduleItemList)
        {
            try
            {
                if (moduleItemList?.Moudels == null) return;

                foreach (var moduleItem in moduleItemList.Moudels)
                {
                    if (string.IsNullOrWhiteSpace(moduleItem.Key)) continue;

                    // 检查设备是否已存在
                    var existingDevice = _Model.DeviceInfos.FirstOrDefault(d => d.ModuleSN == moduleItem.Key);
                    if (existingDevice != null)
                    {
                        // 🎯 设备已存在，确保通道信息完整（避免展开时卡住）
                        EnsureChannelsComplete(existingDevice, moduleItem.Value);

                        // 🎯 更新设备的地址和类型信息（可能之前创建时没有moduleList数据）
                        UpdateDeviceAddressAndType(existingDevice, moduleItem.Key);
                        continue;
                    }

                    // 🎯 设备类型从 moduleItem 的 names 获取，地址从 moduleList 获取
                    string ipAddress = "";
                    //  设备类型来自 moduleItem.names，需要 UTF-8 转换
                    string deviceType = "";
                    if (!string.IsNullOrEmpty(moduleItem.Value?.Names))
                    {
                        try
                        {
                            // 如果 Names 字段已经是正确的字符串，直接使用
                            deviceType = moduleItem.Value.Names;
                            
                            // 如果显示乱码，可能需要字节转换（根据实际情况调整）
                            // byte[] nameBytes = Encoding.Default.GetBytes(moduleItem.Value.Names);
                            // deviceType = Encoding.UTF8.GetString(nameBytes);
                        }
                        catch (Exception ex)
                        {
                            Debug.WriteLine($" 设备类型转换失败: {ex.Message}");
                            deviceType = moduleItem.Value?.Names ?? "";
                        }
                    }
                    
                    Debug.WriteLine($"🎯 处理设备 {moduleItem.Key}，设备类型: {deviceType}");

                    // 🎯 只从 moduleList 获取 IP 地址
                    if (_ModuleList?.Moudels != null)
                    {
                        var correspondingModule = _ModuleList.Moudels.FirstOrDefault(m => m.Key == moduleItem.Key);
                        if (correspondingModule.Value != null)
                        {
                            ipAddress = correspondingModule.Value.Addr ?? "";
                            Debug.WriteLine($"🎯 找到对应模块数据: SN={moduleItem.Key}, IP={ipAddress}, 类型={deviceType}");
                        }
                        else
                        {
                            Debug.WriteLine($"🎯 未找到对应模块数据: SN={moduleItem.Key}");
                        }
                    }
                    else
                    {
                        Debug.WriteLine($"🎯 _ModuleList为空，无法获取IP地址");
                    }

                    // 创建新设备项
                    var deviceInfo = new RealTimeDeviceListViewModel.DeviceInfo
                    {
                        ModuleSN = moduleItem.Key,
                        IPAddress = ipAddress, // 🎯 从 dataInfo 获取
                        DeviceTypeDesc = deviceType, // 🎯 从 dataInfo 获取
                        IsConnected = !string.IsNullOrEmpty(ipAddress), // 有IP地址说明可能连接
                        IsTimeout = true,    // 初始状态
                        Channels = new ObservableCollection<DeviceChannelInfo>(),
                        ModuleItemData = moduleItem.Value // 🎯 保存原始数据，用于延迟加载通道
                    };

                    // 🎯 延迟加载策略：先创建一个占位通道，展开时再加载完整通道
                    CreatePlaceholderChannel(deviceInfo);

                    // 添加到设备列表
                    _Model.DeviceInfos.Add(deviceInfo);
                    Debug.WriteLine($"🎯 新增设备: {deviceInfo.ModuleSN}, IP: {deviceInfo.IPAddress}, 类型: {deviceInfo.DeviceTypeDesc}, 通道数: {deviceInfo.Channels.Count}");
                }

                _Model.DeviceCount = _Model.DeviceInfos.Count;

                // 🎯 确保 TreeView 数据绑定
                if (_Model.DeviceInfos.Count > 0 && DeviceTreeView.ItemsSource == null)
                {
                    DeviceTreeView.ItemsSource = _Model.DeviceInfos;
                    Debug.WriteLine($"🎯 设置 TreeView 数据源，设备数量: {_Model.DeviceInfos.Count}");
                }

                // 🎯 批量更新所有设备的地址和类型信息（以防moduleList已经存在）
                foreach (var device in _Model.DeviceInfos)
                {
                    UpdateDeviceAddressAndType(device, device.ModuleSN);
                }
            }
            catch (Exception ex)
            {
                Logger.Write(ex);
                Debug.WriteLine($"🎯 UpdateDeviceStructure异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 🎯 新增：创建完整的通道信息，确保第一时间就有完整数据
        /// </summary>
        private void CreateCompleteChannels(RealTimeDeviceListViewModel.DeviceInfo deviceInfo, moduleItem moduleItem)
        {
            if (moduleItem?.Items == null) return;

            for (int i = 0; i < moduleItem.Items.Count; i++)
            {
                var item = moduleItem.Items[i];
                var channelInfo = new DeviceChannelInfo
                {
                    Name = item?.Names ?? string.Format(ResourceHelper.GetString("RealTimeDeviceList_ChannelPrefix"), i),
                    IndexNo = i.ToString(),
                    DataTypeDesc = GetDataTypeDescription((int)(item?.ModbusDBTypeField ?? 0)),
                    Value = "-",
                    IsTimeout = true,
                    Remark = "" // 暂时留空
                };
                deviceInfo.Channels.Add(channelInfo);
            }

            Debug.WriteLine($"🎯 为设备 {deviceInfo.ModuleSN} 创建了 {deviceInfo.Channels.Count} 个通道");
        }

        /// <summary>
        /// 🎯 新增：确保已存在设备的通道信息完整
        /// </summary>
        private void EnsureChannelsComplete(RealTimeDeviceListViewModel.DeviceInfo deviceInfo, moduleItem moduleItem)
        {
            if (moduleItem?.Items == null) return;

            // 如果通道数量不匹配，重新创建通道列表
            if (deviceInfo.Channels.Count != moduleItem.Items.Count)
            {
                deviceInfo.Channels.Clear();
                CreateCompleteChannels(deviceInfo, moduleItem);
                Debug.WriteLine($"🎯 重新创建设备 {deviceInfo.ModuleSN} 的通道信息，通道数: {deviceInfo.Channels.Count}");
            }
        }

        /// <summary>
        /// 🎯 新增：创建占位通道，避免展开时卡顿
        /// </summary>
        private void CreatePlaceholderChannel(RealTimeDeviceListViewModel.DeviceInfo deviceInfo)
        {
            // 创建一个占位通道，表示"正在加载..."
            var placeholderChannel = new DeviceChannelInfo
            {
                Name = ResourceHelper.GetString("RealTimeDeviceList_LoadingChannels"),
                IndexNo = "-",
                DataTypeDesc = "-",
                Value = "-",
                IsTimeout = false,
                Remark = "展开时将加载完整通道信息"
            };
            deviceInfo.Channels.Add(placeholderChannel);
            deviceInfo.ChannelsLoaded = false;

            Debug.WriteLine($"🎯 为设备 {deviceInfo.ModuleSN} 创建占位通道");
        }

        /// <summary>
        /// 🎯 新增：更新设备的地址信息（设备类型来自 moduleItem，不需要更新）
        /// </summary>
        private void UpdateDeviceAddressAndType(RealTimeDeviceListViewModel.DeviceInfo deviceInfo, string moduleSN)
        {
            try
            {
                if (_ModuleList?.Moudels != null)
                {
                    var correspondingModule = _ModuleList.Moudels.FirstOrDefault(m => m.Key == moduleSN);
                    if (correspondingModule.Value != null)
                    {
                        string newIPAddress = correspondingModule.Value.Addr ?? "";

                        // 只有当地址发生变化时才更新
                        if (deviceInfo.IPAddress != newIPAddress)
                        {
                            deviceInfo.IPAddress = newIPAddress;
                            deviceInfo.IsConnected = !string.IsNullOrEmpty(newIPAddress);

                            Debug.WriteLine($"🎯 更新设备地址: SN={moduleSN}, IP={newIPAddress}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Write(ex);
                Debug.WriteLine($"🎯 UpdateDeviceAddressAndType异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 🎯 新增：TreeView 展开事件处理 - 异步加载完整通道信息
        /// </summary>
        private async void TreeViewItem_Expanded(object sender, RoutedEventArgs e)
        {
            try
            {
                if (e.OriginalSource is TreeViewItem treeViewItem &&
                    treeViewItem.DataContext is RealTimeDeviceListViewModel.DeviceInfo deviceInfo)
                {
                    // 如果通道已经加载，直接返回
                    if (deviceInfo.ChannelsLoaded) return;

                    Debug.WriteLine($"🎯 开始异步加载设备 {deviceInfo.ModuleSN} 的通道信息");

                    // 异步加载通道信息，避免阻塞UI线程
                    await Task.Run(() =>
                    {
                        // 在后台线程创建通道信息
                        var channels = new List<DeviceChannelInfo>();

                        if (deviceInfo.ModuleItemData?.Items != null)
                        {
                            for (int i = 0; i < deviceInfo.ModuleItemData.Items.Count; i++)
                            {
                                var item = deviceInfo.ModuleItemData.Items[i];
                                var channelInfo = new DeviceChannelInfo
                                {
                                    Name = item?.Names ?? string.Format(ResourceHelper.GetString("RealTimeDeviceList_ChannelPrefix"), i),
                                    IndexNo = i.ToString(),
                                    DataTypeDesc = GetDataTypeDescription((int)(item?.ModbusDBTypeField ?? 0)),
                                    Value = "-",
                                    IsTimeout = true,
                                    Remark = ""
                                };
                                channels.Add(channelInfo);
                            }
                        }

                        // 回到UI线程更新界面
                        Dispatcher.Invoke(() =>
                        {
                            deviceInfo.Channels.Clear();
                            foreach (var channel in channels)
                            {
                                deviceInfo.Channels.Add(channel);
                            }
                            deviceInfo.ChannelsLoaded = true;

                            Debug.WriteLine($"🎯 完成加载设备 {deviceInfo.ModuleSN} 的 {channels.Count} 个通道");
                        });
                    });
                }
            }
            catch (Exception ex)
            {
                Logger.Write(ex);
                Debug.WriteLine($"🎯 TreeViewItem_Expanded异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 🎯 新增：实时更新设备数据值 - 基于moduleList
        /// 只更新数据值，不改变结构，保持用户界面状态
        /// </summary>
        private void UpdateDeviceValues(moduleList moduleList)
        {
            try
            {
                if (moduleList?.Moudels == null) return;

                foreach (var module in moduleList.Moudels)
                {
                    if (string.IsNullOrWhiteSpace(module.Key)) continue;

                    // 查找对应的设备
                    var deviceInfo = _Model.DeviceInfos.FirstOrDefault(d => d.ModuleSN == module.Key);
                    if (deviceInfo == null) continue; // 设备不存在，跳过

                    // 更新设备连接状态和IP地址（设备类型来自 moduleItem，不在这里更新）
                    deviceInfo.IsConnected = true; // moduleList中的设备认为是连接的
                    deviceInfo.IsTimeout = false;  // 有数据更新说明没有超时
                    deviceInfo.IPAddress = module.Value?.Addr ?? ""; // 更新IP地址

                    // 🎯 更新主项的实时数值和超时状态（从 real 中获取）
                    if (module.Value?.Real != null)
                    {
                        // 主项数值：显示 real 中的第一个 values（需要判断是否存在）
                        if (module.Value.Real.Values != null && module.Value.Real.Values.Count > 0)
                        {
                            deviceInfo.RealValue = module.Value.Real.Values[0].ToString("F2");
                        }
                        else
                        {
                            deviceInfo.RealValue = "-";
                        }

                        // 主项超时状态：显示 real 中的 span
                        deviceInfo.RealSpan = module.Value.Real.Span.ToString();
                    }
                    else
                    {
                        deviceInfo.RealValue = "-";
                        deviceInfo.RealSpan = "-";
                    }

                    // 更新通道数据值 - moduleList中的dataInfo包含实时数据
                    if (module.Value?.Real?.Values != null && module.Value.Real.Values.Count > 0)
                    {
                        for (int i = 0; i < module.Value.Real.Values.Count && i < deviceInfo.Channels.Count; i++)
                        {
                            var channel = deviceInfo.Channels[i];
                            if (channel != null)
                            {
                                // 更新通道值和状态
                                channel.Value = module.Value.Real.Values[i].ToString("F2");
                                channel.IsTimeout = false; // 有数据更新说明没有超时
                            }
                        }
                    }
                }

                Debug.WriteLine($"🎯 更新设备数据值完成，处理设备数: {moduleList.Moudels.Count}");
            }
            catch (Exception ex)
            {
                Logger.Write(ex);
                Debug.WriteLine($"🎯 UpdateDeviceValues异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 🎯 新增：更新传感器信息 - 基于SensorInfoList
        /// 更新传感器相关的显示信息
        /// </summary>
        private void UpdateSensorInfo(SysBase.Types.ProBase.Types.SensorInfoList sensorInfoList)
        {
            try
            {
                if (sensorInfoList?.Items == null) return;

                // 这里可以根据需要更新传感器相关的显示信息
                // 例如更新通道名称、单位等
                Debug.WriteLine($"🎯 更新传感器信息完成，传感器数: {sensorInfoList.Items.Count}");
            }
            catch (Exception ex)
            {
                Logger.Write(ex);
                Debug.WriteLine($"🎯 UpdateSensorInfo异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 🎯 新增：获取数据类型描述
        /// </summary>
        private string GetDataTypeDescription(int dataType)
        {
            return dataType switch
            {
                1 => ResourceHelper.GetString("RealTimeDeviceList_DataTypeAnalog"),
                2 => ResourceHelper.GetString("RealTimeDeviceList_DataTypeDigital"),
                3 => ResourceHelper.GetString("RealTimeDeviceList_DataTypePulse"),
                _ => ResourceHelper.GetString("RealTimeDeviceList_DataTypeUnknown")
            };
        }

        private void Init()
        {
            try
            {
                Debug.WriteLine("🎯 开始初始化 RealTimeDeviceListWindow");

                // 🎯 新增：初始化时刷新Global.Messages
                RefreshGlobalMessages();

                // 🎯 使用增量更新方式初始化数据
                InitializeDeviceDataIncrementally();

                if (!_Timer.IsRunning) _Timer.Start();

                Debug.WriteLine("🎯 RealTimeDeviceListWindow 初始化完成");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"🎯 初始化异常: {ex.Message}");
                Logger.Write(ex);
            }
        }

        /// <summary>
        /// 🎯 新增：使用增量更新方式初始化设备数据
        /// </summary>
        private void InitializeDeviceDataIncrementally()
        {
            try
            {
                // 1. 首先基于moduleItemList构建设备结构
                if (_ModuleItemList != null)
                {
                    UpdateDeviceStructure(_ModuleItemList);
                }

                // 2. 然后基于moduleList更新设备数据值
                if (_ModuleList != null)
                {
                    UpdateDeviceValues(_ModuleList);
                }

                // 3. 最后更新传感器信息
                if (_SensorInfoList != null)
                {
                    UpdateSensorInfo(_SensorInfoList);
                }

                Debug.WriteLine($"🎯 增量初始化完成，设备数量: {_Model.DeviceInfos.Count}");
            }
            catch (Exception ex)
            {
                Logger.Write(ex);
                Debug.WriteLine($"🎯 InitializeDeviceDataIncrementally异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 🎯 修改：使用增量更新方式加载设备列表，避免界面闪烁
        /// </summary>
        private async void LoadDeviceList()
        {
            try
            {
                // 🎯 优先尝试从Global.Messages获取数据，使用增量更新
                if (_ModuleList != null || _ModuleItemList != null)
                {
                    InitializeDeviceDataIncrementally();
                    return;
                }

                // 🎯 如果Global中没有数据，回退到原来的service调用方式
                await LoadDeviceListFromService();
            }
            catch (Exception ex)
            {
                this.ShowError(ex);
                Debug.WriteLine($"🎯 LoadDeviceList异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 🎯 新增：从Global.Messages获取设备列表数据
        /// 替代原来的service调用方式，提供更好的实时性
        /// </summary>
        private void LoadDeviceListFromGlobal()
        {
            try
            {
                if (_ModuleList?.Moudels == null || _ModuleItemList?.Moudels == null)
                    return;

                _Model.DeviceInfos.Clear();
                List<RealTimeDeviceListViewModel.DeviceInfo> listDeviceInfos = new();

                foreach (var module in _ModuleList.Moudels)
                {
                    if (string.IsNullOrWhiteSpace(module.Key))
                        continue;

                    var deviceInfo = new RealTimeDeviceListViewModel.DeviceInfo
                    {
                        ModuleSN = module.Key,
                        IPAddress = module.Value?.Addr ?? "",
                        DeviceTypeDesc = module.Value?.Devtype ?? "",
                        IsConnected = true, // 假设在moduleList中的设备都是连接的
                        IsTimeout = false, // 可以根据Real.Lasttime计算超时状态
                        Channels = new ObservableCollection<DeviceChannelInfo>()
                    };

                    // 🎯 检查传感器占用情况
                    if (_SensorInfoList?.Items != null)
                    {
                        var existSensor = _SensorInfoList.Items.FirstOrDefault(s => s.Sn == module.Key);
                        if (existSensor != null)
                        {
                            deviceInfo.IsOccupied = true;
                            deviceInfo.OccupiedBy = string.Format(ResourceHelper.GetString("RealTimeDeviceList_SensorOccupied"), existSensor.Name);
                        }
                    }

                    // 获取对应的模块项信息
                    var moduleItems = _ModuleItemList?.Moudels?.FirstOrDefault(m => m.Key == module.Key);
                    if (moduleItems?.Value == null || moduleItems?.Value.Items == null)
                        continue;

                    foreach (var item in moduleItems?.Value.Items)
                    {
                        if (string.IsNullOrWhiteSpace(item.Names))
                            continue;

                        var channel = new DeviceChannelInfo
                        {
                            Name = item.Names,
                            IndexNo = item.Addr.ToString(), // 使用Addr作为IndexNo
                            DataTypeDesc = item.ModbusDBTypeField.ToString(), // 使用ModbusDBType作为数据类型
                            Value = "0", // 默认值，实际值需要从moduleList的Real.Values获取
                            IsTimeout = false, // 默认不超时
                            Remark = "" // 默认空备注
                        };

                        deviceInfo.Channels.Add(channel);
                    }

                    listDeviceInfos.Add(deviceInfo);
                }

                // 在UI线程中更新数据
                this.Dispatcher.BeginInvoke(() =>
                {
                    // 🎯 关键修改：清空并重新添加数据，确保 ObservableCollection 触发通知
                    _Model.DeviceInfos.Clear();
                    foreach (var deviceInfo in listDeviceInfos)
                    {
                        _Model.DeviceInfos.Add(deviceInfo);
                    }
                    _Model.DeviceCount = _Model.DeviceInfos.Count;

                    // 🎯 强制刷新 TreeView 数据绑定
                    DeviceTreeView.ItemsSource = null;
                    DeviceTreeView.ItemsSource = _Model.DeviceInfos;

                    Debug.WriteLine($"从Global.Messages加载设备列表成功，共{_Model.DeviceInfos.Count}个设备");
                    Debug.WriteLine($"TreeView ItemsSource 设置完成，数据源项目数: {_Model.DeviceInfos?.Count ?? 0}");
                });
            }
            catch (Exception ex)
            {
                this.ShowError(ex);
            }
        }

        /// <summary>
        /// 🎯 新增：从service获取设备列表数据（备用方案）
        /// 当Global.Messages中没有数据时使用
        /// </summary>
        private async Task LoadDeviceListFromService()
        {
            if (!AppInfo.IsServerStarted) return;

            try
            {
                _Model.DeviceInfos.Clear();
                List<RealTimeDeviceListViewModel.DeviceInfo> listDeviceInfos = new();

                // 🎯 简化：暂时返回空列表，主要依赖Global.Messages
                // 如果需要service调用，可以在这里添加具体的service方法调用

                await this.Dispatcher.BeginInvoke(() =>
                {
                    _Model.DeviceInfos = listDeviceInfos;
                    _Model.DeviceCount = listDeviceInfos.Count;
                    Debug.WriteLine($"从Service加载设备列表成功，共{listDeviceInfos.Count}个设备");
                });
            }
            catch (Exception ex)
            {
                this.ShowError(ex);
            }
        }

        private void OnTimerTick(object sender, ElapsedEventArgs e)
        {
            try
            {
                if (!AppInfo.IsServerStarted) return;

                if (_TimeTotalCounter % _SecondCounter == 0)
                {
                    LoadDeviceList();
                }
            }
            catch (Exception ex)
            {
                Logger.Write(ex);
            }
            if (_TimeTotalCounter % TimeTotalMax == 0)
                _TimeTotalCounter = 0;
            _TimeTotalCounter += 1;
        }

        /// <summary>
        /// 🎯 修改：刷新时先更新Global.Messages，然后加载数据
        /// </summary>
        protected void btnRefresh_Click(object sender, EventArgs e)
        {
            try
            {
                RefreshGlobalMessages();
                LoadDeviceList();
            }
            catch (Exception ex)
            {
                this.ShowError(ex);
            }
        }

        protected override void OnClosing(CancelEventArgs e)
        {
            TryStopTimer();
            base.OnClosing(e);
        }

        /// <summary>
        /// 🎯 新增：窗口关闭时清理资源
        /// 取消Global消息事件订阅，防止内存泄漏
        /// </summary>
        protected override void OnClosed(EventArgs e)
        {
            try
            {
                // 取消Global消息事件订阅
                Global.OnMessageReceived -= OnGlobalMessageReceived;
                Debug.WriteLine("RealTimeDeviceListWindow: 已取消Global消息事件订阅");
            }
            catch (Exception ex)
            {
                Logger.Write(ex);
            }
            base.OnClosed(e);
        }

        private void TryStopTimer()
        {
            try
            {
                _Timer.Stop();
            }
            catch { }
        }

        private void DeviceRow_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            try
            {
                // 检测双击
                if (e.ClickCount == 2 && sender is Grid grid && grid.DataContext is RealTimeDeviceListViewModel.DeviceInfo deviceInfo)
                {
                    // 触发设备选择事件，传递设备信息
                    DeviceSelectedEvent?.Invoke(deviceInfo.ModuleSN, deviceInfo.DeviceTypeDesc, "", null);

                    // 🎯 双击主项后关闭窗口
                    this.Close();
                }
            }
            catch (Exception ex)
            {
                this.ShowError(ex);
            }
        }

        private void ChannelRow_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            try
            {
                if (sender is Grid grid && grid.DataContext is DeviceChannelInfo channelInfo)
                {
                    // 从父级获取设备信息
                    var deviceInfo = FindParentDeviceInfo(grid);
                    if (deviceInfo != null)
                    {
                        var deviceItemInfo = new DeviceItemInfo
                        {
                            ModuleSN = deviceInfo.ModuleSN,
                            ItemId = channelInfo.IndexNo,
                            ItemName = channelInfo.Name,
                            ItemDataType = channelInfo.DataTypeDesc,
                            ItemNames = new List<string> { channelInfo.Name }
                        };

                        // 触发设备选择事件，传递通道信息
                        DeviceSelectedEvent?.Invoke(deviceInfo.ModuleSN, deviceInfo.DeviceTypeDesc, channelInfo.IndexNo, deviceItemInfo);
                    }
                }
            }
            catch (Exception ex)
            {
                this.ShowError(ex);
            }
        }

        private RealTimeDeviceListViewModel.DeviceInfo FindParentDeviceInfo(DependencyObject child)
        {
            try
            {
                DependencyObject parent = VisualTreeHelper.GetParent(child);
                while (parent != null)
                {
                    if (parent is FrameworkElement element && element.DataContext is RealTimeDeviceListViewModel.DeviceInfo deviceInfo)
                    {
                        return deviceInfo;
                    }
                    parent = VisualTreeHelper.GetParent(parent);
                }
            }
            catch (Exception ex)
            {
                Logger.Write(ex);
            }
            return null;
        }

        private void ExpandButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is ToggleButton button && button.DataContext is RealTimeDeviceListViewModel.DeviceInfo deviceInfo)
                {
                    deviceInfo.IsExpanded = button.IsChecked ?? false;
                }
            }
            catch (Exception ex)
            {
                this.ShowError(ex);
            }
        }



        /// <summary>
        /// 🎯 新增：选择通道事件
        /// </summary>
        private void SelectChannel_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is DeviceChannelInfo channelInfo)
                {
                    // 触发通道选择事件
                    ChannelSelected?.Invoke(channelInfo);
                    Debug.WriteLine($"选择通道: {channelInfo.Name} - {channelInfo.IndexNo}");
                }
            }
            catch (Exception ex)
            {
                this.ShowError(ex);
            }
        }
    }

    #region 转换器类

    /// <summary>
    /// 布尔值转颜色转换器 - 连接状态
    /// </summary>
    public class BoolToColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isConnected)
            {
                return isConnected ? Colors.Green : Colors.Red;
            }
            return Colors.Gray;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 反向布尔值转颜色转换器 - 超时状态
    /// </summary>
    public class InverseBoolToColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isTimeout)
            {
                return isTimeout ? Colors.Red : Colors.Green;
            }
            return Colors.Gray;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 布尔值转文本转换器
    /// </summary>
    public class BoolToTextConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return boolValue ? ResourceHelper.GetString("RealTimeDeviceList_Yes") : ResourceHelper.GetString("RealTimeDeviceList_No");
            }
            return ResourceHelper.GetString("RealTimeDeviceList_Unknown");
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    #endregion
}

