syntax = "proto3";

package project_struct.alarm;

// 警报规则存储
message AlarmRuleStorage {
  // 阈值级别配置
  message ThresholdAlarmRule {
    // 警报严重程度
    enum AlarmSeverity {
      SEVERITY_CRITICAL = 0;
      SEVERITY_HIGH = 1;
      SEVERITY_MEDIUM = 2;
      SEVERITY_LOW = 3;
    }
    // 比较运算符
    enum ComparisonOperator {
      OPERATOR_GREATER_THAN = 0;
      OPERATOR_GREATER_THAN_OR_EQUAL = 1;
      OPERATOR_LESS_THAN = 2;
      OPERATOR_LESS_THAN_OR_EQUAL = 3;
      OPERATOR_EQUAL = 4;
      OPERATOR_NOT_EQUAL = 5;
    }
    AlarmSeverity severity = 1;
    ComparisonOperator operator = 2;
    double threshold = 3;
    double deadband = 4;  // 每个级别有自己的死区值
    string message = 5;
  }
  // 趋势型警报规则
  message TrendAlarmRule {
    enum TrendDirection {
      TREND_RISING = 0;
      TREND_FALLING = 1;
      TREND_BOTH = 2;  // 监控上升和下降
    }
    double rate_of_change = 1;
    int32 time_window_seconds = 2; //时间窗口秒
    TrendDirection direction = 3;
    string message = 4;
  }
  // 模式型警报规则
  message PatternAlarmRule {
    repeated double pattern = 1;
    double tolerance = 2;
    int32 time_window_seconds = 3;
    string message = 4;
  }
  //传感器信息
  message sensorsource {
    string sn=1;
    int32 id=2;
    string name=3;
  }
  // 关联型警报规则
  message CorrelationAlarmRule {
    repeated sensorsource parameter_names = 21;
    string expression = 2;
    int32 time_window_seconds = 3;
  }
  // 传感器警报配置
  message SensorAlarmConfig {
    sensorsource sensor = 1;
    repeated ThresholdAlarmRule threshold_rule = 2;
    repeated TrendAlarmRule trend_rule = 3;
    repeated PatternAlarmRule pattern_rule = 4;
    repeated CorrelationAlarmRule correlation_rule = 5;
  }
  // 传感器警报配置列表
  repeated SensorAlarmConfig rules = 1;
}