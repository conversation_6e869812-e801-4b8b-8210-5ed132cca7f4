using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using Commiunication;
using Google.Protobuf;
using ProjectStruct;
using SDHD.DC.DataMonitor.UserControls.Chart;
using SDHD.DC.DataMonitor.Utils;
using SDHD.DC.DataMonitor.Utils.Drawing;
using SDHD.DC.Utilities.Logging;
using static ProjectStruct.SysBase.Types;
using static ProjectStruct.SysBase.Types.ProBase.Types;

namespace SDHD.DC.DataMonitor.Views.MonitorView
{
    /// <summary>
    /// MonitorWindowView中处理SensorInfoList消息更新的扩展方法
    /// 当传感器设置被修改时，自动更新曲线控件中所有series的名称和单位
    /// </summary>
    public partial class MonitorWindowView
    {
        /// <summary>
        /// 处理Global.OnSetMessage事件的扩展方法
        /// 当收到SensorInfoList消息时，更新曲线控件中的传感器名称和单位
        /// 注意：这个方法需要在现有的OnSetMessage方法中调用
        /// </summary>
        /// <param name="msg">消息键名</param>
        public void HandleSensorInfoListUpdate(string msg)
        {
            try
            {
                // 只检查SensorInfoList消息，单位直接从sensorinfo.unitstr获取
                if (msg == "SensorInfoList")
                {
                    // 在UI线程中更新曲线控件
                    this.Dispatcher.BeginInvoke(() =>
                    {
                        UpdateCurveChartSensorNamesAndUnits();
                    });
                }
            }
            catch (Exception ex)
            {
                Logger.Write(ex);
            }
        }

        /// <summary>
        /// 更新曲线控件中所有series的传感器名称和单位
        /// 从Global.Messages中获取最新的SensorInfoList，单位直接从sensorinfo.unitstr获取
        /// 直接更新现有的CurveLineInfo对象，不重新初始化整个控件
        /// </summary>
        private void UpdateCurveChartSensorNamesAndUnits()
        {
            try
            {
                // 获取最新的SensorInfoList
                var sensorInfoList = Global.Messages.TryGetValue("SensorInfoList", out var sensorMsg) ? sensorMsg as SensorInfoList : null;

                if (sensorInfoList == null)
                {
                    Logger.Write("UpdateCurveChartSensorNamesAndUnits: SensorInfoList is null");
                    return;
                }

                // 直接更新现有的CurveLineInfo对象
                bool updated = UpdateCurveLineInfoUnits(sensorInfoList);

                if (updated)
                {
                    Logger.Write("Successfully updated curve line info units directly");
                }
                else
                {
                    Logger.Write("Could not update curve line info units, trying fallback method");
                    // 如果直接更新失败，尝试重新初始化
                    RefreshMonitorViewWithUpdatedSensorInfo();
                }
            }
            catch (Exception ex)
            {
                Logger.Write($"Error in UpdateCurveChartSensorNamesAndUnits: {ex.Message}");
                Logger.Write(ex);
            }
        }

        /// <summary>
        /// 直接更新现有CurveLineInfo对象中的传感器名称和单位信息
        /// 这样可以避免重新初始化整个曲线控件，保持现有的数据和状态
        /// </summary>
        /// <param name="sensorInfoList">最新的传感器信息列表</param>
        /// <returns>是否成功更新</returns>
        private bool UpdateCurveLineInfoUnits(SensorInfoList sensorInfoList)
        {
            try
            {
                // 获取曲线控件的引用
                var curveChart = this.FindName("CurveChart") as RealTimeCurveChart;
                if (curveChart == null)
                {
                    Logger.Write("UpdateCurveLineInfoUnits: CurveChart control not found");
                    return false;
                }

                // 通过反射获取MonitorWindowChart对象
                var chartField = curveChart.GetType().GetField("_chart",
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                if (chartField?.GetValue(curveChart) is not SDHD.DC.DataMonitor.Utils.MonitorWindowChart chart)
                {
                    Logger.Write("UpdateCurveLineInfoUnits: MonitorWindowChart not found");
                    return false;
                }

                int updatedCount = 0;

                // 更新每条曲线的单位信息
                foreach (var curveLine in chart.CurveLines)
                {
                    if (int.TryParse(curveLine.SensorId, out int sensorId) &&
                        sensorId >= 0 && sensorId < sensorInfoList.Items.Count)
                    {
                        var sensorInfo = sensorInfoList.Items[sensorId];

                        // 检查是否需要更新
                        bool needUpdate = false;

                        if (curveLine.SensorFullName != sensorInfo.Name)
                        {
                            curveLine.SensorFullName = sensorInfo.Name;
                            needUpdate = true;
                        }

                        string newUnit = sensorInfo.Unitstr ?? "";
                        if (curveLine.DataUnit != newUnit)
                        {
                            curveLine.DataUnit = newUnit;
                            needUpdate = true;
                        }

                        if (needUpdate)
                        {
                            updatedCount++;
                            Logger.Write($"Updated curve line: Sensor {sensorId}, Name: '{sensorInfo.Name}', Unit: '{newUnit}'");
                        }
                    }
                }

                // 🎯 新增：同时更新RealTimeCurveChart中的Seriespropert.Unit
                bool seriesUpdated = UpdateRealTimeCurveChartSeriesUnits(sensorInfoList);

                // 同时更新RealTimeCurveChartSensorInfo中的Unit字段
                bool sensorInfoUpdated = UpdateRealTimeCurveChartSensorInfoUnits(sensorInfoList);

                Logger.Write($"UpdateCurveLineInfoUnits: Updated {updatedCount} curve lines, Series updated: {seriesUpdated}, SensorInfo updated: {sensorInfoUpdated}");
                return updatedCount > 0 || sensorInfoUpdated || seriesUpdated;
            }
            catch (Exception ex)
            {
                Logger.Write($"Error in UpdateCurveLineInfoUnits: {ex.Message}");
                Logger.Write(ex);
                return false;
            }
        }

        /// <summary>
        /// 更新RealTimeCurveChart中Seriespropert的Unit字段
        /// 确保Seriespropert.Unit与最新的传感器单位信息同步
        /// </summary>
        /// <param name="sensorInfoList">最新的传感器信息列表</param>
        /// <returns>是否成功更新</returns>
        private bool UpdateRealTimeCurveChartSeriesUnits(SensorInfoList sensorInfoList)
        {
            try
            {
                // 获取曲线控件的引用
                var curveChart = this.FindName("CurveChart") as RealTimeCurveChart;
                if (curveChart == null)
                {
                    Logger.Write("UpdateRealTimeCurveChartSeriesUnits: CurveChart control not found");
                    return false;
                }

                int updatedCount = 0;

                // 通过反射获取YAxisproperts属性
                var yAxisPropertsField = curveChart.GetType().GetField("YAxisproperts", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                
                if (yAxisPropertsField?.GetValue(curveChart) is not System.Collections.Generic.List<object> yAxisProperts)
                {
                    Logger.Write("UpdateRealTimeCurveChartSeriesUnits: YAxisproperts not found");
                    return false;
                }

                // 遍历所有Y轴和传感器，更新Unit字段
                foreach (var yAxis in yAxisProperts)
                {
                    // 通过反射获取seriesproperts属性
                    var seriesPropertsField = yAxis.GetType().GetField("seriesproperts", 
                        System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance);
                    
                    if (seriesPropertsField?.GetValue(yAxis) is System.Collections.Generic.List<object> seriesProperts)
                    {
                        foreach (var series in seriesProperts)
                        {
                            // 通过反射获取SenId属性
                            var senIdField = series.GetType().GetField("SenId", 
                                System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance);
                            
                            if (senIdField?.GetValue(series) is int senId && senId >= 0 && senId < sensorInfoList.Items.Count)
                            {
                                var latestSensorInfo = sensorInfoList.Items[senId];
                                string newUnit = latestSensorInfo.Unitstr ?? "";
                                string newName = latestSensorInfo.Name ?? "";

                                // 通过反射更新Unit字段
                                var unitField = series.GetType().GetField("Unit", 
                                    System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance);
                                if (unitField != null)
                                {
                                    var currentUnit = unitField.GetValue(series) as string ?? "";
                                    if (currentUnit != newUnit)
                                    {
                                        unitField.SetValue(series, newUnit);
                                        updatedCount++;
                                        Logger.Write($"Updated Seriespropert: Sensor {senId}, Unit: '{newUnit}'");
                                    }
                                }

                                // 通过反射更新Name字段
                                var nameField = series.GetType().GetField("Name", 
                                    System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance);
                                if (nameField != null)
                                {
                                    var currentName = nameField.GetValue(series) as string ?? "";
                                    if (currentName != newName)
                                    {
                                        nameField.SetValue(series, newName);
                                        Logger.Write($"Updated Seriespropert: Sensor {senId}, Name: '{newName}'");
                                    }
                                }
                            }
                        }
                    }
                }

                Logger.Write($"UpdateRealTimeCurveChartSeriesUnits: Updated {updatedCount} series units");
                return updatedCount > 0;
            }
            catch (Exception ex)
            {
                Logger.Write($"Error in UpdateRealTimeCurveChartSeriesUnits: {ex.Message}");
                Logger.Write(ex);
                return false;
            }
        }

        /// <summary>
        /// 更新RealTimeCurveChartSensorInfo中的Unit字段
        /// 确保SeriesProperty中的Unit字段与最新的传感器单位信息同步
        /// 注意：由于RealTimeCurveChart控件可能没有GetAllProperty/SetAllProperty方法，
        /// 这个方法主要用于演示如何更新SeriesProperty.Unit字段的概念
        /// </summary>
        /// <param name="sensorInfoList">最新的传感器信息列表</param>
        /// <returns>是否成功更新</returns>
        private bool UpdateRealTimeCurveChartSensorInfoUnits(SensorInfoList sensorInfoList)
        {
            try
            {
                // 获取曲线控件的引用
                var curveChart = this.FindName("CurveChart") as RealTimeCurveChart;
                if (curveChart == null)
                {
                    Logger.Write("UpdateRealTimeCurveChartSensorInfoUnits: CurveChart control not found");
                    return false;
                }

                // 检查是否有GetAllProperty方法（新版本的RealTimeCurveChart可能有）
                var getAllPropertyMethod = curveChart.GetType().GetMethod("GetAllProperty");
                if (getAllPropertyMethod == null)
                {
                    Logger.Write("UpdateRealTimeCurveChartSensorInfoUnits: GetAllProperty method not found, using fallback approach");
                    return UpdateSensorInfoUnitsFallback(sensorInfoList);
                }

                // 尝试调用GetAllProperty方法
                var currentProperties = getAllPropertyMethod.Invoke(curveChart, null);
                if (currentProperties == null)
                {
                    Logger.Write("UpdateRealTimeCurveChartSensorInfoUnits: GetAllProperty returned null");
                    return false;
                }

                // 通过反射获取YAxes属性
                var yAxesProperty = currentProperties.GetType().GetProperty("YAxes");
                if (yAxesProperty == null)
                {
                    Logger.Write("UpdateRealTimeCurveChartSensorInfoUnits: YAxes property not found");
                    return false;
                }

                var yAxes = yAxesProperty.GetValue(currentProperties) as System.Collections.IEnumerable;
                if (yAxes == null)
                {
                    Logger.Write("UpdateRealTimeCurveChartSensorInfoUnits: No YAxes found in current properties");
                    return false;
                }

                bool hasUpdates = false;
                int updatedSensorCount = 0;

                // 遍历所有Y轴和传感器，更新Unit字段
                foreach (var yAxis in yAxes)
                {
                    var sensorsProperty = yAxis.GetType().GetProperty("Sensors");
                    if (sensorsProperty == null) continue;

                    var sensors = sensorsProperty.GetValue(yAxis) as System.Collections.IEnumerable;
                    if (sensors == null) continue;

                    foreach (var sensorInfo in sensors)
                    {
                        // 获取SensorId属性
                        var sensorIdProperty = sensorInfo.GetType().GetProperty("SensorId");
                        if (sensorIdProperty == null) continue;

                        var sensorId = (int)sensorIdProperty.GetValue(sensorInfo);

                        // 根据SensorId查找对应的传感器信息
                        if (sensorId >= 0 && sensorId < sensorInfoList.Items.Count)
                        {
                            var latestSensorInfo = sensorInfoList.Items[sensorId];
                            string newUnit = latestSensorInfo.Unitstr ?? "";
                            string newName = latestSensorInfo.Name ?? "";

                            // 更新Unit字段（如果存在）
                            var unitProperty = sensorInfo.GetType().GetProperty("Unit");
                            if (unitProperty != null)
                            {
                                var currentUnit = unitProperty.GetValue(sensorInfo) as string ?? "";
                                if (currentUnit != newUnit)
                                {
                                    unitProperty.SetValue(sensorInfo, newUnit);
                                    hasUpdates = true;
                                    updatedSensorCount++;
                                    Logger.Write($"Updated RealTimeCurveChartSensorInfo: Sensor {sensorId}, Unit: '{newUnit}'");
                                }
                            }

                            // 同时更新名称
                            var nameProperty = sensorInfo.GetType().GetProperty("Name");
                            if (nameProperty != null)
                            {
                                var currentName = nameProperty.GetValue(sensorInfo) as string ?? "";
                                if (currentName != newName)
                                {
                                    nameProperty.SetValue(sensorInfo, newName);
                                    hasUpdates = true;
                                    Logger.Write($"Updated RealTimeCurveChartSensorInfo: Sensor {sensorId}, Name: '{newName}'");
                                }
                            }
                        }
                    }
                }

                // 如果有更新，尝试重新应用属性配置
                if (hasUpdates)
                {
                    var setAllPropertyMethod = curveChart.GetType().GetMethod("SetAllProperty");
                    if (setAllPropertyMethod != null)
                    {
                        var success = (bool)setAllPropertyMethod.Invoke(curveChart, new[] { currentProperties });
                        Logger.Write($"UpdateRealTimeCurveChartSensorInfoUnits: Updated {updatedSensorCount} sensors, SetAllProperty result: {success}");
                        return success;
                    }
                    else
                    {
                        Logger.Write($"UpdateRealTimeCurveChartSensorInfoUnits: Updated {updatedSensorCount} sensors, but SetAllProperty method not found");
                        return true; // 数据已更新，即使无法重新应用
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                Logger.Write($"Error in UpdateRealTimeCurveChartSensorInfoUnits: {ex.Message}");
                Logger.Write(ex);
                return false;
            }
        }

        /// <summary>
        /// 备用方法：当RealTimeCurveChart没有GetAllProperty/SetAllProperty方法时使用
        /// 这种情况下，我们只能记录需要更新的信息，实际的更新需要在其他地方处理
        /// </summary>
        /// <param name="sensorInfoList">最新的传感器信息列表</param>
        /// <returns>是否成功处理</returns>
        private bool UpdateSensorInfoUnitsFallback(SensorInfoList sensorInfoList)
        {
            try
            {
                Logger.Write("Using fallback approach for updating sensor units");

                // 在这里，您可以：
                // 1. 记录需要更新的传感器信息
                // 2. 触发重新初始化曲线控件
                // 3. 或者使用其他方式更新传感器信息

                // 示例：记录所有传感器的最新单位信息
                for (int i = 0; i < sensorInfoList.Items.Count; i++)
                {
                    var sensorInfo = sensorInfoList.Items[i];
                    Logger.Write($"Sensor {i}: Name='{sensorInfo.Name}', Unit='{sensorInfo.Unitstr}'");
                }

                // 这里可以触发重新初始化，确保使用最新的传感器信息
                RefreshMonitorViewWithUpdatedSensorInfo();

                return true;
            }
            catch (Exception ex)
            {
                Logger.Write($"Error in UpdateSensorInfoUnitsFallback: {ex.Message}");
                Logger.Write(ex);
                return false;
            }
        }

        /// <summary>
        /// 触发MonitorWindowView的刷新以更新传感器信息
        /// 这将重新初始化曲线控件，使用最新的传感器名称和单位
        /// </summary>
        private void RefreshMonitorViewWithUpdatedSensorInfo()
        {
            try
            {
                // 使用反射调用MonitorWindowView的RefreshView方法
                // 这样可以重新初始化曲线控件，使用最新的传感器信息
                var refreshViewMethod = this.GetType().GetMethod("RefreshView",
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                if (refreshViewMethod != null)
                {
                    refreshViewMethod.Invoke(this, null);
                    Logger.Write("Successfully called RefreshView to update sensor names and units");
                }
                else
                {
                    // 如果RefreshView方法不存在，尝试调用其他可能的刷新方法
                    var initMethod = this.GetType().GetMethod("InitMonitorViewInfo",
                        System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                    if (initMethod != null)
                    {
                        initMethod.Invoke(this, null);
                        Logger.Write("Successfully called InitMonitorViewInfo to update sensor names and units");
                    }
                    else
                    {
                        Logger.Write("No suitable refresh method found in MonitorWindowView");
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Write($"Error in RefreshMonitorViewWithUpdatedSensorInfo: {ex.Message}");
                Logger.Write(ex);
            }
        }

        /// <summary>
        /// 获取传感器的显示名称
        /// 支持传统传感器和模块设备传感器
        /// </summary>
        /// <param name="sensorId">传感器ID</param>
        /// <param name="sn">设备序列号（可选）</param>
        /// <returns>传感器显示名称</returns>
        private string GetSensorDisplayName(int sensorId, string sn = null)
        {
            try
            {
                var sensorInfoList = Global.Messages.TryGetValue("SensorInfoList", out var sensorMsg) ? sensorMsg as SensorInfoList : null;

                if (sensorInfoList == null) return string.Empty;

                // 传统传感器（sn为空）
                if (string.IsNullOrEmpty(sn))
                {
                    if (sensorId >= 0 && sensorId < sensorInfoList.Items.Count)
                    {
                        return sensorInfoList.Items[sensorId].Name;
                    }
                }
                else
                {
                    // 模块设备传感器（sn不为空）
                    // 模块设备传感器功能暂时注释，等待确认数据结构
                    /*
                    var moduleItemList = Global.Messages.TryGetValue("ModuleItemList", out var moduleMsg) ? moduleMsg as SysBase.Types.deviceBase.Types.moduleItemList : null;
                    if (moduleItemList != null)
                    {
                        var module = moduleItemList.Moudels.FirstOrDefault(m => m.Value.Sn == sn);
                        if (module.Value != null && sensorId >= 0 && sensorId < module.Value.Items.Count)
                        {
                            return module.Value.Items[sensorId].Names;
                        }
                    }
                    */
                }

                return string.Empty;
            }
            catch (Exception ex)
            {
                Logger.Write($"Error in GetSensorDisplayName: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 获取传感器的单位名称
        /// 直接从sensorinfo.unitstr获取，不再需要查询UnitInfoList
        /// </summary>
        /// <param name="sensorInfo">传感器信息</param>
        /// <returns>单位名称</returns>
        private string GetSensorUnitString(SensorInfo sensorInfo)
        {
            try
            {
                return sensorInfo?.Unitstr ?? string.Empty;
            }
            catch (Exception ex)
            {
                Logger.Write($"Error in GetSensorUnitString: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 窗口关闭时取消事件订阅
        /// 防止内存泄漏
        /// </summary>
        protected override void OnClosed(EventArgs e)
        {
            try
            {
                // 取消事件订阅
                Commiunication.Global.OnSetMessage -= OnSetMessage;
                Commiunication.Global.OnMessageReceived -= OnReceivedMessage;

                Logger.Write("MonitorWindowView: Unsubscribed from Global events");
            }
            catch (Exception ex)
            {
                Logger.Write($"Error in OnClosed: {ex.Message}");
            }
            finally
            {
                base.OnClosed(e);
            }
        }

        /// <summary>
        /// 手动触发传感器名称和单位更新
        /// 可以在需要时主动调用此方法
        /// </summary>
        public void RefreshSensorNamesAndUnits()
        {
            try
            {
                this.Dispatcher.BeginInvoke(() =>
                {
                    UpdateCurveChartSensorNamesAndUnits();
                });
            }
            catch (Exception ex)
            {
                Logger.Write($"Error in RefreshSensorNamesAndUnits: {ex.Message}");
            }
        }
    }
}
