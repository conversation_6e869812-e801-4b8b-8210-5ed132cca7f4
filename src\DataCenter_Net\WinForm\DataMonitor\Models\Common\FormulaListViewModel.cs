﻿using SDHD.DC.Utilities;
using SDHD.DC.Utilities.WPF.Extensions;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Media;

namespace SDHD.DC.DataMonitor.Models.Common
{
    public class FormulaListViewModel : NotifyPropertyChangedModel
    {
        public List<FormulaInfo> Formulas { get; set; } = new List<FormulaInfo>();
        public class FormulaInfo
        {
            public string FormulaId { set; get; }
            public string Name { set; get; }
            public string Definition { set; get; }
            public string Description { set; get; }
            public string Example { set; get; }
        }
    }
}
