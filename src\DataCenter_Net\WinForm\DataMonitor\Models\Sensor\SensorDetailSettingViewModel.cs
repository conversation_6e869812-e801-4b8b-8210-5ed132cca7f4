﻿using SDHD.DC.DataCollection.ApplicationCore.Domain.Entities;
using SDHD.DC.DataMonitor.Utils;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Media;

namespace SDHD.DC.DataMonitor.Models.Sensor
{
    public class SensorDetailSettingViewModel : NotifyPropertyChangedModel
    {
        public DeviceItemInfo DeviceItemInfo = new();
        public string WindowTitle { set; get; }
        public string WellStationId { set; get; }
        public string SensorId { set; get; }
        public int CurrentStepNo { set; get; }

        private string _SensorTypeTitle;
        public string SensorTypeTitle
        {
            get { return _SensorTypeTitle; }
            set
            {
                if (_SensorTypeTitle == value) return;
                _SensorTypeTitle = value;
                OnPropertyChanged(nameof(SensorTypeTitle));
            }
        }
        private string _SensorNameTitle;
        public string SensorNameTitle
        {
            get { return _SensorNameTitle; }
            set
            {
                if (_SensorNameTitle == value) return;
                _SensorNameTitle = value;
                OnPropertyChanged(nameof(SensorNameTitle));
            }
        }
        public string SensorNameGroup { set; get; }

        private string _SensorName;
        public string SensorName
        {
            get { return _SensorName; }
            set
            {
                if (_SensorName == value) return;
                _SensorName = value;
                OnPropertyChanged(nameof(SensorName));
            }
        }

        private string _SensorModuleType;
        public string SensorModuleType
        {
            get { return _SensorModuleType; }
            set
            {
                if (_SensorModuleType == value) return;
                _SensorModuleType = value;
                OnPropertyChanged(nameof(SensorModuleType));
            }
        }

        private string _SensorCategory;
        public string SensorCategory
        {
            get { return _SensorCategory; }
            set
            {
                if (_SensorCategory == value) return;
                _SensorCategory = value;
                OnPropertyChanged(nameof(SensorCategory));
            }
        }
        private bool _IsAccumulatedYiYe;
        public bool IsAccumulatedYiYe
        {
            get { return _IsAccumulatedYiYe; }
            set
            {
                if (_IsAccumulatedYiYe == value) return;
                _IsAccumulatedYiYe = value;
                OnPropertyChanged(nameof(IsAccumulatedYiYe));
            }
        }

        private string _AccumulationTimeUnit;
        public string AccumulationTimeUnit
        {
            get { return _AccumulationTimeUnit; }
            set
            {
                _AccumulationTimeUnit = value;
                OnPropertyChanged(nameof(_AccumulationTimeUnit));
            }
        }

        private string _MeasureUnitId;
        public string MeasureUnitId
        {
            get { return _MeasureUnitId; }
            set
            {
                if (_MeasureUnitId == value) return;
                _MeasureUnitId = value;
                OnPropertyChanged(nameof(MeasureUnitId));
            }
        }

        private string _MeasureUnitStr;
        public string MeasureUnitStr
        {
            get { return _MeasureUnitStr; }
            set
            {
                if (_MeasureUnitStr == value) return;
                _MeasureUnitStr = value;
                OnPropertyChanged(nameof(MeasureUnitStr));
            }
        }

        private bool _IsModuleSNEnabled = true;
        public bool IsModuleSNEnabled
        {
            get { return _IsModuleSNEnabled; }
            set
            {
                if (_IsModuleSNEnabled == value) return;
                _IsModuleSNEnabled = value;
                OnPropertyChanged(nameof(IsModuleSNEnabled));
            }
        }
        private bool _IsRangeEnabled = true;
        public bool IsRangeEnabled
        {
            get { return _IsRangeEnabled; }
            set
            {
                if (_IsRangeEnabled == value) return;
                _IsRangeEnabled = value;
                OnPropertyChanged(nameof(IsRangeEnabled));
            }
        }
        private string _MinRange;
        public string MinRange
        {
            get { return _MinRange; }
            set
            {
                if (_MinRange == value) return;
                _MinRange = value;
                OnPropertyChanged(nameof(MinRange));
            }
        }
        private string _MaxRange;
        public string MaxRange
        {
            get { return _MaxRange; }
            set
            {
                if (_MaxRange == value) return;
                _MaxRange = value;
                OnPropertyChanged(nameof(MaxRange));
            }
        }
        private string _ADCMinRange;
        public string ADCMinRange
        {
            get { return _ADCMinRange; }
            set
            {
                if (_ADCMinRange == value) return;
                _ADCMinRange = value;
                OnPropertyChanged(nameof(ADCMinRange));
            }
        }

        private string _ADCMaxRange;
        public string ADCMaxRange
        {
            get { return _ADCMaxRange; }
            set
            {
                if (_ADCMaxRange == value) return;
                _ADCMaxRange = value;
                OnPropertyChanged(nameof(ADCMaxRange));
            }
        }

        private string _UserGroup;
        public string UserGroup
        {
            get { return _UserGroup; }
            set
            {
                if (_UserGroup == value) return;
                _UserGroup = value;
                OnPropertyChanged(nameof(UserGroup));
                OnPropertyChanged(nameof(UserGroups));
            }
        }


        private bool _IsEnabledValidDataValue;
        public bool IsEnabledValidDataValue
        {
            get { return _IsEnabledValidDataValue; }
            set
            {
                _IsEnabledValidDataValue = value;
                if (!_IsEnabledValidDataValue)
                {
                    ValidDataValue = string.Empty;
                }
                OnPropertyChanged(nameof(ValidDataValue));
                OnPropertyChanged(nameof(IsDisabledValidDataValue));
            }
        }
        public bool IsDisabledValidDataValue
        {
            get { return !_IsEnabledValidDataValue; }
        }
        private string _ValidDataValue;
        public string ValidDataValue
        {
            get { return _ValidDataValue; }
            set
            {
                if (value == _ValidDataValue) return;
                _ValidDataValue = value;
                OnPropertyChanged(nameof(ValidDataValue));
            }
        }
        public bool IsEnabled { set; get; }
        public bool IsHidden { set; get; }
        public bool IsStorageProductionMiddlePara { set; get; }

        private bool _IsEnableMaxWarning;
        public bool IsEnableMaxWarning
        {
            get { return _IsEnableMaxWarning; }
            set
            {
                if (_IsEnableMaxWarning == value) return;
                _IsEnableMaxWarning = value;
                if (!_IsEnableMaxWarning)
                {
                    MaxExceedWarning = null;
                    MaxWarningType = null;
                }
                OnPropertyChanged(nameof(IsEnableMaxWarning));
                OnPropertyChanged(nameof(IsDisabledMaxWarning));
            }
        }
        public bool IsDisabledMaxWarning
        {
            get { return !_IsEnableMaxWarning; }
        }
        private string _MaxExceedWarning;
        public string MaxExceedWarning
        {
            get { return _MaxExceedWarning; }
            set
            {
                if (_MaxExceedWarning == value) return;
                _MaxExceedWarning = value;
                OnPropertyChanged(nameof(MaxExceedWarning));
            }
        }
        private string _MaxWarningType;
        public string MaxWarningType
        {
            get { return _MaxWarningType; }
            set
            {
                if (_MaxWarningType == value) return;
                _MaxWarningType = value;
                OnPropertyChanged(nameof(MaxWarningType));
            }
        }

        private bool _IsEnableMinWarning;
        public bool IsEnableMinWarning
        {
            get { return _IsEnableMinWarning; }
            set
            {
                if (_IsEnableMinWarning == value) return;
                _IsEnableMinWarning = value;
                if (!_IsEnableMinWarning)
                {
                    MinExceedWarning = null;
                    MinWarningType = null;
                }
                OnPropertyChanged(nameof(IsEnableMinWarning));
                OnPropertyChanged(nameof(IsDisabledMinWarning));
            }
        }
        public bool IsDisabledMinWarning
        {
            get { return !_IsEnableMinWarning; }
        }
        private string _MinExceedWarning;
        public string MinExceedWarning
        {
            get { return _MinExceedWarning; }
            set
            {
                if (_MinExceedWarning == value) return;
                _MinExceedWarning = value;
                OnPropertyChanged(nameof(MinExceedWarning));
            }
        }
        private string _MinWarningType;
        public string MinWarningType
        {
            get { return _MinWarningType; }
            set
            {
                if (_MinWarningType == value) return;
                _MinWarningType = value;
                OnPropertyChanged(nameof(MinWarningType));
            }
        }

        private bool _IsEnableErrorWarning;
        public bool IsEnableErrorWarning
        {
            get { return _IsEnableErrorWarning; }
            set
            {
                if (_IsEnableErrorWarning == value) return;
                _IsEnableErrorWarning = value;
                if (!_IsEnableErrorWarning)
                {
                    ErrorWarningType = null;
                }
                OnPropertyChanged(nameof(IsEnableErrorWarning));
                OnPropertyChanged(nameof(IsDisabledErrorWarning));
            }
        }
        public bool IsDisabledErrorWarning
        {
            get { return !_IsEnableErrorWarning; }
        }
        private string _ErrorWarningType;
        public string ErrorWarningType
        {
            get { return _ErrorWarningType; }
            set
            {
                if (_ErrorWarningType == value) return;
                _ErrorWarningType = value;
                OnPropertyChanged(nameof(ErrorWarningType));
            }
        }

        private bool _IsEnableLightWarning;
        public bool IsEnableLightWarning
        {
            get { return _IsEnableLightWarning; }
            set
            {
                _IsEnableLightWarning = value;
                if (!_IsEnableLightWarning)
                {
                    LightWarningType = null;
                }
                OnPropertyChanged(nameof(IsEnableLightWarning));
                OnPropertyChanged(nameof(IsDisabledLightWarning));
            }
        }
        public bool IsDisabledLightWarning
        {
            get { return !_IsEnableLightWarning; }
        }
        private string _LightWarningType;
        public string LightWarningType
        {
            get { return _LightWarningType; }
            set
            {
                if (value == _LightWarningType) return;
                _LightWarningType = value;
                OnPropertyChanged(nameof(LightWarningType));
            }
        }

        private string _Icon;
        public string Icon
        {
            get { return _Icon; }
            set
            {
                if (value == _Icon) return;
                _Icon = value;
                OnPropertyChanged(nameof(Icon));
            }
        }
        private string _DecimalPrecision;
        public string DecimalPrecision
        {
            get { return _DecimalPrecision; }
            set
            {
                if (value == _DecimalPrecision) return;
                _DecimalPrecision = value;
                OnPropertyChanged(nameof(DecimalPrecision));
            }
        }

        private string _LineColor = "#000000";
        public string LineColor
        {
            get { return _LineColor; }
            set
            {
                if (_LineColor == value) return;
                _LineColor = value;
                OnPropertyChanged(nameof(LineColor));
                OnPropertyChanged(nameof(LineColorBrush));
            }
        }
        public SolidColorBrush LineColorBrush
        {
            get
            {
                var color = ColorExtensionsWPF.ConvertFromHex(LineColor, true);
                return new SolidColorBrush(color);
            }
        }

        private string _DevIndexID { set; get; }
        public string DevIndexID
        {
            get { return _DevIndexID; }
            set
            {
                if (_DevIndexID == value) return;
                _DevIndexID = value;
                OnPropertyChanged(nameof(DevIndexID));
            }
        }
        private string _ModuleSN { set; get; }
        public string ModuleSN
        {
            get { return _ModuleSN; }
            set
            {
                if (_ModuleSN == value) return;
                _ModuleSN = value; OnPropertyChanged(nameof(ModuleSN));
            }
        }
        public bool IsReadRealTimeDataVisible
        {
            get { return !string.IsNullOrEmpty(WellStationId); }
        }
        private bool _IsEnableReadRealTime;
        public bool IsEnableReadRealTime
        {
            get { return _IsEnableReadRealTime; }
            set
            {
                if (value == _IsEnableReadRealTime) return;
                _IsEnableReadRealTime = true;
                OnPropertyChanged(nameof(IsEnableReadRealTime));
            }
        }
        public CorrectItem CorrectItem1 { set; get; } = new CorrectItem();
        public CorrectItem CorrectItem2 { set; get; } = new CorrectItem();
        public CorrectItem CorrectItem3 { set; get; } = new CorrectItem();
        public CorrectItem CorrectItem4 { set; get; } = new CorrectItem();
        public CorrectItem CorrectItem5 { set; get; } = new CorrectItem();
        public CorrectItem CorrectItem6 { set; get; } = new CorrectItem();
        public CorrectItem CorrectItem7 { set; get; } = new CorrectItem();

        private string _CalFormula;
        public string CalFormula
        {
            get { return _CalFormula; }
            set
            {
                if (_CalFormula == value) return;
                _CalFormula = value;
                OnPropertyChanged(nameof(CalFormula));
            }
        }

        private string _CalFormulaVerifyResult;
        public string CalFormulaVerifyResult
        {
            get { return _CalFormulaVerifyResult; }
            set
            {
                if (_CalFormulaVerifyResult == value) return;
                _CalFormulaVerifyResult = value;
                OnPropertyChanged(nameof(CalFormulaVerifyResult));
            }
        }

        public List<SelectItem> SensorNameGroups { set; get; } = new List<SelectItem>();
        public List<SelectItem> SensorNames { set; get; } = new List<SelectItem>();
        public List<SelectItem> SensorModuleTypes { set; get; } = new List<SelectItem>();
        public List<SelectItem> SensorCategories { set; get; } = new ();
        public List<SelectItem> AccumulationTimeUnits { set; get; } = new List<SelectItem>();

        private List<SelectItem> _MeasureUnits = new();
        public List<SelectItem> MeasureUnits
        {
            get { return _MeasureUnits; }
            set
            {
                _MeasureUnits = value;
                OnPropertyChanged(nameof(MeasureUnits));
            }
        }
        public List<SelectItem> UserGroups { set; get; } = new List<SelectItem>();
        public List<SelectItem> WarningTypes { set; get; } = new List<SelectItem>();
        public List<SelectItem> LightWarningTypes { set; get; } = new List<SelectItem>();
        public List<SelectItem> DecimalPrecisions { set; get; } = new List<SelectItem>();
        public List<SelectItem> Icons { set; get; } = new List<SelectItem>();
        public List<SelectItem> NormalFormulas { set; get; } = new List<SelectItem>();

        //采样时间
        private string _CollectInterval = "0";
        public string CollectInterval
        {
            get { return _CollectInterval; }
            set
            {
                //TODO:Ahri—$整数限制10的倍数
                if (value == null || string.IsNullOrEmpty(value) || value.StartsWith("-") || !int.TryParse(value, out int tmpCollectInterval) || tmpCollectInterval < 0 || (tmpCollectInterval > 10 && tmpCollectInterval % 10 != 0))
                {
                    _CollectInterval = "0";
                    return;
                }

                _CollectInterval = value;
                OnPropertyChanged(nameof(CollectInterval));
            }
        }


        //采样时间
        private bool _IsMuitValue = false;
        public bool IsMuitValue
        {
            get { return _IsMuitValue; }
            set
            {
                if (_IsMuitValue == value) return;
                _IsMuitValue = value;
                OnPropertyChanged(nameof(IsMuitValue));
            }
        }

        public string CollectIntervalUnit { set; get; }
        //单位集合
        public List<SelectItem> CollectIntervalUnits { set; get; } = new List<SelectItem>();

        public List<SensorAdditionalPropertie> SensorAdditionalProperties { private set; get; } = InitSensorAdditionalProperties();

        private SensorAdditionalPropertie _SensorAdditionalPropertie;
        public SensorAdditionalPropertie SensorAdditionalPropertieSelected
        {
            get { return _SensorAdditionalPropertie; }
            set
            {
                if (value == null) return;
                _SensorAdditionalPropertie = value;
                SensorAdditionalPropertieChilds = value.Childs;
                SensorAdditionalPropertieTextVisibility = value.Id != null ? Visibility.Visible : Visibility.Collapsed;
                OnPropertyChanged(nameof(SensorAdditionalPropertieSelected));
            }
        }

        private List<SensorAdditionalPropertie> _SensorAdditionalPropertieChilds;
        public List<SensorAdditionalPropertie> SensorAdditionalPropertieChilds
        {
            get { return _SensorAdditionalPropertieChilds; }
            set
            {
                if (value == null) return;
                _SensorAdditionalPropertieChilds = value;
                SensorAdditionalPropertieChild = value.First();
                OnPropertyChanged(nameof(SensorAdditionalPropertieChilds));
            }
        }

        private SensorAdditionalPropertie _SensorAdditionalPropertieChild;
        public SensorAdditionalPropertie SensorAdditionalPropertieChild
        {
            get { return _SensorAdditionalPropertieChild; }
            set
            {
                _SensorAdditionalPropertieChild = value;
                OnPropertyChanged(nameof(SensorAdditionalPropertieChild));
            }
        }

        private Visibility _SensorAdditionalPropertieTextVisibility = Visibility.Collapsed;
        public Visibility SensorAdditionalPropertieTextVisibility
        {
            get { return _SensorAdditionalPropertieTextVisibility; }
            set
            {
                _SensorAdditionalPropertieTextVisibility = value;
                OnPropertyChanged(nameof(SensorAdditionalPropertieTextVisibility));
            }
        }

        private string _SensorAdditionalPropertieTextSN = "0";
        public string SensorAdditionalPropertieTextSN
        {
            get { return _SensorAdditionalPropertieTextSN; }
            set
            {
                if (value == null || string.IsNullOrEmpty(value) || value.StartsWith("-") || !int.TryParse(value, out _) || int.Parse(value) < 0)
                {
                    _SensorAdditionalPropertieTextSN = "0";
                    return;
                }
                _SensorAdditionalPropertieTextSN = value;
                OnPropertyChanged(nameof(SensorAdditionalPropertieTextSN));
            }
        }

        private string _SensorAdditionalPropertieTextId = "0";
        public string SensorAdditionalPropertieTextId
        {
            get { return _SensorAdditionalPropertieTextId; }
            set
            {
                if (value == null || string.IsNullOrEmpty(value) || value.StartsWith("-") || !int.TryParse(value, out _) || int.Parse(value) < 0)
                {
                    _SensorAdditionalPropertieTextId = "0";
                    return;
                }
                _SensorAdditionalPropertieTextId = value;
                OnPropertyChanged(nameof(SensorAdditionalPropertieTextId));
            }
        }

        public class CorrectItem : NotifyPropertyChangedModel
        {
            private string _ReadValue;
            public string ReadValue
            {
                get { return _ReadValue; }
                set
                {
                    if (_ReadValue == value) return;
                    _ReadValue = value;
                    OnPropertyChanged(nameof(ReadValue));
                }
            }
            public string ActualValue { set; get; }
        }

        public class SensorAdditionalPropertie : NotifyPropertyChangedModel
        {
            public object Id { set; get; }
            public string Name { set; get; }
            public string Value { set; get; }
            public int SortIndex { set; get; }
            public List<SensorAdditionalPropertie> Childs { set; get; }
        }

        public static List<SensorAdditionalPropertie> InitSensorAdditionalProperties()
        {
            return new List<SensorAdditionalPropertie>
            {
                new SensorAdditionalPropertie
                {
                    Name = "井口",
                    Value = "WellHead",
                    Childs = new List<SensorAdditionalPropertie>
                    {
                        new SensorAdditionalPropertie{Name = "油压", Value = "OilPressure"},
                        new SensorAdditionalPropertie{Name = "套压", Value = "CasingPressure"},
                        new SensorAdditionalPropertie{Name = "B环空", Value = "BAnnulusAir"},
                        new SensorAdditionalPropertie{Name = "C环空", Value = "DAnnulusAir"},
                        new SensorAdditionalPropertie{Name = "D环空", Value = "DAnnulusAir"},
                        new SensorAdditionalPropertie{Name = "温度", Value = "Temperature"},
                        new SensorAdditionalPropertie{Name = "气产量", Value = "GasRate"},
                        new SensorAdditionalPropertie{Name = "累计气产量", Value = "AccumulatedGas"},
                    }
                },
                new SensorAdditionalPropertie
                {
                    Name = "分离器",
                    Value = "Separator",
                    Childs = new List<SensorAdditionalPropertie>
                    {
                        new SensorAdditionalPropertie{Name = "压力", Value = "Pressure"},
                        new SensorAdditionalPropertie{Name = "温度", Value = "Temperature"},
                        new SensorAdditionalPropertie{Name = "液位", Value = "LiquidLevel"},
                        new SensorAdditionalPropertie{Name = "压差", Value = "PressureDifferential"},
                        new SensorAdditionalPropertie{Name = "下压", Value = "DownholePressure"},
                        new SensorAdditionalPropertie{Name = "下温", Value = "DownholeTemperature"},
                        new SensorAdditionalPropertie{Name = "瞬时排液量", Value = "LiquidRate"},
                        new SensorAdditionalPropertie{Name = "瞬时油量", Value = "OilRate"},
                        new SensorAdditionalPropertie{Name = "瞬时气量", Value = "GasRate"},
                        new SensorAdditionalPropertie{Name = "累计排液量", Value = "AccumulatedLiquid"},
                        new SensorAdditionalPropertie{Name = "累计油量", Value = "AccumulatedOil"},
                        new SensorAdditionalPropertie{Name = "累计气量", Value = "AccumulatedGas"},

                    }
                },
                new SensorAdditionalPropertie
                {
                    Name = "油嘴管汇",
                    Value = "OilNozzlePipeManifold",
                    Childs = new List<SensorAdditionalPropertie>
                    {
                        new SensorAdditionalPropertie{Name = "上压", Value = "UpholePressure"},
                        new SensorAdditionalPropertie{Name = "下压", Value = "DownholePressure"},
                    }
                },
                new SensorAdditionalPropertie
                {
                    Name = "MSRV",
                    Value = "MSRV",
                    Childs = new List<SensorAdditionalPropertie>
                    {
                        new SensorAdditionalPropertie{Name = "上压", Value = "UpholePressure"},
                        new SensorAdditionalPropertie{Name = "上温", Value = "UpholeTemperature"},
                        new SensorAdditionalPropertie{Name = "下压", Value = "DownholePressure"},
                        new SensorAdditionalPropertie{Name = "下温", Value = "DownholeTemperature"},
                    }
                },
                new SensorAdditionalPropertie
                {
                    Name = "捕屑器",
                    Value = "DebrisCatcher",
                    Childs = new List<SensorAdditionalPropertie>
                    {
                        new SensorAdditionalPropertie{Name = "进口压力", Value = "InletPressure"},
                        new SensorAdditionalPropertie{Name = "出口压力", Value = "OutletPressure"},
                    }
                },
                new SensorAdditionalPropertie
                {
                    Name = "除砂器",
                    Value = "Desander",
                    Childs = new List<SensorAdditionalPropertie>
                    {
                        new SensorAdditionalPropertie{Name = "进口压力", Value = "InletPressure" },
                        new SensorAdditionalPropertie{Name = "出口压力", Value = "OutletPressure" },
                        new SensorAdditionalPropertie{Name = "上进口压力", Value = "UpInletPressure" },
                        new SensorAdditionalPropertie{Name = "上出口压力", Value = "UpOutletPressure" },
                        new SensorAdditionalPropertie{Name = "温度", Value = "Temperature" },
                        new SensorAdditionalPropertie{Name = "进口砂量", Value = "InletSandRate" },
                        new SensorAdditionalPropertie{Name = "出口砂量", Value = "OutletSandRate" },
                    }
                },
                new SensorAdditionalPropertie
                {
                    Name = "热交换器",
                    Value = "HeatExchanger",
                    Childs = new List<SensorAdditionalPropertie>
                    {
                        new SensorAdditionalPropertie{Name = "进口压力", Value = "InletPressure" },
                        new SensorAdditionalPropertie{Name = "出口压力", Value = "OutletPressure" },
                        new SensorAdditionalPropertie{Name = "温度", Value = "Temperature" },
                        new SensorAdditionalPropertie{Name = "压力", Value = "Pressure" },
                        new SensorAdditionalPropertie{Name = "孔板压差", Value = "OrificePlateDiffPressure" },
                        new SensorAdditionalPropertie{Name = "下游温度", Value = "DownholeTemperature" },
                        new SensorAdditionalPropertie{Name = "油路液位", Value = "OilLineLiquidLevel" },
                        new SensorAdditionalPropertie{Name = "水路液位", Value = "WaterLineLiquidLevel" },
                    }
                },
                new SensorAdditionalPropertie
                {
                    Name = "缓冲罐",
                    Value = "BufferTank",
                    Childs = new List<SensorAdditionalPropertie>
                    {
                        new SensorAdditionalPropertie{Name = "压力", Value = "Pressure" },
                        new SensorAdditionalPropertie{Name = "液位", Value = "WaterLevel" },
                    }
                },
                new SensorAdditionalPropertie
                {
                    Name = "临界速度流量计",
                    Value = "CriticalFlowrateMeter",
                    Childs = new List<SensorAdditionalPropertie>
                    {
                        new SensorAdditionalPropertie{Name = "上游压力", Value = "UpholePressure" },
                        new SensorAdditionalPropertie{Name = "上游温度", Value = "UpholeTemperature" },
                        new SensorAdditionalPropertie{Name = "下游压力", Value = "DownholePressure" },
                    }
                },
                new SensorAdditionalPropertie
                {
                    Name = "计量罐",
                    Value = "MeteringTank",
                    Childs = new List<SensorAdditionalPropertie>
                    {
                        new SensorAdditionalPropertie{Name = "液位1", Value = "WaterLevel1" },
                        new SensorAdditionalPropertie{Name = "液位2", Value = "WaterLevel2" },
                    }
                },
                new SensorAdditionalPropertie
                {
                    Id=1,
                    Name = "气体探头",
                    Value = "GasProbe",
                    Childs = new List<SensorAdditionalPropertie>
                    {
                        new SensorAdditionalPropertie{Name = "可燃气体", Value = "FlammableGas" },
                        new SensorAdditionalPropertie{Name = "硫化氢气体", Value = "HydrogenSulfide" },
                    }
                }
            };
        }
    }
}
