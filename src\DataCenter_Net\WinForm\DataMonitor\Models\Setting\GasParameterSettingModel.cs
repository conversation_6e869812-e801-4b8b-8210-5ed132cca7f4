﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static SDHD.DC.DataMonitor.Models.Sensor.SensorDetailSettingViewModel;

namespace SDHD.DC.DataMonitor.Models.Setting
{
    public class GasParameterSettingModel : NotifyPropertyChangedModel
    {
        private string _Id;
        public string Id
        {
            get { return _Id; }
            set
            {
                if (value == null || string.IsNullOrEmpty(value) || value.StartsWith("-") || !uint.TryParse(value, out _) || uint.Parse(value) < 0)
                {
                    _Id = "";
                    return;
                }
                _Id = value;
                OnPropertyChanged(nameof(Id));
            }
        }

        private string _DensityRatio = "0.5";

        public string DensityRatio
        {
            get { return _DensityRatio; }
            set
            {
                _DensityRatio = value;
                OnPropertyChanged(nameof(DensityRatio));
            }
        }
        private string _AtmosphericPressure;

        public string AtmosphericPressure
        {
            get { return _AtmosphericPressure; }
            set
            {
                _AtmosphericPressure = value;
                OnPropertyChanged(nameof(AtmosphericPressure));
            }
        }
        private string _CO2MoleFraction;

        public string CO2MoleFraction
        {
            get { return _CO2MoleFraction; }
            set
            {
                _CO2MoleFraction = value;
                OnPropertyChanged(nameof(CO2MoleFraction));
            }
        }
        private string _N2MoleFraction;

        public string N2MoleFraction
        {
            get { return _N2MoleFraction; }
            set
            {
                _N2MoleFraction = value;
                OnPropertyChanged(nameof(N2MoleFraction));
            }
        }
        private string _StandardPipeDiameter;

        public string StandardPipeDiameter
        {
            get { return _StandardPipeDiameter; }
            set
            {
                _StandardPipeDiameter = value;
                OnPropertyChanged(nameof(StandardPipeDiameter));
            }
        }
        private string _StandardWellPlateDiameter;

        public string StandardWellPlateDiameter
        {
            get { return _StandardWellPlateDiameter; }
            set
            {
                _StandardWellPlateDiameter = value;
                OnPropertyChanged(nameof(StandardWellPlateDiameter));
            }
        }
        private string _Process;

        public string Process
        {
            get { return _Process; }
            set
            {
                _Process = value;
                OnPropertyChanged(nameof(Process));
            }
        }

        private ObservableCollection<string> _ProcessList = new();
        public ObservableCollection<string> ProcessList
        {
            get { return _ProcessList; }
            set
            {
                _ProcessList = value;
                OnPropertyChanged(nameof(ProcessList));
            }
        }

        private ObservableCollection<SelectItem> _StuffList = new()
        {
            new SelectItem(){ Text="A3号钢、15号钢",Value="11.75"},
            new SelectItem(){ Text="10号钢",Value="11.60"},
            new SelectItem(){ Text="20号钢",Value="11.16"},
            new SelectItem(){ Text="45号钢",Value="11.59"},
            new SelectItem(){ Text="1Cr13、2Cr13",Value="10.50"},
            new SelectItem(){ Text="1Cr17",Value="10.00"},
            new SelectItem(){ Text="12Cr1MoV",Value="9.80"},
            new SelectItem(){ Text="Cr6SiMo",Value="11.50"},
            new SelectItem(){ Text="2Cr12Ni1MoWV",Value="10.80"},
            new SelectItem(){ Text="1Cr18Ni9Ti",Value="16.60"},
            new SelectItem(){ Text="普通碳钢",Value="10.60"},
            new SelectItem(){ Text="工业用铜",Value="16.00"},
            new SelectItem(){ Text="黄铜",Value="17.80"},
            new SelectItem(){ Text="红铜",Value="17.20"},
        };
        public ObservableCollection<SelectItem> StuffList
        {
            get { return _StuffList; }
            set
            {
                _StuffList = value;
                OnPropertyChanged(nameof(StuffList));
            }
        }

        private string _GDstuff;

        public string GDstuff
        {
            get { return _GDstuff; }
            set
            {
                _GDstuff = value;
                OnPropertyChanged(nameof(GDstuff));
            }
        }
        private string _KBstuff;

        public string KBstuff
        {
            get { return _KBstuff; }
            set
            {
                _KBstuff = value;
                OnPropertyChanged(nameof(KBstuff));
            }
        }


        //public ObservableCollection<SelectItem> ProcessList=new ObservableCollection<SelectItem>() { new SelectItem() { Value="10",Text="10" } };
    }

    public class GasParameterMBSettingModel : NotifyPropertyChangedModel
    {
        private string _Id = "1";
        public string Id
        {
            get { return _Id; }
            set
            {
                if (value == null || string.IsNullOrEmpty(value) || value.StartsWith("-") || !uint.TryParse(value, out _) || uint.Parse(value) < 0)
                {
                    _Id = "";
                    return;
                }
                _Id = value;
                OnPropertyChanged(nameof(Id));
            }
        }

        private string _SpecificGravity = "0.576";

        public string SpecificGravity
        {
            get { return _SpecificGravity; }
            set
            {
                _SpecificGravity = value;
                OnPropertyChanged(nameof(SpecificGravity));
            }
        }
        private string _InMeterRunDiameter = "5.761";

        public string InMeterRunDiameter
        {
            get { return _InMeterRunDiameter; }
            set
            {
                _InMeterRunDiameter = value;
                OnPropertyChanged(nameof(InMeterRunDiameter));
            }
        }
        private string _InOrificeDiameter = "3.5";

        public string InOrificeDiameter
        {
            get { return _InOrificeDiameter; }
            set
            {
                _InOrificeDiameter = value;
                OnPropertyChanged(nameof(InOrificeDiameter));
            }
        }

        private string _DegFTgr = "60";
        public string DegFTgr
        {
            get { return _DegFTgr; }
            set
            {
                _DegFTgr = value;
                OnPropertyChanged(nameof(DegFTgr));
            }
        }

        private string _PsiaPgr = "14.73";

        public string PsiaPgr
        {
            get { return _PsiaPgr; }
            set
            {
                _PsiaPgr = value;
                OnPropertyChanged(nameof(PsiaPgr));
            }
        }

        private string _DegFTb = "60";

        public string DegFTb
        {
            get { return _DegFTb; }
            set
            {
                _DegFTb = value;
                OnPropertyChanged(nameof(DegFTb));
            }
        }


        private string _PsiaPB = "14.73";

        public string PsiaPB
        {
            get { return _PsiaPB; }
            set
            {
                _PsiaPB = value;
                OnPropertyChanged(nameof(PsiaPB));
            }
        }

        private string _AtmosphericPressure = "14.73";

        public string AtmosphericPressure
        {
            get { return _AtmosphericPressure; }
            set
            {
                _AtmosphericPressure = value;
                OnPropertyChanged(nameof(AtmosphericPressure));
            }
        }


        private string _InsentropicExponent = "1.3";

        public string InsentropicExponent
        {
            get { return _InsentropicExponent; }
            set
            {
                _InsentropicExponent = value;
                OnPropertyChanged(nameof(InsentropicExponent));
            }
        }

        private string _CompressibilityOfAirAtStdCond = "0.99959";

        public string CompressibilityOfAirAtStdCond
        {
            get { return _CompressibilityOfAirAtStdCond; }
            set
            {
                _CompressibilityOfAirAtStdCond = value;
                OnPropertyChanged(nameof(CompressibilityOfAirAtStdCond));
            }
        }

        private string _OrificePlateThermalExpansionCofe = "9.25e-06";

        public string OrificePlateThermalExpansionCofe
        {
            get { return _OrificePlateThermalExpansionCofe; }
            set
            {
                _OrificePlateThermalExpansionCofe = value;
                OnPropertyChanged(nameof(OrificePlateThermalExpansionCofe));
            }
        }

        private string _MeterRunThermalExpansionCofe = $"6.2e-06";

        public string MeterRunThermalExpansionCofe
        {
            get { return _MeterRunThermalExpansionCofe; }
            set
            {
                _MeterRunThermalExpansionCofe = value;
                OnPropertyChanged(nameof(MeterRunThermalExpansionCofe));
            }
        }

        private string _DynamicViscosity = $"6.2e-06";

        public string DynamicViscosity
        {
            get { return _DynamicViscosity; }
            set
            {
                _DynamicViscosity = value;
                OnPropertyChanged(nameof(DynamicViscosity));
            }
        }

        private string _Process;

        public string Process
        {
            get { return _Process; }
            set
            {
                _Process = value;
                OnPropertyChanged(nameof(Process));
            }
        }

        private ObservableCollection<string> _ProcessList = new();
        public ObservableCollection<string> ProcessList
        {
            get { return _ProcessList; }
            set
            {
                _ProcessList = value;
                OnPropertyChanged(nameof(ProcessList));
            }
        }
    }

    public class GasParameterSandSettingModel : NotifyPropertyChangedModel
    {
        private string _Id = "1";
        public string Id
        {
            get { return _Id; }
            set
            {
                if (value == null || string.IsNullOrEmpty(value) || value.StartsWith("-") || !uint.TryParse(value, out _) || uint.Parse(value) < 0)
                {
                    _Id = "";
                    return;
                }
                _Id = value;
                OnPropertyChanged(nameof(Id));
            }
        }

        private string _YSXS = "1";

        public string YSXS
        {
            get { return _YSXS; }
            set
            {
                _YSXS = value;
                OnPropertyChanged(nameof(YSXS));
            }
        }
        private string _GDNJ = "50";

        public string GDNJ
        {
            get { return _GDNJ; }
            set
            {
                _GDNJ = value;
                OnPropertyChanged(nameof(GDNJ));
            }
        }
        private string _BJZY = "2000";

        public string BJZY
        {
            get { return _BJZY; }
            set
            {
                _BJZY = value;
                OnPropertyChanged(nameof(BJZY));
            }
        }
        private string _TSPAN;

        public string TSPAN
        {
            get { return _TSPAN; }
            set
            {
                _TSPAN = value;
                OnPropertyChanged(nameof(TSPAN));
            }
        }
        private string _CheckRang = "10";

        public string CheckRang
        {
            get { return _CheckRang; }
            set
            {
                _CheckRang = value;
                OnPropertyChanged(nameof(CheckRang));
            }
        }
        private bool _AUTOGDJZ;

        public bool AUTOGDJZ
        {
            get { return _AUTOGDJZ; }
            set
            {
                _AUTOGDJZ = value;
                OnPropertyChanged(nameof(AUTOGDJZ));
            }
        }

        private bool _AUTOBJZY;

        public bool AUTOBJZY
        {
            get { return _AUTOBJZY; }
            set
            {
                _AUTOBJZY = value;
                OnPropertyChanged(nameof(AUTOBJZY));
            }
        }


        private string _Process;

        public string Process
        {
            get { return _Process; }
            set
            {
                _Process = value;
                OnPropertyChanged(nameof(Process));
            }
        }

        private ObservableCollection<string> _ProcessList = new();
        public ObservableCollection<string> ProcessList
        {
            get { return _ProcessList; }
            set
            {
                _ProcessList = value;
                OnPropertyChanged(nameof(ProcessList));
            }
        }

        private ObservableCollection<SelectItem> _SandJZList = new()
        {
            new SelectItem(){ Text="气井",Value="0"},
            new SelectItem(){ Text="油井",Value="1"},
        };
        public ObservableCollection<SelectItem> SandJZList
        {
            get { return _SandJZList; }
            set
            {
                _SandJZList = value;
                OnPropertyChanged(nameof(SandJZList));
            }
        }

        private string _SandJZ = "0";

        public string SandJZ
        {
            get { return _SandJZ; }
            set
            {
                _SandJZ = value;
                OnPropertyChanged(nameof(SandJZ));
            }
        }

    }
}
