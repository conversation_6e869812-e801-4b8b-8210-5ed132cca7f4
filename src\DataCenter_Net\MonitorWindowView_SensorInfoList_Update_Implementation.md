# MonitorWindowView SensorInfoList 消息处理实现

## 概述

本文档说明了在 `MonitorWindowView` 中实现 SensorInfoList 消息处理的功能，当传感器设置被修改时，自动更新曲线控件中所有 series 的名称和单位。

## 实现文件

### 主要实现文件
- `MonitorWindowView_SensorInfoList_Update.cs` - 包含所有相关的方法实现

## 核心功能

### 1. 事件订阅处理

#### OnSetMessage 方法
```csharp
private void OnSetMessage(string msg)
{
    // 检查是否是SensorInfoList消息
    if (msg == "SensorInfoList")
    {
        // 在UI线程中更新曲线控件
        this.Dispatcher.BeginInvoke(() =>
        {
            UpdateCurveChartSensorNamesAndUnits();
        });
    }
    else if (msg == "UnitInfoList")
    {
        // 单位信息更新时也需要更新曲线控件
        this.Dispatcher.BeginInvoke(() =>
        {
            UpdateCurveChartSensorNamesAndUnits();
        });
    }
}
```

#### OnReceivedMessage 方法
```csharp
private void OnReceivedMessage(IMessage message)
{
    // 处理其他类型的消息
    // 例如实时数据更新等
}
```

### 2. 曲线控件更新

#### UpdateCurveChartSensorNamesAndUnits 方法
- 从 `Global.Messages` 获取最新的 `SensorInfoList` 和 `UnitInfoList`
- 遍历曲线控件中的所有 Y 轴和传感器
- 更新传感器名称和单位信息
- 使用 `CurveChart.SetAllProperty()` 应用更新

### 3. 辅助方法

#### GetSensorDisplayName 方法
- 支持传统传感器和模块设备传感器
- 根据 sensorId 和 sn 参数获取正确的传感器名称

#### GetUnitDisplayName 方法
- 根据 unitId 获取单位名称

#### RefreshSensorNamesAndUnits 方法
- 手动触发传感器名称和单位更新
- 可以在需要时主动调用

## 数据流程

### 1. 传感器设置修改流程
```
用户修改传感器设置 → 保存传感器信息 → ThreadedWorker.SetMessage(SensorInfoList) 
→ Global.NotifySetMessage("SensorInfoList") → Global.OnSetMessage 事件触发 
→ MonitorWindowView.OnSetMessage("SensorInfoList") → UpdateCurveChartSensorNamesAndUnits() 
→ CurveChart.SetAllProperty() → 曲线控件更新显示
```

### 2. 消息处理流程
```
Global.OnSetMessage("SensorInfoList") 
→ 检查消息类型 
→ UI线程调度更新 
→ 获取最新数据 
→ 遍历更新series 
→ 应用到曲线控件
```

## 技术特点

### 1. 线程安全
- 使用 `Dispatcher.BeginInvoke()` 确保UI更新在UI线程中执行
- 避免跨线程操作异常

### 2. 性能优化
- 只在有实际更新时才调用 `SetAllProperty()`
- 使用 `hasUpdates` 标志避免不必要的更新

### 3. 错误处理
- 完整的异常处理和日志记录
- 防止单个错误影响整体功能

### 4. 内存管理
- 在窗口关闭时正确取消事件订阅
- 防止内存泄漏

## 支持的传感器类型

### 1. 传统传感器
- sn 为空或 null
- 使用 sensorId 作为 `SensorInfoList.Items` 的索引
- 返回 `sensorInfoList.Items[sensorId].Name`

### 2. 模块设备传感器
- sn 不为空
- 从 `ModuleItemList` 中查找对应的 sn
- 使用 sensorId 作为该模块下 `Items` 的索引
- 返回 `moduleItems.Items[sensorId].Names`

## 使用方法

### 1. 自动更新
当传感器设置被修改并保存时，系统会自动：
- 发送 SensorInfoList 消息
- 触发所有打开的 MonitorWindowView 更新
- 更新曲线控件中的传感器名称和单位

### 2. 手动更新
```csharp
// 手动触发更新
monitorWindowView.RefreshSensorNamesAndUnits();
```

## 日志记录

系统会记录以下信息：
- 传感器名称更新：`Updated sensor {index} name to: {name}`
- Y轴单位更新：`Updated Y-axis unit to: {unit}`
- 更新成功：`Successfully updated curve chart sensor names and units`
- 更新失败：`Failed to update curve chart sensor names and units`
- 错误信息：详细的异常信息

## 兼容性

### 1. 向后兼容
- 不影响现有的 MonitorWindowView 功能
- 保持原有的事件订阅机制

### 2. 扩展性
- 可以轻松添加其他消息类型的处理
- 支持未来的功能扩展

## 测试建议

### 1. 功能测试
- 修改传感器名称，验证曲线控件是否更新
- 修改传感器单位，验证Y轴标签是否更新
- 测试多个 MonitorWindowView 同时更新

### 2. 性能测试
- 大量传感器时的更新性能
- 频繁更新时的响应性能

### 3. 异常测试
- 数据为空时的处理
- 网络异常时的处理
- 并发访问时的稳定性

## 注意事项

1. **确保事件订阅**：MonitorWindowView 构造函数中必须正确订阅 Global.OnSetMessage 事件
2. **UI线程更新**：所有UI更新必须在UI线程中执行
3. **资源清理**：窗口关闭时必须取消事件订阅
4. **错误处理**：添加适当的异常处理和日志记录
5. **性能考虑**：避免不必要的更新操作

## 总结

这个实现提供了完整的 SensorInfoList 消息处理功能，确保当传感器设置被修改时，所有打开的 MonitorWindowView 都能自动更新曲线控件中的传感器名称和单位信息，提升了用户体验和数据一致性。
