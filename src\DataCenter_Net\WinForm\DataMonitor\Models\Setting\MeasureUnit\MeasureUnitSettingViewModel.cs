﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Media;

namespace SDHD.DC.DataMonitor.Models.Setting.MeasureUnit
{
    internal class MeasureUnitSettingViewModel : NotifyPropertyChangedModel
    {
        public string WindowTitle { set; get; }
        public string WellStationId { set; get; }
        public bool IsAllowAddOrEdit { set; get; }
        public ObservableCollection<MeasureUnitInfo> MeasureUnits { get; set; } = new ObservableCollection<MeasureUnitInfo>();

        public class MeasureUnitInfo
        {
            public string MeasureUnitId { get; set; }
            public string DefaultIcon { set; get; }
            public string Name { set; get; }
            public string CategoryDesc { set; get; }
            public string ExchangeRelationship { set; get; }
            public string DefaultRange { set; get; }
            public string DefaultDisplayRange { set; get; }
            public SolidColorBrush DefaultColor { set; get; }
        }
    }
}
