# 修复 MainWindow.xaml.cs 中的 UserHandler 引用
$content = Get-Content 'WinForm\DataMonitor\MainWindow.xaml.cs' -Encoding UTF8

# 修复第1701行 - LogOut 调用
$content = $content -replace 'UserHandler\.Instance\.LogOut\(\);', '// 架构简化：移除UserHandler依赖，用户登出功能已简化'

# 修复第2369行 - RefreshLoginUser 调用
$content = $content -replace '_CurrentUser = UserHandler\.Instance\.RefreshLoginUser\(\);', '_CurrentUser = new User { UserName = "Administrator", Rank = new Rank { Level = 0 } }; // 架构简化：移除UserHandler依赖'

# 保存文件
$content | Out-File 'WinForm\DataMonitor\MainWindow.xaml.cs' -Encoding UTF8

Write-Host "UserHandler 引用已修复"
