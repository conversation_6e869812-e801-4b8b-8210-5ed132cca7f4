<Window x:Class="SDHD.DC.DataMonitor.Views.SplashScreen.SplashScreenWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="启动中..." 
        Height="400" Width="600"
        WindowStyle="None" 
        AllowsTransparency="True" 
        Background="Transparent"
        WindowStartupLocation="CenterScreen"
        Topmost="True"
        ShowInTaskbar="False">

    <Window.Resources>
        <!-- 工业现代风格样式 -->
        <Style x:Key="ModernProgressBar" TargetType="ProgressBar">
            <Setter Property="Height" Value="6"/>
            <Setter Property="Background" Value="#2d3748"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ProgressBar">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="3">
                            <Grid>
                                <Border Name="PART_Track" 
                                        Background="{TemplateBinding Background}" 
                                        CornerRadius="3"/>
                                <Border Name="PART_Indicator"
                                        HorizontalAlignment="Left"
                                        CornerRadius="3">
                                    <Border.Background>
                                        <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                                            <GradientStop Color="#0366d6" Offset="0"/>
                                            <GradientStop Color="#58a6ff" Offset="1"/>
                                        </LinearGradientBrush>
                                    </Border.Background>
                                </Border>
                            </Grid>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 淡入动画 -->
        <Storyboard x:Key="FadeInAnimation">
            <DoubleAnimation Storyboard.TargetProperty="Opacity" 
                           From="0" To="1" Duration="0:0:0.5"/>
        </Storyboard>

        <!-- 脉冲动画 -->
        <Storyboard x:Key="PulseAnimation" RepeatBehavior="Forever">
            <DoubleAnimation Storyboard.TargetProperty="Opacity" 
                           From="0.5" To="1" Duration="0:0:1" 
                           AutoReverse="True"/>
        </Storyboard>
    </Window.Resources>

    <Border CornerRadius="12" Background="Transparent">
        <Border.Effect>
            <DropShadowEffect Color="Black" BlurRadius="20" ShadowDepth="0" Opacity="0.5"/>
        </Border.Effect>
        
        <!-- 主背景 -->
        <Border CornerRadius="12">
            <Border.Background>
                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                    <GradientStop Color="#1a1a1a" Offset="0"/>
                    <GradientStop Color="#0d1117" Offset="1"/>
                </LinearGradientBrush>
            </Border.Background>
            
            <!-- 边框 -->
            <Border BorderThickness="1" CornerRadius="12">
                <Border.BorderBrush>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                        <GradientStop Color="#30363d" Offset="0"/>
                        <GradientStop Color="#21262d" Offset="1"/>
                    </LinearGradientBrush>
                </Border.BorderBrush>

                <Grid Margin="40">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Logo区域 -->
                    <StackPanel Grid.Row="0" HorizontalAlignment="Center" Margin="0,20,0,30">
                        <!-- 这里可以放置公司Logo或产品图标 -->
                        <Ellipse Width="80" Height="80" Margin="0,0,0,20">
                            <Ellipse.Fill>
                                <RadialGradientBrush>
                                    <GradientStop Color="#0366d6" Offset="0"/>
                                    <GradientStop Color="#1f2937" Offset="1"/>
                                </RadialGradientBrush>
                            </Ellipse.Fill>
                        </Ellipse>
                        
                        <!-- 产品名称 -->
                        <TextBlock Text="{Binding ProductName}" 
                                   FontSize="24" 
                                   FontWeight="Bold" 
                                   Foreground="#f0f6fc" 
                                   HorizontalAlignment="Center"
                                   Margin="0,0,0,5"/>
                        
                        <TextBlock Text="{Binding ProductSubName}" 
                                   FontSize="16" 
                                   Foreground="#8b949e" 
                                   HorizontalAlignment="Center"/>
                    </StackPanel>

                    <!-- 进度条区域 -->
                    <StackPanel Grid.Row="2" Margin="0,20">
                        <Grid Margin="0,0,0,10">
                            <ProgressBar Value="{Binding Progress}" 
                                       Maximum="100" 
                                       Style="{StaticResource ModernProgressBar}"/>
                            
                            <!-- 进度百分比 -->
                            <TextBlock Text="{Binding ProgressText}" 
                                     FontSize="12" 
                                     Foreground="#58a6ff" 
                                     HorizontalAlignment="Right" 
                                     VerticalAlignment="Center"
                                     Margin="0,-20,0,0"/>
                        </Grid>
                    </StackPanel>

                    <!-- 状态文本 -->
                    <TextBlock Grid.Row="3" 
                             Text="{Binding StatusText}" 
                             FontSize="14" 
                             Foreground="#f0f6fc" 
                             HorizontalAlignment="Center"
                             Margin="0,10">
                        <TextBlock.Triggers>
                            <EventTrigger RoutedEvent="Binding.TargetUpdated">
                                <BeginStoryboard Storyboard="{StaticResource FadeInAnimation}"/>
                            </EventTrigger>
                        </TextBlock.Triggers>
                    </TextBlock>

                    <!-- 错误信息 -->
                    <Border Grid.Row="4" 
                          Visibility="{Binding ErrorVisibility}"
                          Background="#dc2626" 
                          CornerRadius="6" 
                          Padding="15,10" 
                          Margin="0,10">
                        <TextBlock Text="{Binding ErrorMessage}" 
                                 Foreground="White" 
                                 FontSize="12" 
                                 TextWrapping="Wrap"
                                 HorizontalAlignment="Center"/>
                    </Border>

                    <!-- 版本信息 -->
                    <TextBlock Grid.Row="6" 
                             Text="{Binding VersionText}" 
                             FontSize="12" 
                             Foreground="#6e7681" 
                             HorizontalAlignment="Center"
                             Margin="0,20,0,0"/>
                </Grid>
            </Border>
        </Border>
    </Border>

    <Window.Triggers>
        <EventTrigger RoutedEvent="Window.Loaded">
            <BeginStoryboard Storyboard="{StaticResource FadeInAnimation}"/>
        </EventTrigger>
    </Window.Triggers>
</Window>
