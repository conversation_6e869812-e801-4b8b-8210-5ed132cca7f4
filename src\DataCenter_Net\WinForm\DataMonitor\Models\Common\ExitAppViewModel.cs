﻿using SDHD.DC.Utilities;
using SDHD.DC.Utilities.WPF.Extensions;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Media;

namespace SDHD.DC.DataMonitor.Models.Common
{
    public class ExitAppViewModel : NotifyPropertyChangedModel
    {
        private bool _IsExitServer;
        public bool IsExitServer
        {
            get { return _IsExitServer; }
            set
            {
                _IsExitServer = value;
                OnPropertyChanged(nameof(IsExitServer));
            }
        }
    }
}
