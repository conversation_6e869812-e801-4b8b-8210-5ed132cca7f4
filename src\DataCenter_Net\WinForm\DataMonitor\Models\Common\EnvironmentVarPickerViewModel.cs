﻿using SDHD.DC.Utilities;
using SDHD.DC.Utilities.WPF.Extensions;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Media;

namespace SDHD.DC.DataMonitor.Models.Common
{
    public class EnvironmentVarPickerViewModel : NotifyPropertyChangedModel
    {
        public string WellStationId { get; set; }
        public List<EnvironmentVarInfo> EnvironmentVars { get; set; } = new List<EnvironmentVarInfo>();
        public class EnvironmentVarInfo
        {
            public string EnvironmentVarId { set; get; }
            public string Name { set; get; }
            public string Value { set; get; }
        }
    }
}
