﻿using SDHD.DC.Utilities.Constants;
using SDHD.DC.Utilities.Extensions;
using SDHD.DC.Utilities.WPF.Extensions;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Media;

namespace SDHD.DC.DataMonitor.Models.MonitorView
{
    public class MonitorViewSettingViewModel : NotifyPropertyChangedModel
    {
        public string WindowTitle { set; get; }
        public string WellStationId { set; get; }
        public bool IsFirstCreateNew { set; get; }
        public string MonitorViewItemId { set; get; }
        public string MonitorName { set; get; }
        public string YScaleCount { set; get; } = $"{DefaultValues.MonitorViewDefaultSmallTickCount}";
        public bool IsShowWorkStatus { set; get; }

        private string _BackgroudColor;
        public string BackgroudColor
        {
            get { return _BackgroudColor; }
            set
            {
                if (_BackgroudColor == value) return;
                _BackgroudColor = value;
                OnPropertyChanged(nameof(BackgroudColor));
                OnPropertyChanged(nameof(BackgroudColorBrush));
            }
        }
        public SolidColorBrush BackgroudColorBrush
        {
            get
            {
                return BackgroudColor.ConvertToControlColorBrush(true);
            }
        }
        private string _TitleFontColor;
        public string TitleFontColor
        {
            get { return _TitleFontColor; }
            set
            {
                if (_TitleFontColor == value) return;
                _TitleFontColor = value;
                OnPropertyChanged(nameof(TitleFontColor));
                OnPropertyChanged(nameof(TitleFontColorBrush));
            }
        }
        public SolidColorBrush TitleFontColorBrush
        {
            get
            {
                return TitleFontColor.ConvertToControlColorBrush(true);
            }
        }
        private string _TableCellBorderLineColor;
        public string TableCellBorderLineColor
        {
            get { return _TableCellBorderLineColor; }
            set
            {
                if (_TableCellBorderLineColor == value) return;
                _TableCellBorderLineColor = value;
                OnPropertyChanged(nameof(TableCellBorderLineColor));
                OnPropertyChanged(nameof(TableCellBorderLineColorBrush));
            }
        }
        public SolidColorBrush TableCellBorderLineColorBrush
        {
            get
            {
                return TableCellBorderLineColor.ConvertToControlColorBrush(true);
            }
        }

        private string _XLineTimeFontColor;
        public string XLineTimeFontColor
        {
            get { return _XLineTimeFontColor; }
            set
            {
                if (_XLineTimeFontColor == value) return;
                _XLineTimeFontColor = value;
                OnPropertyChanged(nameof(XLineTimeFontColor));
                OnPropertyChanged(nameof(XLineTimeFontColorBrush));
            }
        }
        public SolidColorBrush XLineTimeFontColorBrush
        {
            get
            {
                return XLineTimeFontColor.ConvertToControlColorBrush(true);
            }
        }

        private string _TableBorderLineColor;
        public string TableBorderLineColor
        {
            get { return _TableBorderLineColor; }
            set
            {
                if (_TableBorderLineColor == value) return;
                _TableBorderLineColor = value;
                OnPropertyChanged(nameof(TableBorderLineColor));
                OnPropertyChanged(nameof(TableBorderLineColorBrush));
            }
        }
        public SolidColorBrush TableBorderLineColorBrush
        {
            get
            {
                return TableBorderLineColor.ConvertToControlColorBrush(true);
            }
        }
        private string _WorkStatusLineColor;
        public string WorkStatusLineColor
        {
            get { return _WorkStatusLineColor; }
            set
            {
                if (_WorkStatusLineColor == value) return;
                _WorkStatusLineColor = value;
                OnPropertyChanged(nameof(WorkStatusLineColor));
                OnPropertyChanged(nameof(WorkStatusLineColorBrush));
            }
        }
        public SolidColorBrush WorkStatusLineColorBrush
        {
            get
            {
                return WorkStatusLineColor.ConvertToControlColorBrush(true);
            }
        }

        private string _WorkStatusTextColor;
        public string WorkStatusTextColor
        {
            get { return _WorkStatusTextColor; }
            set
            {
                if (_WorkStatusTextColor == value) return;
                _WorkStatusTextColor = value;
                OnPropertyChanged(nameof(WorkStatusTextColor));
                OnPropertyChanged(nameof(WorkStatusTextColorBrush));
            }
        }
        public SolidColorBrush WorkStatusTextColorBrush
        {
            get
            {
                return WorkStatusTextColor.ConvertToControlColorBrush(true);
            }
        }

        /// <summary>
        /// 1个大刻度表示多少时间（秒、分)
        /// </summary>
        public string XScaleLabelIntervalTime { set; get; } = $"{DefaultValues.MonitorViewDefaultBigTickCount}";

        /// 1个大刻度单位
        public string XScaleLabelIntervalUnit { set; get; }
        /// <summary>
        /// 1个等于一个格子
        /// </summary>
        public string YCategoryXInterval { set; get; } = $"{DefaultValues.MonitorViewDefaultYCategoryXInterval}";

        public string SensorDataFontSize { set; get; }
        /// <summary>
        /// 整点显示数据时间间隔
        /// </summary>
        public string IntDisplayTimeInterval { set; get; }

        public bool IsDisplayMultipleLines { set; get; }
        public bool IsShowAxisRelatedSensor { set; get; }
        public string DisconnectLineDataCount { set; get; }
        public string LineWidth { set; get; } = $"{DefaultValues.MonitorViewDefaultLineWidth}";
        public bool IsShowSerialNo { set; get; }

        /// <summary>
        /// 是否显示批注
        /// </summary>
        public bool IsShowCheckMarks { set; get; }

        public List<SelectItem> YScaleCounts { set; get; } = new List<SelectItem>();
        public List<SelectItem> XScaleLabelIntervalUnits { set; get; } = new List<SelectItem>();
    }
}
