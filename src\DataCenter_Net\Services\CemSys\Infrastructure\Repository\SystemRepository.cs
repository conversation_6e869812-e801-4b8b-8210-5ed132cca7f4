﻿using ProjectStruct;
using SDHD.DC.CemSys.ApplicationCore.Domain.Entities;
using SDHD.DC.CemSys.ApplicationCore.Interfaces.Repository;
using SDHD.DC.CemSys.Infrastructure.Mapping;
using SDHD.DC.Utilities;
using SDHD.DC.Utilities.Constants;
using SDHD.DC.Utilities.DataStore;
using SDHD.DC.Utilities.Net;
using SDHD.DC.Utilities.ServiceExtensions;
using System;
using System.Threading.Tasks;

namespace SDHD.DC.CemSys.Infrastructure.Repository
{
    public class SystemRepository : BaseRepository, ISystemRepository
    {
        private ITCPPbStore _TCPPbStore;
        public SystemRepository()
        {
            _TCPPbStore = ServiceHelper.GetService<ITCPPbStore>();
        }
        public Task<bool> TestServerConnection(string serverIP, int port, int connectionTimeout, out string errorMsg)
        {
            errorMsg = null;
            if (TCPClientHelper.TryConnectTest(serverIP, port, connectionTimeout, out string error))
            {
                return Task.FromResult(true);
            }
            errorMsg = error;
            return Task.FromResult(false);
        }
        public Task CloseServerConnection()
        {
            if (_TCPPbStore != null)
            {
                _TCPPbStore.Close();
            }
            return Task.CompletedTask;
        }
        private async Task<PBEntities.SysConfig> GetSysConfigDataEntity()
        {
            var dataEntity = await GetDataFromPb<PBEntities.SysConfig>(StoreFiles.RegisterData, null);
            return dataEntity ?? new PBEntities.SysConfig();
        }
        private async Task<bool> SaveSysConfigDataEntity(PBEntities.SysConfig dataEntity)
        {
            return await SaveDataAsPb(StoreFiles.RegisterData, dataEntity, null);
        }
        public async Task<SysConfig> GetSysConfig()
        {
            SysConfig result = new SysConfig();
            var dataEntity = await GetSysConfigDataEntity();
            if (dataEntity != null)
            {
                result = dataEntity.ToDomainEntity(result);
            }
            return result;
        }
        public async Task<bool> UpdateSysConfig(RegisterData sysConfig)
        {
            var dataEntity = await GetRegisterData();
            if (dataEntity == null)
            {
                dataEntity = new RegisterData();
            }
            var entity = new SysBase.Types.sysInfo();
            entity.User = dataEntity.UserName;
            entity.RegStr = dataEntity.AuthCode;
            entity.MachineCode = dataEntity.machineCode;
            entity.LastPro = dataEntity.lastPro;
            //dataEntity = sysConfig.ToDataEntity(dataEntity);
            await ExecuteUpdateMessage<SysBase.Types.sysInfo>(-1,entity);
            //await SaveSysConfigDataEntity(dataEntity);
            return true;
        }
        public async Task<RegisterData> GetRegisterData()
        {
            var dataEntity = new RegisterData();
            var entity = await ExecuteGetAllMessage<SysBase.Types.sysInfo>();
            if (entity == null)
                entity = new SysBase.Types.sysInfo();
            dataEntity.UserName = entity.User;
            dataEntity.AuthCode = entity.RegStr;
            dataEntity.machineCode=entity.MachineCode;
            dataEntity.lastPro=entity.LastPro;
            return dataEntity;
            //RegisterData result = null;
            //var dataEntity = await GetSysConfigDataEntity();
            //if (dataEntity != null)
            //{
            //    result = new RegisterData();
            //    result.UserName = dataEntity.UnitName;
            //    result.AuthCode = dataEntity.AuthCode;
            //}
            //return result;
        }
        public async Task<bool> SaveRegisterData(RegisterData entity)
        {
            var dataEntity = new SysBase.Types.sysInfo();

            dataEntity.User = entity.UserName;
            dataEntity.RegStr = entity.AuthCode;
            await ExecuteAddMessage(dataEntity);
            return true;
        }
        //public async Task<bool> SaveRegisterData(RegisterData entity)
        //{
        //    var dataEntity = await GetSysConfigDataEntity();
        //    if (dataEntity == null)
        //    {
        //        dataEntity = new PBEntities.SysConfig();
        //    }
        //    dataEntity.UnitName = entity.UserName;
        //    dataEntity.AuthCode = entity.AuthCode;
        //    await SaveSysConfigDataEntity(dataEntity);
        //    return true;
        //}
        public async Task<SysExternal> GetSysExternal()
        {
            var dataEntity = await GetDataFromPb<PBEntities.SysExternal>(StoreFiles.SysExternal, null);
            if (dataEntity == null)
            {
                dataEntity = new PBEntities.SysExternal();
            }
            var result = new SysExternal();
            result = dataEntity.ToDomainEntity(result);
            return result;
        }
        public async Task<bool> UpdateSysExternal(SysExternal entity)
        {
            var dataEntity = await GetDataFromPb<PBEntities.SysExternal>(StoreFiles.SysExternal, null);
            if (dataEntity == null)
            {
                dataEntity = new PBEntities.SysExternal();
            }
            dataEntity = entity.ToDataEntity(dataEntity);
            await SaveDataAsPb(StoreFiles.SysExternal, dataEntity, null);
            return true;
        }
    }
}
