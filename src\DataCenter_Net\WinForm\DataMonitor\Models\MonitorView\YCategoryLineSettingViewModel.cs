﻿using SDHD.DC.DataMonitor.Utils;
using SDHD.DC.Utilities;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Media;

namespace SDHD.DC.DataMonitor.Models.MonitorView
{
    public class YCategoryLineSettingViewModel : NotifyPropertyChangedModel
    {
        public string WellStationId { get; set; }
        public string MonitorViewItemId { get; set; }
        private string _SensorCategoryId;
        public string SensorCategoryId
        {
            get { return _SensorCategoryId; }
            set
            {
                if (_SensorCategoryId != value)
                {
                    _SensorCategoryId = value;
                    OnPropertyChanged(nameof(SensorCategoryId));
                }
            }
        }
        private string _MeasureUnitId;
        public string MeasureUnitId
        {
            get { return _MeasureUnitId; }
            set
            {
                if (_MeasureUnitId != value)
                {
                    _MeasureUnitId = value;
                    OnPropertyChanged(nameof(MeasureUnitId));
                }
            }
        }
        private string _MinRange;
        public string MinRange
        {
            get { return _MinRange; }
            set
            {
                if (_MinRange != value)
                {
                    _MinRange = value;
                    OnPropertyChanged(nameof(MinRange));
                }
            }
        }
        private string _MaxRange;
        public string MaxRange
        {
            get { return _MaxRange; }
            set
            {
                if (_MaxRange != value)
                {
                    _MaxRange = value;
                    OnPropertyChanged(nameof(MaxRange));
                }
            }
        }
        private string _LineWidth;
        public string LineWidth
        {
            get { return _LineWidth; }
            set
            {
                if (_LineWidth != value)
                {
                    _LineWidth = value;
                    OnPropertyChanged(nameof(LineWidth));
                }
            }
        }
        private string _LineColor;
        public string LineColor
        {
            get { return _LineColor; }
            set
            {
                if (_LineColor != value)
                {
                    _LineColor = value;
                    OnPropertyChanged(nameof(LineColor));
                    OnPropertyChanged(nameof(LineColorBrush));
                }
            }
        }
        public SolidColorBrush LineColorBrush
        {
            get
            {
                return ColorExtensionsWPF.ConvertToBrush(LineColor, true);
            }
        }
        public List<SensorInfo> Sensors { set; get; } = new List<SensorInfo>();
        public List<SelectItem> SensorCategories { set; get; } = new List<SelectItem>();

        private List<SelectItem> _MeasureUnits = new List<SelectItem>();
        public List<SelectItem> MeasureUnits
        {
            get { return _MeasureUnits; }
            set
            {
                _MeasureUnits = value;
                OnPropertyChanged(nameof(MeasureUnits));
            }
        }

        public class SensorInfo
        {
            public string SensorId { get; set; }
            public int Category { set; get; }
            public string MeasureUnitId { set; get; }
            public string LineColor { set; get; }
            public int SeqNo { set; get; }
        }
    }
}
