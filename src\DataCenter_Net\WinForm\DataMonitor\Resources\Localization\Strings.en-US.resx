<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AppTitle" xml:space="preserve">
    <value>Field DAQ System</value>
  </data>
  <data name="Welcome" xml:space="preserve">
    <value>Welcome to Field DAQ System</value>
  </data>
  <data name="MenuFile" xml:space="preserve">
    <value>File</value>
  </data>
  <data name="MenuEdit" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="MenuView" xml:space="preserve">
    <value>View</value>
  </data>
  <data name="MenuTools" xml:space="preserve">
    <value>Tools</value>
  </data>
  <data name="MenuHelp" xml:space="preserve">
    <value>Help</value>
  </data>
  <data name="MenuSettings" xml:space="preserve">
    <value>Settings</value>
  </data>
  <data name="MenuExit" xml:space="preserve">
    <value>Exit</value>
  </data>
  <data name="MenuCurrentProject" xml:space="preserve">
    <value>Wellsite</value>
  </data>
  <data name="MenuAlarm" xml:space="preserve">
    <value>Alerts</value>
  </data>
  <data name="MenuWindow" xml:space="preserve">
    <value>Window</value>
  </data>
  <data name="MenuDeleteCurrentMonitor" xml:space="preserve">
    <value>Delete Dashboard</value>
  </data>
  <data name="ButtonOk" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="ButtonCancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="ButtonSave" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="ButtonClose" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="ButtonApply" xml:space="preserve">
    <value>Apply</value>
  </data>
  <data name="SettingsTitle" xml:space="preserve">
    <value>System Config</value>
  </data>
  <data name="LanguageSettings" xml:space="preserve">
    <value>Language</value>
  </data>
  <data name="SelectLanguage" xml:space="preserve">
    <value>Select Language:</value>
  </data>
  <data name="LoadingData" xml:space="preserve">
    <value>Loading data...</value>
  </data>
  <data name="DataSaved" xml:space="preserve">
    <value>Data saved successfully</value>
  </data>
  <data name="ErrorOccurred" xml:space="preserve">
    <value>Error occurred</value>
  </data>
  <data name="ConnectionError" xml:space="preserve">
    <value>Connection Error</value>
  </data>
  <data name="MenuNewProject" xml:space="preserve">
    <value>New Wellsite</value>
  </data>
  <data name="MenuOpenHistoryProject" xml:space="preserve">
    <value>Open Wellsite</value>
  </data>
  <data name="MenuRefreshHistoryProject" xml:space="preserve">
    <value>Refresh List</value>
  </data>
  <data name="MenuStartCollect" xml:space="preserve">
    <value>Start Acquisition</value>
  </data>
  <data name="MenuStopCollect" xml:space="preserve">
    <value>Stop Acquisition</value>
  </data>
  <data name="MenuNewGraphMonitor" xml:space="preserve">
    <value>New Dashboard</value>
  </data>
  <data name="MenuOpenGraphMonitor" xml:space="preserve">
    <value>Open Dashboard</value>
  </data>
  <data name="MenuProjectLog" xml:space="preserve">
    <value>Operation Log</value>
  </data>
  <data name="MenuExport" xml:space="preserve">
    <value>Export</value>
  </data>
  <data name="MenuExportPanorama" xml:space="preserve">
    <value>Full Dataset</value>
  </data>
  <data name="MenuExportTimePeriod" xml:space="preserve">
    <value>Timeframe Data</value>
  </data>
  <data name="MenuHistoricalDataQuery" xml:space="preserve">
    <value>Historical Analysis</value>
  </data>
  <data name="MenuSensorSettings" xml:space="preserve">
    <value>Sensors</value>
  </data>
  <data name="MenuProjectSettings" xml:space="preserve">
    <value>Wellsite Setup</value>
  </data>
  <data name="MenuMeasurementUnitSettings" xml:space="preserve">
    <value>Measurement Unit Settings</value>
  </data>
  <data name="MenuUnitTypeListSettings" xml:space="preserve">
    <value>Unit Type Settings</value>
  </data>
  <data name="MenuTCPNetworkDeviceSettings" xml:space="preserve">
    <value>TCP/IP Devices</value>
  </data>
  <data name="MenuRedisServiceSettings" xml:space="preserve">
    <value>Redis Service</value>
  </data>
  <data name="MenuModBusDeviceSettings" xml:space="preserve">
    <value>ModBus Devices</value>
  </data>
  <data name="MenuModBusTCPDataSettings" xml:space="preserve">
    <value>ModBus TCP</value>
  </data>
  <data name="MenuS7PointTableSettings" xml:space="preserve">
    <value>S7 Addressing</value>
  </data>
  <data name="MenuInterlockRuleSettings" xml:space="preserve">
    <value>Safety Interlocks</value>
  </data>
  <data name="MenuGBGasProductionParameterSettings" xml:space="preserve">
    <value>GB Flow Params</value>
  </data>
  <data name="MenuUSGasProductionParameterSettings" xml:space="preserve">
    <value>US Flow Params</value>
  </data>
  <data name="MenuSandVolumeParameterSettings" xml:space="preserve">
    <value>Sand Analysis</value>
  </data>
  <data name="MenuLanguageSettings" xml:space="preserve">
    <value>Language</value>
  </data>
  <data name="MenuAlarmSettings" xml:space="preserve">
    <value>Alert Thresholds</value>
  </data>
  <data name="MenuAlarmInformation" xml:space="preserve">
    <value>Active Alerts</value>
  </data>
  <data name="MenuAlarmLog" xml:space="preserve">
    <value>Alert History</value>
  </data>
  <data name="MenuVerticalWindowArrangement" xml:space="preserve">
    <value>Vertical Layout</value>
  </data>
  <data name="MenuHorizontalWindowArrangement" xml:space="preserve">
    <value>Horizontal Layout</value>
  </data>
  <data name="MenuCloseAllWindows" xml:space="preserve">
    <value>Close All</value>
  </data>
  <data name="MenuSetAsFloatingWindow" xml:space="preserve">
    <value>Float Window</value>
  </data>
  <data name="MenuConfigureWebWindow" xml:space="preserve">
    <value>Web Interface</value>
  </data>
  <data name="MenuRegister" xml:space="preserve">
    <value>License</value>
  </data>
  <data name="ButtonStartCollection" xml:space="preserve">
    <value>Start</value>
  </data>
  <data name="ButtonStopCollection" xml:space="preserve">
    <value>Stop</value>
  </data>
  <data name="ButtonSensorSettings" xml:space="preserve">
    <value>Sensors</value>
  </data>
  <data name="ButtonDataMonitor" xml:space="preserve">
    <value>Monitor</value>
  </data>
  <data name="Button3DFlowDiagram" xml:space="preserve">
    <value>3D View</value>
  </data>
  <data name="Button2DFlowDiagram" xml:space="preserve">
    <value>2D View</value>
  </data>
  <data name="ButtonLaserPlatform" xml:space="preserve">
    <value>Laser System</value>
  </data>
  <data name="LabelServiceNotStarted" xml:space="preserve">
    <value>Service Inactive</value>
  </data>
  <data name="LabelSoftwareNotRegistered" xml:space="preserve">
    <value>License Required</value>
  </data>
  <data name="ButtonStartService" xml:space="preserve">
    <value>Start Service</value>
  </data>
  <data name="LabelCurrentProject" xml:space="preserve">
    <value>Wellsite:</value>
  </data>
  <!-- Help Menu Items -->
  <data name="MenuRegister" xml:space="preserve">
    <value>Register</value>
  </data>
  <data name="MenuLanguageSettings" xml:space="preserve">
    <value>Language Settings</value>
  </data>
  <data name="MenuLanguageEnglish" xml:space="preserve">
    <value>English</value>
  </data>
  <data name="MenuLanguageChinese" xml:space="preserve">
    <value>Chinese</value>
  </data>
  <data name="MenuLanguageRussian" xml:space="preserve">
    <value>Russian</value>
  </data>
  
  <!-- AlarmListControl Column Headers -->
  <data name="AlarmSensorName" xml:space="preserve">
    <value>Sensor Name</value>
  </data>
  <data name="AlarmLevel" xml:space="preserve">
    <value>Alert Level</value>
  </data>
  <data name="AlarmCondition" xml:space="preserve">
    <value>Condition</value>
  </data>
  <data name="AlarmValue" xml:space="preserve">
    <value>Alert Value</value>
  </data>
  <data name="AlarmRealTimeValue" xml:space="preserve">
    <value>Real-time Value</value>
  </data>
  <data name="AlarmContent" xml:space="preserve">
    <value>Alert Content</value>
  </data>
  <data name="AlarmStatus" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="AlarmStartTime" xml:space="preserve">
    <value>Start Time</value>
  </data>
  <data name="AlarmStopPlay" xml:space="preserve">
    <value>Stop Sound</value>
  </data>
  
  <!-- Alarm Level Names -->
  <data name="AlarmLevelCritical" xml:space="preserve">
    <value>Critical</value>
  </data>
  <data name="AlarmLevelHigh" xml:space="preserve">
    <value>High</value>
  </data>
  <data name="AlarmLevelMedium" xml:space="preserve">
    <value>Medium</value>
  </data>
  <data name="AlarmLevelLow" xml:space="preserve">
    <value>Low</value>
  </data>
  
  <!-- Alarm Condition Symbols -->
  <data name="ConditionGreaterThan" xml:space="preserve">
    <value>&gt;</value>
  </data>
  <data name="ConditionGreaterEqual" xml:space="preserve">
    <value>&gt;=</value>
  </data>
  <data name="ConditionLessThan" xml:space="preserve">
    <value>&lt;</value>
  </data>
  <data name="ConditionLessEqual" xml:space="preserve">
    <value>&lt;=</value>
  </data>
  <data name="ConditionEqual" xml:space="preserve">
    <value>=</value>
  </data>
  <data name="ConditionNotEqual" xml:space="preserve">
    <value>&lt;&gt;</value>
  </data>
  
  <!-- Warning Type Descriptions -->
  <data name="WarningTypeLessThanLower" xml:space="preserve">
    <value>Below Lower Limit</value>
  </data>
  <data name="WarningTypeGreaterThanUpper" xml:space="preserve">
    <value>Above Upper Limit</value>
  </data>
  <data name="WarningTypeLessEqualLower" xml:space="preserve">
    <value>&lt;= Lower Limit</value>
  </data>
  <data name="WarningTypeGreaterEqualUpper" xml:space="preserve">
    <value>&gt;= Upper Limit</value>
  </data>
  <data name="WarningTypeEqualValue" xml:space="preserve">
    <value>Equal to Value</value>
  </data>
  <data name="WarningTypeUnknown" xml:space="preserve">
    <value>Unknown Type</value>
  </data>

  <!-- SensorAlarmConfigControl Strings -->
  <data name="SensorAlarmConfigList" xml:space="preserve">
    <value>Sensor Alert Configuration List</value>
  </data>
  <data name="AddMenuItem" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="DeleteMenuItem" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="ColumnSN" xml:space="preserve">
    <value>SN</value>
  </data>
  <data name="ColumnID" xml:space="preserve">
    <value>ID</value>
  </data>
  <data name="ColumnName" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="SensorInfo" xml:space="preserve">
    <value>Sensor Information</value>
  </data>
  <data name="LabelSN" xml:space="preserve">
    <value>SN:</value>
  </data>
  <data name="LabelID" xml:space="preserve">
    <value>ID:</value>
  </data>
  <data name="LabelName" xml:space="preserve">
    <value>Name:</value>
  </data>
  <data name="ThresholdAlarmRules" xml:space="preserve">
    <value>Threshold Alert Rules</value>
  </data>
  <data name="ColumnSeverity" xml:space="preserve">
    <value>Severity</value>
  </data>
  <data name="ColumnOperator" xml:space="preserve">
    <value>Operator</value>
  </data>
  <data name="ColumnThreshold" xml:space="preserve">
    <value>Threshold</value>
  </data>
  <data name="ColumnDeadband" xml:space="preserve">
    <value>Deadband</value>
  </data>
  <data name="ColumnMessage" xml:space="preserve">
    <value>Message</value>
  </data>
  <data name="TrendAlarmRules" xml:space="preserve">
    <value>Trend Alert Rules</value>
  </data>
  <data name="ColumnRateOfChange" xml:space="preserve">
    <value>Rate of Change</value>
  </data>
  <data name="ColumnTimeWindow" xml:space="preserve">
    <value>Time Window (sec)</value>
  </data>
  <data name="ColumnDirection" xml:space="preserve">
    <value>Direction</value>
  </data>
  
  <!-- ThresholdAlarmRuleEditor Strings -->
  <data name="LabelAlarmLevel" xml:space="preserve">
    <value>Alert Level:</value>
  </data>
  <data name="LabelComparisonOperator" xml:space="preserve">
    <value>Operator:</value>
  </data>
  <data name="LabelThreshold" xml:space="preserve">
    <value>Threshold:</value>
  </data>
  <data name="LabelDeadband" xml:space="preserve">
    <value>Deadband:</value>
  </data>
  <data name="LabelAlarmMessage" xml:space="preserve">
    <value>Message:</value>
  </data>
  <data name="OperatorGreaterThan" xml:space="preserve">
    <value>Greater Than (&gt;)</value>
  </data>
  <data name="OperatorGreaterEqual" xml:space="preserve">
    <value>Greater or Equal (&gt;=)</value>
  </data>
  <data name="OperatorLessThan" xml:space="preserve">
    <value>Less Than (&lt;)</value>
  </data>
  <data name="OperatorLessEqual" xml:space="preserve">
    <value>Less or Equal (&lt;=)</value>
  </data>
  <data name="OperatorEqual" xml:space="preserve">
    <value>Equal (=)</value>
  </data>
  <data name="OperatorNotEqual" xml:space="preserve">
    <value>Not Equal (&lt;&gt;)</value>
  </data>
  
  <!-- TrendAlarmRuleEditor Strings -->
  <data name="LabelRateOfChange" xml:space="preserve">
    <value>Rate of Change:</value>
  </data>
  <data name="LabelTimeWindow" xml:space="preserve">
    <value>Time Window (sec):</value>
  </data>
  <data name="LabelTrendDirection" xml:space="preserve">
    <value>Trend Direction:</value>
  </data>
  <data name="DirectionRising" xml:space="preserve">
    <value>Rising</value>
  </data>
  <data name="DirectionFalling" xml:space="preserve">
    <value>Falling</value>
  </data>
  <data name="DirectionBoth" xml:space="preserve">
    <value>Bidirectional</value>
  </data>
  
  <!-- UnitTypeListSettingWindow Strings -->
  <data name="UnitTypeListSettingTitle" xml:space="preserve">
    <value>Unit Type List Settings</value>
  </data>
  <data name="UnitTypeListSettingSystemTemplate" xml:space="preserve">
    <value>Unit Type List Settings - System Template</value>
  </data>
  <data name="UnitTypeListSettingDescription" xml:space="preserve">
    <value>Manage unit type list in the system, support add, delete and modify operations</value>
  </data>
  <data name="UnitTypeListSettingIndex" xml:space="preserve">
    <value>Index</value>
  </data>
  <data name="UnitTypeListSettingName" xml:space="preserve">
    <value>Unit Type Name</value>
  </data>
  <data name="UnitTypeListSettingSaveAndExit" xml:space="preserve">
    <value>Save and Exit</value>
  </data>
  <data name="ColumnIndex" xml:space="preserve">
    <value>Index</value>
  </data>
  <data name="ColumnUnitType" xml:space="preserve">
    <value>Unit Type</value>
  </data>
  <data name="LabelIndex" xml:space="preserve">
    <value>Index:</value>
  </data>
  <data name="LabelUnitType" xml:space="preserve">
    <value>Unit Type:</value>
  </data>
  <data name="ButtonAddModify" xml:space="preserve">
    <value>Add/Modify</value>
  </data>
  <data name="ButtonDelete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="ButtonSaveAndExit" xml:space="preserve">
    <value>Save and Exit</value>
  </data>
  <data name="MessageUnitTypeNameRequired" xml:space="preserve">
    <value>Unit type name cannot be empty!</value>
  </data>
  <data name="MessageSelectItemToDelete" xml:space="preserve">
    <value>Please select an item to delete first!</value>
  </data>
  <data name="MessageConfirmDelete" xml:space="preserve">
    <value>Are you sure you want to delete the selected item?</value>
  </data>
  <data name="MessageConfirmDeleteUnitType" xml:space="preserve">
    <value>Are you sure you want to delete the selected unit type?</value>
  </data>
  <data name="MessageUnitTypeAlreadyExists" xml:space="preserve">
    <value>Unit type already exists, please use a different name</value>
  </data>
  <data name="MessageUnitTypeListSaved" xml:space="preserve">
    <value>Unit type list has been saved</value>
  </data>
  <data name="MessagePrompt" xml:space="preserve">
    <value>Prompt</value>
  </data>
  
  <!-- UserLoginWindow Strings -->
  <data name="UserLoginTitle" xml:space="preserve">
    <value>User Login</value>
  </data>
  <data name="LabelUsername" xml:space="preserve">
    <value>Username:</value>
  </data>
  <data name="LabelPassword" xml:space="preserve">
    <value>Password:</value>
  </data>
  
  <!-- SensorListWindow Strings -->
  <data name="SensorListTitle" xml:space="preserve">
    <value>Sensor List</value>
  </data>
  <data name="ButtonAddModule" xml:space="preserve">
    <value>Add Module</value>
  </data>
  <data name="ButtonEditModule" xml:space="preserve">
    <value>Edit Module</value>
  </data>
  <data name="ButtonDeleteSensor" xml:space="preserve">
    <value>Delete Sensor</value>
  </data>
  <data name="ButtonViewDeviceList" xml:space="preserve">
    <value>View Device List</value>
  </data>
  <data name="ButtonViewDeviceStatus" xml:space="preserve">
    <value>View Device Status</value>
  </data>
  <data name="ColumnModuleSN" xml:space="preserve">
    <value>Module SN</value>
  </data>
  <data name="ColumnSensorAlias" xml:space="preserve">
    <value>Sensor Alias</value>
  </data>
  <data name="ColumnType" xml:space="preserve">
    <value>Type</value>
  </data>
  <data name="ColumnIsEnabled" xml:space="preserve">
    <value>Enabled</value>
  </data>
  <data name="ColumnMeasureUnit" xml:space="preserve">
    <value>Unit</value>
  </data>
  <data name="ColumnRangeMin" xml:space="preserve">
    <value>Range Min</value>
  </data>
  <data name="ColumnRangeMax" xml:space="preserve">
    <value>Range Max</value>
  </data>
  <data name="ColumnCalculationMethod" xml:space="preserve">
    <value>Calculation</value>
  </data>
  <data name="ColumnDefaultColor" xml:space="preserve">
    <value>Default Color</value>
  </data>
  
  <!-- WellStationSettingWindow Strings -->
  <data name="WellStationSettingTitle" xml:space="preserve">
    <value>Wellsite Settings</value>
  </data>
  <data name="LabelWellStationID" xml:space="preserve">
    <value>Wellsite ID:</value>
  </data>
  <data name="LabelCreateTime" xml:space="preserve">
    <value>Create Time:</value>
  </data>
  <data name="LabelWellStationName" xml:space="preserve">
    <value>Wellsite Name:</value>
  </data>
  <data name="LabelSamplingTime" xml:space="preserve">
    <value>Sampling Time:</value>
  </data>
  <data name="LabelDefaultIs" xml:space="preserve">
    <value>(Default is</value>
  </data>
  <data name="LabelCloseParenthesis" xml:space="preserve">
    <value>)</value>
  </data>
  <data name="LabelCollectIntervalNote" xml:space="preserve">
    <value>If you modify the collection time frequency, you need to restart the software and service for it to take effect.</value>
  </data>
  
  <!-- DataMonitorWindow Strings -->
  <data name="DataMonitorTitle" xml:space="preserve">
    <value>Data Monitor</value>
  </data>
  <data name="LabelStartTime" xml:space="preserve">
    <value>Start Time:</value>
  </data>
  <data name="LabelEndTime" xml:space="preserve">
    <value>End Time:</value>
  </data>
  <data name="ButtonShow" xml:space="preserve">
    <value>Show</value>
  </data>
  <data name="MenuDisplaySensorInChart" xml:space="preserve">
    <value>Display Sensor in Chart</value>
  </data>
  <data name="MenuRemoveFromChart" xml:space="preserve">
    <value>Remove from Chart</value>
  </data>
  <data name="MenuSetColor" xml:space="preserve">
    <value>Set Color</value>
  </data>
  <data name="MenuMoveUp" xml:space="preserve">
    <value>Move Up</value>
  </data>
  <data name="MenuMoveDown" xml:space="preserve">
    <value>Move Down</value>
  </data>
  <data name="MenuSensorSettings" xml:space="preserve">
    <value>Sensor Settings</value>
  </data>
  <data name="MenuModifyLastValue" xml:space="preserve">
    <value>Modify Last Value</value>
  </data>
  <data name="MenuHideSensor" xml:space="preserve">
    <value>Hide Sensor</value>
  </data>
  <data name="MenuShowSensor" xml:space="preserve">
    <value>Show Sensor</value>
  </data>
  <data name="MenuShowAllHiddenSensors" xml:space="preserve">
    <value>Show (Hidden Sensors)</value>
  </data>
  <data name="MenuHideAllHiddenSensors" xml:space="preserve">
    <value>Hide (Hidden Sensors)</value>
  </data>
  <data name="MenuIndependentWindow" xml:space="preserve">
    <value>Independent Window</value>
  </data>
  <data name="ColumnStatus" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="ColumnCurrentValue" xml:space="preserve">
    <value>Current Value</value>
  </data>
  
  <!-- UserListWindow Strings -->
  <data name="UserManagementTitle" xml:space="preserve">
    <value>User Management</value>
  </data>
  <data name="ButtonAdd" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="ColumnUsername" xml:space="preserve">
    <value>Username</value>
  </data>
  <data name="ColumnRankName" xml:space="preserve">
    <value>Permission Name</value>
  </data>
  
  <!-- NewWellStationWindow Strings -->
  <data name="NewWellStationTitle" xml:space="preserve">
    <value>New Wellsite</value>
  </data>
  
  <!-- SensorDetailSettingWindow Strings -->
  <data name="SensorSettingTitle" xml:space="preserve">
    <value>Sensor Settings</value>
  </data>
  <data name="LabelProperties" xml:space="preserve">
    <value>Properties</value>
  </data>
  <data name="LabelCheckConnection" xml:space="preserve">
    <value>Check field connection status, then configure sensor settings.</value>
  </data>
  <data name="LabelSamplingTime" xml:space="preserve">
    <value>Sampling Time:</value>
  </data>
  <data name="LabelSamplingTimeNote" xml:space="preserve">
    <value>Default 0 uses wellsite sampling rate. (Use multiples of 10)</value>
  </data>
  <data name="LabelSensorNaming" xml:space="preserve">
    <value>Sensor Naming</value>
  </data>
  <data name="LabelDataType" xml:space="preserve">
    <value>Data Type:</value>
  </data>
  <data name="LabelHighFrequencyDevice" xml:space="preserve">
    <value>High Frequency Data Device</value>
  </data>
  <data name="LabelModuleSN" xml:space="preserve">
    <value>Module SN:</value>
  </data>
  <data name="LabelModuleSNNote" xml:space="preserve">
    <value>Hardware number on analog-to-digital converter</value>
  </data>
  <data name="LabelSensorType" xml:space="preserve">
    <value>Sensor Type:</value>
  </data>
  <data name="LabelAccumulateLiquid" xml:space="preserve">
    <value>Accumulate liquid volume on this sensor</value>
  </data>
  <data name="LabelBy" xml:space="preserve">
    <value>By</value>
  </data>
  <data name="LabelAccumulate" xml:space="preserve">
    <value>Accumulate</value>
  </data>
  <data name="LabelSensorRange" xml:space="preserve">
    <value>Sensor Range:</value>
  </data>
  <data name="LabelTo" xml:space="preserve">
    <value>to</value>
  </data>
  <data name="LabelADCRange" xml:space="preserve">
    <value>ADC Output Module Range:</value>
  </data>
  <data name="LabelMeasureUnit" xml:space="preserve">
    <value>Measure Unit:</value>
  </data>
  <data name="LabelUserGroup" xml:space="preserve">
    <value>User Group:</value>
  </data>
  <data name="LabelValidThreshold" xml:space="preserve">
    <value>Valid Threshold:</value>
  </data>
  <data name="LabelEnableValidThreshold" xml:space="preserve">
    <value>Enable Valid Threshold</value>
  </data>
  <data name="LabelSerialNumber" xml:space="preserve">
    <value>Serial Number:</value>
  </data>
  <data name="LabelSensorAdditionalProperties" xml:space="preserve">
    <value>Sensor Additional Properties:</value>
  </data>
  <data name="LabelID" xml:space="preserve">
    <value>ID:</value>
  </data>
  <data name="LabelEnableModule" xml:space="preserve">
    <value>Enable Module</value>
  </data>
  <data name="LabelHideSensor" xml:space="preserve">
    <value>Hide Sensor</value>
  </data>
  <data name="LabelStoreProductionParams" xml:space="preserve">
    <value>Store Production Middle Parameters</value>
  </data>
  <data name="LabelEnableModuleNote" xml:space="preserve">
    <value>Note: Only after enabling module can data be collected and saved.</value>
  </data>
  <data name="LabelIcon" xml:space="preserve">
    <value>Icon:</value>
  </data>
  <data name="LabelDecimalPlaces" xml:space="preserve">
    <value>Decimal Places:</value>
  </data>
  <data name="LabelDefaultLineColor" xml:space="preserve">
    <value>Default Line Color:</value>
  </data>
  <data name="LabelMultiValueIndex" xml:space="preserve">
    <value>Multi-Value Index:</value>
  </data>
  <data name="LabelSensorCalibration" xml:space="preserve">
    <value>Sensor Calibration</value>
  </data>
  <data name="LabelReading1" xml:space="preserve">
    <value>Reading 1:</value>
  </data>
  <data name="LabelReading2" xml:space="preserve">
    <value>Reading 2:</value>
  </data>
  <data name="LabelReading3" xml:space="preserve">
    <value>Reading 3:</value>
  </data>
  <data name="LabelReading4" xml:space="preserve">
    <value>Reading 4:</value>
  </data>
  <data name="LabelReading5" xml:space="preserve">
    <value>Reading 5:</value>
  </data>
  <data name="LabelReading6" xml:space="preserve">
    <value>Reading 6:</value>
  </data>
  <data name="LabelReading7" xml:space="preserve">
    <value>Reading 7:</value>
  </data>
  <data name="LabelActualValue" xml:space="preserve">
    <value>Actual Value:</value>
  </data>
  <data name="ButtonRead" xml:space="preserve">
    <value>Read</value>
  </data>
  <data name="LabelCalibrationNote" xml:space="preserve">
    <value>The calibration process involves testing various points of this sensor according to the on-site standard inspection platform. The front shows the current reading of this sensor on the calibration platform, and the back shows the actual calibration reading.</value>
  </data>
  <data name="LabelFormulaNote" xml:space="preserve">
    <value>In the calculation formula, "Input" represents the sensor reading and can directly use the names of other sensors.</value>
  </data>
  <data name="LabelCommonFormulas" xml:space="preserve">
    <value>Common Formulas:</value>
  </data>
  <data name="LabelFormulaEditor" xml:space="preserve">
    <value>Calculation Formula Editor (Note: Formula is case-sensitive):</value>
  </data>
  <data name="LabelFormulaAnalysis" xml:space="preserve">
    <value>Formula Analysis Status and Calculation Verification:</value>
  </data>
  <data name="MenuInsertFunction" xml:space="preserve">
    <value>Insert Function...</value>
  </data>
  <data name="MenuInsertSensorVariable" xml:space="preserve">
    <value>Insert Sensor Variable...</value>
  </data>
  <data name="MenuInsertInput" xml:space="preserve">
    <value>Insert Input (represents current sensor reading)</value>
  </data>
  <data name="MenuInsertEnvVar" xml:space="preserve">
    <value>Insert Environment Variable...</value>
  </data>
  <data name="ButtonPrevious" xml:space="preserve">
    <value>Previous</value>
  </data>
  <data name="ButtonNext" xml:space="preserve">
    <value>Next</value>
  </data>
  <data name="ButtonComplete" xml:space="preserve">
    <value>Complete</value>
  </data>
  
  <!-- SysLogWindow Strings -->
  <data name="SysLogTitle" xml:space="preserve">
    <value>Operation Log</value>
  </data>
  <data name="ButtonSelectRange" xml:space="preserve">
    <value>Select Range</value>
  </data>
  <data name="ColumnLogList" xml:space="preserve">
    <value>Log List</value>
  </data>
  <data name="ColumnTimeRange" xml:space="preserve">
    <value>Time Range</value>
  </data>
  <data name="ColumnTime" xml:space="preserve">
    <value>Time</value>
  </data>
  <data name="ColumnMessage" xml:space="preserve">
    <value>Message</value>
  </data>
  
  <!-- WellStationLogListWindow Strings -->
  <data name="WellStationLogTitle" xml:space="preserve">
    <value>Wellsite Log</value>
  </data>
  <data name="ColumnWellStationLog" xml:space="preserve">
    <value>Wellsite Log</value>
  </data>
  <data name="LabelLog" xml:space="preserve">
    <value>Log:</value>
  </data>
  <data name="ButtonAdd" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="ButtonModify" xml:space="preserve">
    <value>Modify</value>
  </data>
  <data name="ButtonCancelModify" xml:space="preserve">
    <value>Cancel Modify</value>
  </data>
  <data name="ButtonDelete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="ButtonExportToExcel" xml:space="preserve">
    <value>Export to Excel</value>
  </data>
  
  <!-- WorkingStateListWindow Strings -->
  <data name="WorkingStateModifyTitle" xml:space="preserve">
    <value>Working State Modify</value>
  </data>
  <data name="MenuAddWorkingState" xml:space="preserve">
    <value>Add Working State</value>
  </data>
  <data name="MenuInsertWorkingState" xml:space="preserve">
    <value>Insert Working State</value>
  </data>
  <data name="MenuDeleteWorkingState" xml:space="preserve">
    <value>Delete Working State</value>
  </data>
  <data name="ColumnWorkingStateProcess" xml:space="preserve">
    <value>Working State Process</value>
  </data>
  <data name="ColumnStartTime" xml:space="preserve">
    <value>Start Time</value>
  </data>
  <data name="ColumnEndTime" xml:space="preserve">
    <value>End Time</value>
  </data>
  <data name="LabelWorkingStateName" xml:space="preserve">
    <value>Working State Name:</value>
  </data>
  <data name="LabelStartTime" xml:space="preserve">
    <value>Start Time:</value>
  </data>
  <data name="LabelEndTime" xml:space="preserve">
    <value>End Time:</value>
  </data>
  <data name="ButtonSave" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="ButtonClearTime" xml:space="preserve">
    <value>Clear Time</value>
  </data>
  
  <!-- DataRemarkListWindow Strings -->
  <data name="DataRemarkQueryExportTitle" xml:space="preserve">
    <value>Data Remark Query and Export</value>
  </data>
  <data name="LabelKeyword" xml:space="preserve">
    <value>Keyword:</value>
  </data>
  <data name="LabelSearchHint" xml:space="preserve">
    <value>(Hint: Empty value queries all)</value>
  </data>
  <data name="ButtonSearch" xml:space="preserve">
    <value>Search</value>
  </data>
  <data name="ColumnSensor" xml:space="preserve">
    <value>Sensor</value>
  </data>
  <data name="ColumnRemarkInfo" xml:space="preserve">
    <value>Remark Info</value>
  </data>
  
  <!-- DataRemarkAddWindow Strings -->
  <data name="DataRemarkTitle" xml:space="preserve">
    <value>Data Remark</value>
  </data>
  <data name="LabelDataTime" xml:space="preserve">
    <value>Data Time:</value>
  </data>
  <data name="LabelTextDescription" xml:space="preserve">
    <value>Text Description</value>
  </data>
  <data name="ButtonConfirmTextRemark" xml:space="preserve">
    <value>Confirm (This remark is text remark)</value>
  </data>
  
  <!-- WebWindowSetting Strings -->
  <data name="WebWindowSettingTitle" xml:space="preserve">
    <value>Configure Web Window</value>
  </data>
  <data name="LabelWebsite" xml:space="preserve">
    <value>Website:</value>
  </data>
  <data name="ColumnWebsite" xml:space="preserve">
    <value>Website</value>
  </data>
  <data name="ButtonOpen" xml:space="preserve">
    <value>Open</value>
  </data>
  
  <!-- RedisConfigSettingWindow Strings -->
  <data name="RedisConfigSettingTitle" xml:space="preserve">
    <value>Redis Service Connection Settings</value>
  </data>
  <data name="LabelServerIP" xml:space="preserve">
    <value>Server IP Address:</value>
  </data>
  <data name="LabelPort" xml:space="preserve">
    <value>Port:</value>
  </data>
  <data name="LabelConnectionTimeout" xml:space="preserve">
    <value>Connection Timeout:</value>
  </data>
  <data name="LabelTimeoutUnit" xml:space="preserve">
    <value>(Unit: seconds; when value is 0, use default value.)</value>
  </data>
  <data name="LabelUsername" xml:space="preserve">
    <value>Username:</value>
  </data>
  <data name="LabelPassword" xml:space="preserve">
    <value>Password:</value>
  </data>
  <data name="LabelShowPassword" xml:space="preserve">
    <value>Show Password</value>
  </data>
  <data name="LabelRestartServiceHint" xml:space="preserve">
    <value>Note: Please restart service after modification!</value>
  </data>
  
  <!-- AboutUsWindow Strings -->
  <data name="AboutSystemTitle" xml:space="preserve">
    <value>About System</value>
  </data>
  <data name="LabelTechnicalSupport" xml:space="preserve">
    <value>Technical Support:</value>
  </data>
  <data name="LabelProductName" xml:space="preserve">
    <value>Product Name:</value>
  </data>
  <data name="LabelSoftwareVersion" xml:space="preserve">
    <value>Software Version:</value>
  </data>
  
  <!-- ExitAppWindow Strings -->
  <data name="ExitAppTitle" xml:space="preserve">
    <value>Prompt</value>
  </data>
  <data name="LabelConfirmExit" xml:space="preserve">
    <value>Are you sure you want to exit the software?</value>
  </data>
  
  <!-- UserEditWindow Strings -->
  <data name="UserEditTitle" xml:space="preserve">
    <value>Edit User</value>
  </data>
  <data name="LabelRealName" xml:space="preserve">
    <value>Real Name:</value>
  </data>
  <data name="LabelOptional" xml:space="preserve">
    <value>(Optional)</value>
  </data>
  
  <!-- UserCreateWindow Strings -->
  <data name="UserCreateTitle" xml:space="preserve">
    <value>Add New User</value>
  </data>
  <data name="LabelNewUsername" xml:space="preserve">
    <value>New Username:</value>
  </data>
  <data name="LabelUsernameHint" xml:space="preserve">
    <value>(Consists of numbers and letters, length range 3-30 characters)</value>
  </data>
  <data name="LabelPassword" xml:space="preserve">
    <value>Password:</value>
  </data>
  <data name="LabelPasswordHint" xml:space="preserve">
    <value>(Consists of numbers, letters, and underscores, length range 3-30 characters)</value>
  </data>
  <data name="LabelConfirmPassword" xml:space="preserve">
    <value>Confirm Password:</value>
  </data>
  <data name="LabelPermissionLevel" xml:space="preserve">
    <value>Permission Level:</value>
  </data>
  
  <!-- UserChangePasswordWindow Strings -->
  <data name="UserChangePasswordTitle" xml:space="preserve">
    <value>Change Password</value>
  </data>
  <data name="LabelOldPassword" xml:space="preserve">
    <value>Old Password:</value>
  </data>
  <data name="LabelNewPassword" xml:space="preserve">
    <value>New Password:</value>
  </data>
  <data name="LabelConfirmNewPassword" xml:space="preserve">
    <value>Confirm New Password:</value>
  </data>
  
  <!-- AlarmListWindow Strings -->
  <data name="AlarmListTitle" xml:space="preserve">
    <value>Alarm Settings List</value>
  </data>
  <data name="LabelDataType" xml:space="preserve">
    <value>Data Type:</value>
  </data>
  <data name="LabelDataName" xml:space="preserve">
    <value>Data Name:</value>
  </data>
  <data name="ButtonFilter" xml:space="preserve">
    <value>Filter</value>
  </data>
  <data name="ColumnIndex" xml:space="preserve">
    <value>Index</value>
  </data>
  <data name="ColumnName" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="ColumnLowAlarm" xml:space="preserve">
    <value>Low Alarm</value>
  </data>
  <data name="ColumnLowLowAlarm" xml:space="preserve">
    <value>Low-Low Alarm</value>
  </data>
  <data name="ColumnLowLowLowAlarm" xml:space="preserve">
    <value>Low-Low-Low Alarm</value>
  </data>
  <data name="ColumnHighAlarm" xml:space="preserve">
    <value>High Alarm</value>
  </data>
  <data name="ColumnHighHighAlarm" xml:space="preserve">
    <value>High-High Alarm</value>
  </data>
  <data name="ColumnHighHighHighAlarm" xml:space="preserve">
    <value>High-High-High Alarm</value>
  </data>
  <data name="ColumnEqualAlarm" xml:space="preserve">
    <value>Equal Alarm</value>
  </data>
  
  <!-- AlarmAddWindow Strings -->
  <data name="AlarmAddTitle" xml:space="preserve">
    <value>Alarm Settings</value>
  </data>
  <data name="LabelCurrentValue" xml:space="preserve">
    <value>Current Value:</value>
  </data>
  <data name="LabelAlarmContent" xml:space="preserve">
    <value>Alarm Content:</value>
  </data>
  
  <!-- TCPIPDeviceSettingWindow Strings -->
  <data name="TCPIPDeviceSettingTitle" xml:space="preserve">
    <value>Network Device Settings</value>
  </data>
  <data name="LabelConnectMode" xml:space="preserve">
    <value>Connect Mode</value>
  </data>
  <data name="LabelDeviceType" xml:space="preserve">
    <value>Device Type</value>
  </data>
  <data name="LabelWorkMode" xml:space="preserve">
    <value>Work Mode</value>
  </data>
  <data name="LabelLocalPort" xml:space="preserve">
    <value>Local Port</value>
  </data>
  <data name="LabelRemotePort" xml:space="preserve">
    <value>Remote Port</value>
  </data>
  <data name="LabelBaudRate" xml:space="preserve">
    <value>Baud Rate</value>
  </data>
  <data name="LabelPLCAddress" xml:space="preserve">
    <value>PLC Address</value>
  </data>
  <data name="LabelMODBUSPoint" xml:space="preserve">
    <value>MODBUS Point</value>
  </data>
  <data name="LabelGDBoxSelect" xml:space="preserve">
    <value>GD Box Select</value>
  </data>
  <data name="LabelGDBox1" xml:space="preserve">
    <value>Box 1</value>
  </data>
  <data name="LabelGDBox2" xml:space="preserve">
    <value>Box 2</value>
  </data>
  <data name="LabelGDBox3" xml:space="preserve">
    <value>Box 3</value>
  </data>
  <data name="LabelGDBox4" xml:space="preserve">
    <value>Box 4</value>
  </data>
  <data name="LabelGDBox5" xml:space="preserve">
    <value>Box 5</value>
  </data>
  <data name="LabelS7Point" xml:space="preserve">
    <value>S7 Point</value>
  </data>
  <data name="ButtonEdit" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="ButtonSaveAndExit" xml:space="preserve">
    <value>Save &amp; Exit</value>
  </data>
  <data name="ButtonCancelAndExit" xml:space="preserve">
    <value>Cancel &amp; Exit</value>
  </data>
  
  <!-- RegisterWindow Strings -->
  <data name="RegisterTitle" xml:space="preserve">
    <value>Software License</value>
  </data>
  <data name="LabelUnitName" xml:space="preserve">
    <value>Unit Name:</value>
  </data>
  <data name="LabelVerifyCode" xml:space="preserve">
    <value>Verify Code:</value>
  </data>
  <data name="LabelRegisterHint" xml:space="preserve">
    <value>Please submit the above information to the supplier. The supplier will return an authorization code. Enter the authorization code below to obtain authorization and start using the software.</value>
  </data>
  <data name="LabelAuthCode" xml:space="preserve">
    <value>Authorization Code:</value>
  </data>
  
  <!-- RankMenuWindow Strings -->
  <data name="RankMenuSettingTitle" xml:space="preserve">
    <value>Permission Menu Settings</value>
  </data>
  
  <!-- GetDeviceList Strings -->
  <data name="GetDeviceListTitle" xml:space="preserve">
    <value>Data Source List</value>
  </data>
  <data name="LabelID" xml:space="preserve">
    <value>ID:</value>
  </data>
  <data name="LabelSN" xml:space="preserve">
    <value>SN:</value>
  </data>
  <data name="LabelFixedValue" xml:space="preserve">
    <value>Fixed Value:</value>
  </data>
  <data name="ButtonManualAdd" xml:space="preserve">
    <value>Manual Add</value>
  </data>
  
  <!-- FloatInputWindow Strings -->
  <data name="FloatInputTitle" xml:space="preserve">
    <value>Input Float</value>
  </data>
  <data name="LabelInputFloat" xml:space="preserve">
    <value>Please input a float number:</value>
  </data>
  
  <!-- GasParameterSetting Strings -->
  <data name="GasParameterSettingTitle" xml:space="preserve">
    <value>Natural Gas Standard Flow Calculation Parameters</value>
  </data>
  <data name="LabelParameterSettings" xml:space="preserve">
    <value>Parameter Settings</value>
  </data>
  <data name="LabelParameterName" xml:space="preserve">
    <value>Parameter Name</value>
  </data>
  <data name="LabelParameterValue" xml:space="preserve">
    <value>Parameter Value</value>
  </data>
  <data name="LabelParameterUnit" xml:space="preserve">
    <value>Unit</value>
  </data>
  <data name="LabelParameterDescription" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="LabelParameterList" xml:space="preserve">
    <value>Parameter List</value>
  </data>
  <data name="ButtonAdd" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="ButtonClear" xml:space="preserve">
    <value>Clear</value>
  </data>
  
  <!-- ChemicalComponentSettingWindow Strings -->
  <data name="ChemicalComponentSettingTitle" xml:space="preserve">
    <value>Natural Gas Component Settings</value>
  </data>
  <data name="LabelComponentSettings" xml:space="preserve">
    <value>Component Settings</value>
  </data>
  <data name="LabelComponentName" xml:space="preserve">
    <value>Component Name</value>
  </data>
  <data name="LabelComponentValue" xml:space="preserve">
    <value>Component Value</value>
  </data>
  <data name="LabelComponentUnit" xml:space="preserve">
    <value>Unit</value>
  </data>
  <data name="LabelComponentList" xml:space="preserve">
    <value>Component List</value>
  </data>
  
  <!-- SettingWindow Strings -->
  <data name="SettingWindowTitle" xml:space="preserve">
    <value>Settings Window</value>
  </data>
  <data name="ButtonConfirm" xml:space="preserve">
    <value>Confirm</value>
  </data>
  
  <!-- WebWindow Strings -->
  <data name="WebWindowTitle" xml:space="preserve">
    <value>Web Window</value>
  </data>
  
  <!-- AIServiceSetting Strings -->
  <data name="AIServiceSettingTitle" xml:space="preserve">
    <value>AI Service Configuration</value>
  </data>
  <data name="LabelIPAddress" xml:space="preserve">
    <value>IP Address:</value>
  </data>
  <data name="LabelEnableService" xml:space="preserve">
    <value>Enable Service:</value>
  </data>
  <data name="LabelRestartHint" xml:space="preserve">
    <value>(Note: Manual restart required)</value>
  </data>
  
  <!-- SensorPickerWindow Strings -->
  <data name="SensorPickerTitle" xml:space="preserve">
    <value>Select Sensor</value>
  </data>
  <data name="ColumnSensorName" xml:space="preserve">
    <value>Sensor Name</value>
  </data>
  <data name="ColumnType" xml:space="preserve">
    <value>Type</value>
  </data>
  
  <!-- GasParameterSetting Additional Strings -->
  <data name="LabelCurrentProcess" xml:space="preserve">
    <value>Current Process:</value>
  </data>
  <data name="LabelProcessNumber" xml:space="preserve">
    <value>Process Number:</value>
  </data>
  <data name="LabelDensityRatio" xml:space="preserve">
    <value>Gas Density Ratio:</value>
  </data>
  <data name="LabelDensityRatioHint" xml:space="preserve">
    <value>(Gas SG) 0-0.75</value>
  </data>
  <data name="LabelAtmosphericPressure" xml:space="preserve">
    <value>Atmospheric Pressure:</value>
  </data>
  <data name="LabelCO2MoleFraction" xml:space="preserve">
    <value>CO2 Mole Fraction:</value>
  </data>
  <data name="LabelN2MoleFraction" xml:space="preserve">
    <value>N2 Mole Fraction:</value>
  </data>
  <data name="LabelStandardPipeDiameter" xml:space="preserve">
    <value>Standard Pipe Diameter:</value>
  </data>
  <data name="LabelStandardWellPlateDiameter" xml:space="preserve">
    <value>Standard Orifice Diameter:</value>
  </data>
  <data name="LabelPipeMaterial" xml:space="preserve">
    <value>Pipe Material:</value>
  </data>
  <data name="LabelOrificeMaterial" xml:space="preserve">
    <value>Orifice Material:</value>
  </data>
  <data name="LabelProcessList" xml:space="preserve">
    <value>Process List</value>
  </data>
  
  <!-- GetDeviceList Additional Strings -->
  <data name="LabelManualInput" xml:space="preserve">
    <value>Manual Input</value>
  </data>
  <data name="LabelAvailableDevices" xml:space="preserve">
    <value>Available Devices</value>
  </data>
  <data name="LabelSelectedDevices" xml:space="preserve">
    <value>Selected Devices</value>
  </data>
  
  <!-- RankMenuWindow Additional Strings -->
  <data name="LabelRankManagement" xml:space="preserve">
    <value>Rank Management</value>
  </data>
  <data name="LabelRankName" xml:space="preserve">
    <value>Rank Name:</value>
  </data>
  <data name="LabelMenuPermissions" xml:space="preserve">
    <value>Menu Permissions</value>
  </data>
  <data name="LabelPermission" xml:space="preserve">
    <value>Permission</value>
  </data>
  
  <!-- TCPIPDeviceSettingWindow Additional Strings -->
  <data name="LabelDeviceList" xml:space="preserve">
    <value>Device List</value>
  </data>
  <data name="LabelDeviceSettings" xml:space="preserve">
    <value>Device Settings</value>
  </data>
  <data name="LabelTCPParameters" xml:space="preserve">
    <value>TCP Connection Parameters</value>
  </data>
  <data name="LabelPort" xml:space="preserve">
    <value>Port</value>
  </data>
  <data name="TextYes" xml:space="preserve">
    <value>Yes</value>
  </data>
  <data name="TextNo" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="MessagePleaseSelectSensorToEdit" xml:space="preserve">
    <value>Please select a sensor to edit first!</value>
  </data>
  <data name="MessageNoItemSelectedToDelete" xml:space="preserve">
    <value>No item selected to delete!</value>
  </data>
  <data name="MessageConfirmDeleteSensor" xml:space="preserve">
    <value>Are you sure you want to delete sensor &lt;{0}&gt;?</value>
  </data>
  <data name="TitlePrompt" xml:space="preserve">
    <value>Prompt</value>
  </data>
  <data name="LogDeleteSensor" xml:space="preserve">
    <value>Delete sensor, Id:{0},{1}</value>
  </data>
  <data name="SensorDetailConfigTitle" xml:space="preserve">
    <value>Sensor Settings</value>
  </data>
  <data name="TextProperties" xml:space="preserve">
    <value> Properties</value>
  </data>
  <data name="LabelCheckConnectionAndSetup" xml:space="preserve">
    <value>Check field connections and configure sensors.</value>
  </data>
  <data name="LabelSamplingTime" xml:space="preserve">
    <value>Sampling Time:</value>
  </data>
  <data name="LabelSamplingTimeNote" xml:space="preserve">
    <value>Default 0 uses well site sampling rate. (Use multiples of 10)</value>
  </data>
  <data name="LabelSensorNaming" xml:space="preserve">
    <value>Sensor Naming</value>
  </data>
  <data name="LabelDataType" xml:space="preserve">
    <value>Data Type:</value>
  </data>
  <data name="CheckBoxHighFrequencyDevice" xml:space="preserve">
    <value>High Frequency Data Device</value>
  </data>
  <data name="LabelModuleNumber" xml:space="preserve">
    <value>Module Number:</value>
  </data>
  <data name="LabelHardwareNumberOnADC" xml:space="preserve">
    <value>Hardware number on ADC</value>
  </data>
  <data name="LabelSensorType" xml:space="preserve">
    <value>Sensor Type:</value>
  </data>
  <data name="CheckBoxAccumulateDisplacement" xml:space="preserve">
    <value>Accumulate displacement on this sensor</value>
  </data>
  <data name="LabelBy" xml:space="preserve">
    <value>By</value>
  </data>
  <data name="LabelAccumulate" xml:space="preserve">
    <value>Accumulate</value>
  </data>
  <data name="LabelSensorRange" xml:space="preserve">
    <value>Sensor Range:</value>
  </data>
  <data name="TextTo" xml:space="preserve">
    <value>to</value>
  </data>
  <data name="LabelADCOutputRange" xml:space="preserve">
    <value>ADC Output Module Range:</value>
  </data>
  <data name="LabelMeasureUnit" xml:space="preserve">
    <value>Measurement Unit:</value>
  </data>
  <data name="LabelUserGroup" xml:space="preserve">
    <value>User Group:</value>
  </data>
  <data name="LabelValidThreshold" xml:space="preserve">
    <value>Valid Threshold:</value>
  </data>
  <data name="CheckBoxEnableValidThreshold" xml:space="preserve">
    <value>Enable Valid Threshold</value>
  </data>
  <data name="LabelSerialNumber" xml:space="preserve">
    <value>Serial Number:</value>
  </data>
  <data name="LabelSensorAdditionalProperties" xml:space="preserve">
    <value>Sensor Additional Properties:</value>
  </data>
  <data name="CheckBoxEnableModule" xml:space="preserve">
    <value>Enable Module</value>
  </data>
  <data name="CheckBoxHideSensor" xml:space="preserve">
    <value>Hide Sensor</value>
  </data>
  <data name="CheckBoxStoreProductionParams" xml:space="preserve">
    <value>Store Production Parameters</value>
  </data>
  <data name="LabelModuleEnableNote" xml:space="preserve">
    <value>Note: Data can only be collected and saved after enabling the module.</value>
  </data>
  <data name="LabelIcon" xml:space="preserve">
    <value>Icon:</value>
  </data>
  <data name="LabelDecimalPrecision" xml:space="preserve">
    <value>Decimal Precision:</value>
  </data>
  <data name="LabelDefaultLineColor" xml:space="preserve">
    <value>Default Line Color:</value>
  </data>
  <data name="LabelMultiValueIndex" xml:space="preserve">
    <value>Multi-Value Index:</value>
  </data>
  <data name="LabelSensorCalibration" xml:space="preserve">
    <value>Sensor Calibration</value>
  </data>
  <data name="LabelReading1" xml:space="preserve">
    <value>Reading 1:</value>
  </data>
  <data name="ButtonRead" xml:space="preserve">
    <value>Read</value>
  </data>
  <data name="LabelActualValue" xml:space="preserve">
    <value>Actual Value:</value>
  </data>
  <data name="LabelFormulaInputNote" xml:space="preserve">
    <value>In calculation formulas, "Input" represents sensor readings, and other sensor names can be used directly.</value>
  </data>
  <data name="LabelCommonFormulas" xml:space="preserve">
    <value>Common Formulas:</value>
  </data>
  <data name="LabelFormulaEditArea" xml:space="preserve">
    <value>Formula Edit Area (Note: Formulas are case-sensitive):</value>
  </data>
  <data name="MenuInsertFunction" xml:space="preserve">
    <value>Insert Function...</value>
  </data>
  <data name="MenuInsertSensorVariable" xml:space="preserve">
    <value>Insert Sensor Variable...</value>
  </data>
  <data name="MenuInsertInput" xml:space="preserve">
    <value>Insert Input (represents current sensor reading)</value>
  </data>
  <data name="MenuInsertEnvironmentVariable" xml:space="preserve">
    <value>Insert Environment Variable...</value>
  </data>
  <data name="LabelFormulaAnalysisStatus" xml:space="preserve">
    <value>Formula Analysis Status and Calculation Verification:</value>
  </data>
  <data name="ButtonPrevStep" xml:space="preserve">
    <value>Previous</value>
  </data>
  <data name="ButtonNextStep" xml:space="preserve">
    <value>Next</value>
  </data>
  <data name="ButtonComplete" xml:space="preserve">
    <value>Complete</value>
  </data>
  <data name="ButtonCancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="TextCorrect" xml:space="preserve">
    <value>Correct</value>
  </data>
  <data name="TextPleaseSelect" xml:space="preserve">
    <value>--Select--</value>
  </data>
  <data name="LabelSetupSN" xml:space="preserve">
    <value>Setup SN:</value>
  </data>
  <data name="LabelSetupVirtualSensor" xml:space="preserve">
    <value>Setup Virtual Sensor:</value>
  </data>
  <data name="MessageSensorNameCannotDuplicate" xml:space="preserve">
    <value>Sensor name cannot be duplicated</value>
  </data>
  <data name="MessageValidThresholdCannotBeEmpty" xml:space="preserve">
    <value>Valid threshold cannot be empty!</value>
  </data>
  <data name="MessageValidThresholdFormatIncorrect" xml:space="preserve">
    <value>Valid threshold format is incorrect!</value>
  </data>
  <data name="MessageFormulaInvalidPleaseModify" xml:space="preserve">
    <value>Formula is invalid, please modify!</value>
  </data>
  <data name="MessageSoftwareNotRegistered" xml:space="preserve">
    <value>Software not registered, please register first!</value>
  </data>
  <data name="LogAddSensor" xml:space="preserve">
    <value>Add sensor, Id:{0},{1}</value>
  </data>
  <data name="LogModifySensor" xml:space="preserve">
    <value>Modify sensor, Id:{0},{1}</value>
  </data>
  <data name="TextVirtualSensor" xml:space="preserve">
    <value>Virtual Sensor</value>
  </data>
  <data name="MeasureUnitSettingTitle" xml:space="preserve">
    <value>Measurement Unit Settings</value>
  </data>
  <data name="LabelMeasureUnitAdvancedWarning" xml:space="preserve">
    <value>Measurement units are advanced settings, please do not modify them easily</value>
  </data>
  <data name="ColumnSymbol" xml:space="preserve">
    <value>Symbol</value>
  </data>
  <data name="ColumnExchangeRelationship" xml:space="preserve">
    <value>Exchange Relationship</value>
  </data>
  <data name="ColumnDefaultRange" xml:space="preserve">
    <value>Default Range</value>
  </data>
  <data name="ColumnDefaultDisplayRange" xml:space="preserve">
    <value>Default Display Range</value>
  </data>
  <data name="ColumnDefaultColor" xml:space="preserve">
    <value>Default Color</value>
  </data>
  <data name="LabelUnitSymbol" xml:space="preserve">
    <value>Unit Symbol: e.g. ℃</value>
  </data>
  <data name="LabelUnitCategory" xml:space="preserve">
    <value>Unit Category: e.g. Temperature, Pressure</value>
  </data>
  <data name="LabelExchangeRelationship" xml:space="preserve">
    <value>Exchange Relationship:</value>
  </data>
  <data name="LabelBaseUnit" xml:space="preserve">
    <value>Base Unit:</value>
  </data>
  <data name="LabelExchangeMethod" xml:space="preserve">
    <value>Exchange Method:</value>
  </data>
  <data name="LabelDefaultRange" xml:space="preserve">
    <value>Default Range:</value>
  </data>
  <data name="LabelDefaultDisplayRange" xml:space="preserve">
    <value>Default Display Range:</value>
  </data>
  <data name="LabelDefaultColor" xml:space="preserve">
    <value>Default Color:</value>
  </data>
  <data name="LabelDefaultIcon" xml:space="preserve">
    <value>Default Icon:</value>
  </data>
  <data name="LabelCoefficient" xml:space="preserve">
    <value>Coefficient:</value>
  </data>
  <data name="LabelCalculationFormula" xml:space="preserve">
    <value>Calculation Formula:</value>
  </data>
  <data name="TextSystemTemplate" xml:space="preserve">
    <value>System Template</value>
  </data>
  <data name="MessageConfirmDeleteUnitSymbol" xml:space="preserve">
    <value>Are you sure you want to delete this unit symbol?</value>
  </data>
  <data name="LogDeleteMeasureUnit" xml:space="preserve">
    <value>Delete measurement unit &lt;{0}&gt;</value>
  </data>
  <data name="TextAdd" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="TextModify" xml:space="preserve">
    <value>Modify</value>
  </data>
  <data name="LogAddMeasureUnit" xml:space="preserve">
    <value>Add measurement unit &lt;{0}&gt;</value>
  </data>
  <data name="LogModifyMeasureUnit" xml:space="preserve">
    <value>Modify measurement unit &lt;{0}&gt;</value>
  </data>
  <data name="TCPIPDeviceSettingTitle" xml:space="preserve">
    <value>Network Device Settings</value>
  </data>
  <data name="LabelConnectionType" xml:space="preserve">
    <value>Connection Type</value>
  </data>
  <data name="LabelDeviceType" xml:space="preserve">
    <value>Device Type</value>
  </data>
  <data name="LabelWorkMode" xml:space="preserve">
    <value>Work Mode</value>
  </data>
  <data name="LabelIP" xml:space="preserve">
    <value>IP</value>
  </data>
  <data name="LabelLocalPort" xml:space="preserve">
    <value>Local Port</value>
  </data>
  <data name="LabelRemotePort" xml:space="preserve">
    <value>Remote Port</value>
  </data>
  <data name="LabelBaudRate" xml:space="preserve">
    <value>Baud Rate</value>
  </data>
  <data name="LabelPLCAddress" xml:space="preserve">
    <value>PLC Address:</value>
  </data>
  <data name="LabelMODBUSPointTable" xml:space="preserve">
    <value>MODBUS Point Table:</value>
  </data>
  <data name="LabelCementingBoxSelection" xml:space="preserve">
    <value>Cementing Box Selection:</value>
  </data>
  <data name="LabelBox1" xml:space="preserve">
    <value>Box 1</value>
  </data>
  <data name="LabelBox2" xml:space="preserve">
    <value>Box 2</value>
  </data>
  <data name="LabelBox3" xml:space="preserve">
    <value>Box 3</value>
  </data>
  <data name="LabelBox4" xml:space="preserve">
    <value>Box 4</value>
  </data>
  <data name="LabelBox5" xml:space="preserve">
    <value>Box 5</value>
  </data>
  <data name="LabelS7PointTable" xml:space="preserve">
    <value>S7 Point Table:</value>
  </data>
  <data name="ButtonEdit" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="ButtonSaveAndExit" xml:space="preserve">
    <value>Save &amp; Exit</value>
  </data>
  <data name="ButtonCancelAndExit" xml:space="preserve">
    <value>Cancel &amp; Exit</value>
  </data>
  <data name="ColumnConnectionType" xml:space="preserve">
    <value>Connection Type</value>
  </data>
  <data name="ColumnDeviceType" xml:space="preserve">
    <value>Device Type</value>
  </data>
  <data name="DevConnectModeTCPIP" xml:space="preserve">
    <value>TCP/IP Connection</value>
  </data>
  <data name="DevConnectModeCOM" xml:space="preserve">
    <value>COM Serial Connection</value>
  </data>
  <data name="DevConnectModePLCS7" xml:space="preserve">
    <value>PLC S7 Connection</value>
  </data>
  <data name="DevTypeInterfaceBox" xml:space="preserve">
    <value>Interface Box</value>
  </data>
  <data name="DevTypeMODBUSPointTable" xml:space="preserve">
    <value>MODBUS Point Table</value>
  </data>
  <data name="DevTypeCementingBox" xml:space="preserve">
    <value>Cementing Box</value>
  </data>
  <data name="DevTypeCementingAshTank" xml:space="preserve">
    <value>Cementing Ash Tank</value>
  </data>
  <data name="DevTypeSandNoise" xml:space="preserve">
    <value>Sand Noise</value>
  </data>
  <data name="DevTypeMODBUSOutput" xml:space="preserve">
    <value>MODBUS Output</value>
  </data>
  <data name="DevTypePLCS7PointTable" xml:space="preserve">
    <value>PLC S7 Point Table</value>
  </data>
  <data name="DevTypeWirelessSensor" xml:space="preserve">
    <value>Wireless Sensor</value>
  </data>
  <data name="TextUnknownConnectionType" xml:space="preserve">
    <value>Unknown Connection Type</value>
  </data>
  <data name="TextUnknownDeviceType" xml:space="preserve">
    <value>Unknown Device Type</value>
  </data>
  <data name="TextUnknown" xml:space="preserve">
    <value>Unknown</value>
  </data>
  <data name="MessagePleaseSelectMODBUSPointTable" xml:space="preserve">
    <value>Please select MODBUS point table</value>
  </data>
  <data name="MessagePleaseSelectS7PointTable" xml:space="preserve">
    <value>Please select S7 point table</value>
  </data>
  <data name="ModBusDataSettingTitle" xml:space="preserve">
    <value>ModBus Data Settings</value>
  </data>
  <data name="MessagePleaseSelectSensor" xml:space="preserve">
    <value>Please select a sensor first.</value>
  </data>
  <data name="GeneralModBusDeviceSettingTitle" xml:space="preserve">
    <value>General ModBus Device Settings</value>
  </data>
  <data name="MenuAddDevice" xml:space="preserve">
    <value>Add Device</value>
  </data>
  <data name="MenuCopyDevice" xml:space="preserve">
    <value>Copy Device</value>
  </data>
  <data name="MenuDeleteDevice" xml:space="preserve">
    <value>Delete Device</value>
  </data>
  <data name="LabelDeviceName" xml:space="preserve">
    <value>Device Name:</value>
  </data>
  <data name="LabelDeviceSN" xml:space="preserve">
    <value>Device SN:</value>
  </data>
  <data name="LabelRTU" xml:space="preserve">
    <value>RTU</value>
  </data>
  <data name="LabelDataItem" xml:space="preserve">
    <value>Data Item</value>
  </data>
  <data name="LabelUnit" xml:space="preserve">
    <value>Unit</value>
  </data>
  <data name="LabelDeviceID" xml:space="preserve">
    <value>Device ID</value>
  </data>
  <data name="LabelAddress" xml:space="preserve">
    <value>Address</value>
  </data>
  <data name="LabelOffset" xml:space="preserve">
    <value>Offset</value>
  </data>
  <data name="LabelFunctionCode" xml:space="preserve">
    <value>Function Code</value>
  </data>
  <data name="LabelDisplayType" xml:space="preserve">
    <value>Display Type:</value>
  </data>
  <data name="LabelDisplayLevel" xml:space="preserve">
    <value>Display Level:</value>
  </data>
  <data name="LabelDataFormat" xml:space="preserve">
    <value>Data Format:</value>
  </data>
  <data name="LabelDataBits" xml:space="preserve">
    <value>Data Bits</value>
  </data>
  <data name="LabelGroupNumber" xml:space="preserve">
    <value>Group Number</value>
  </data>
  <data name="LabelInterlockingRelationship" xml:space="preserve">
    <value>Interlocking Relationship</value>
  </data>
  <data name="ButtonModify" xml:space="preserve">
    <value>Modify</value>
  </data>
  <data name="ButtonImportExcelPointTable" xml:space="preserve">
    <value>Import Excel Point Table</value>
  </data>
  <data name="MenuAddDataItem" xml:space="preserve">
    <value>Add Data Item</value>
  </data>
  <data name="MenuDeleteDataItem" xml:space="preserve">
    <value>Delete Data Item</value>
  </data>
  <data name="MenuInsertDataItem" xml:space="preserve">
    <value>Insert Data Item</value>
  </data>
  <data name="MenuCopyDataItem" xml:space="preserve">
    <value>Copy Data Item</value>
  </data>
  <data name="MenuPasteDataItem" xml:space="preserve">
    <value>Paste Data Item</value>
  </data>
  <data name="ColumnAddressHex" xml:space="preserve">
    <value>Address(Hex)</value>
  </data>
  <data name="ColumnAddress4X" xml:space="preserve">
    <value>Address(4X)</value>
  </data>
  <data name="TextNewDevice" xml:space="preserve">
    <value>New Device</value>
  </data>
  <data name="GasParameterSettingTitle" xml:space="preserve">
    <value>US Standard Natural Gas Production Calculation Parameter Settings</value>
  </data>
  <data name="LabelCurrentProcess" xml:space="preserve">
    <value>Current Process:</value>
  </data>
  <data name="LabelProcessNumber" xml:space="preserve">
    <value>Process Number:</value>
  </data>
  <data name="LabelGasSpecificGravity" xml:space="preserve">
    <value>Natural Gas to Dry Air Density Ratio:</value>
  </data>
  <data name="LabelGasSpecificGravityNote" xml:space="preserve">
    <value>(Gas Specific Gravity)</value>
  </data>
  <data name="LabelPipeDiameter" xml:space="preserve">
    <value>Pipe Diameter:</value>
  </data>
  <data name="LabelOrificeDiameter" xml:space="preserve">
    <value>Orifice Diameter:</value>
  </data>
  <data name="LabelDegFTgr" xml:space="preserve">
    <value>DegFTgr:</value>
  </data>
  <data name="LabelPsiaPgr" xml:space="preserve">
    <value>PsiaPgr:</value>
  </data>
  <data name="LabelDegFTb" xml:space="preserve">
    <value>DegFTb:</value>
  </data>
  <data name="LabelPsiaPB" xml:space="preserve">
    <value>PsiaPB:</value>
  </data>
  <data name="LabelAtmosphericPressure" xml:space="preserve">
    <value>AtmosphericPressure:</value>
  </data>
  <data name="LabelInsentropicExponent" xml:space="preserve">
    <value>InsentropicExponent:</value>
  </data>
  <data name="LabelCompressibilityOfAirAtStdCond" xml:space="preserve">
    <value>CompressibilityOfAirAtStdCond:</value>
  </data>
  <data name="LabelOrificePlateThermalExpansionCofe" xml:space="preserve">
    <value>OrificePlateThermalExpansionCofe:</value>
  </data>
  <data name="LabelMeterRunThermalExpansionCofe" xml:space="preserve">
    <value>MeterRunThermalExpansionCofe:</value>
  </data>
  <data name="LabelDynamicViscosity" xml:space="preserve">
    <value>DynamicViscosity:</value>
  </data>
  <data name="LabelProcessList" xml:space="preserve">
    <value>Process List</value>
  </data>
  <data name="ProcessNumberFormat" xml:space="preserve">
    <value>Process {0}</value>
  </data>
  <data name="MessageInvalidProcessNumber" xml:space="preserve">
    <value>Data Item: Process number is incorrect!</value>
  </data>
  <data name="MessageProcessNumberExists" xml:space="preserve">
    <value>Data Item: Process number already exists!</value>
  </data>
  <data name="MessageModifySuccess" xml:space="preserve">
    <value>Modification successful!</value>
  </data>
  <data name="MessageConfirmSaveChanges" xml:space="preserve">
    <value>Changes have been made but not saved. Do you want to confirm and save the changes?</value>
  </data>
  <data name="MessageNoChangesDetected" xml:space="preserve">
    <value>No changes detected. Please add or modify first!</value>
  </data>
  <data name="MessageSaveResult" xml:space="preserve">
    <value>Save {0}!</value>
  </data>
  <data name="TextSuccess" xml:space="preserve">
    <value>successful</value>
  </data>
  <data name="TextFailed" xml:space="preserve">
    <value>failed</value>
  </data>
  <data name="MessageConfirmDelete" xml:space="preserve">
    <value>Are you sure you want to delete {0}?</value>
  </data>
  <data name="S7SettingWindowTitle" xml:space="preserve">
    <value>S7 Point Table Settings</value>
  </data>
  <data name="LabelPLCDeviceList" xml:space="preserve">
    <value>PLC Device List</value>
  </data>
  <data name="LabelRack" xml:space="preserve">
    <value>Rack</value>
  </data>
  <data name="LabelSlot" xml:space="preserve">
    <value>Slot</value>
  </data>
  <data name="LabelSN" xml:space="preserve">
    <value>SN</value>
  </data>
  <data name="ButtonImportExcel" xml:space="preserve">
    <value>Import Excel</value>
  </data>
  <data name="ButtonSaveAndExit" xml:space="preserve">
    <value>Save &amp; Exit</value>
  </data>
  <data name="ButtonCancelAndExit" xml:space="preserve">
    <value>Cancel &amp; Exit</value>
  </data>
  <data name="LabelSequenceNumber" xml:space="preserve">
    <value>Sequence Number</value>
  </data>
  <data name="LabelStartAddress" xml:space="preserve">
    <value>Start Address</value>
  </data>
  <data name="LabelBit" xml:space="preserve">
    <value>Bit</value>
  </data>
  <data name="LabelDataBlock" xml:space="preserve">
    <value>Data Block</value>
  </data>
  <data name="LabelDBNumber" xml:space="preserve">
    <value>DB Number</value>
  </data>
  <data name="LabelOperationType" xml:space="preserve">
    <value>Operation Type</value>
  </data>
  <data name="LabelConversionRange" xml:space="preserve">
    <value>Conversion Range</value>
  </data>
  <data name="MessageDuplicateNameRenamed" xml:space="preserve">
    <value>Duplicate name detected. Renamed to ensure uniqueness. Original name: {0}   Changed to: {1}</value>
  </data>
  <data name="MessageUnknownDataGridClicked" xml:space="preserve">
    <value>Unknown DataGrid clicked</value>
  </data>
  <data name="MessagePleaseSelectPLCDevice" xml:space="preserve">
    <value>Please select a PLC device</value>
  </data>
  <data name="FilterExcelFiles" xml:space="preserve">
    <value>Excel Files (*.xlsx)|*.xlsx</value>
  </data>
  <data name="MessageDataFormatError" xml:space="preserve">
    <value>Error: Required field data format is incorrect, please check!</value>
  </data>
  <data name="MessageDataImportCompleted" xml:space="preserve">
    <value>Tip: Data import completed!</value>
  </data>
  <data name="MessageRowError" xml:space="preserve">
    <value>Error in row {0}: {1}</value>
  </data>
  <data name="InterlockConfigWindowTitle" xml:space="preserve">
    <value>Interlock Rule Data Settings</value>
  </data>
  <data name="LabelRuleGroupList" xml:space="preserve">
    <value>Rule Group List</value>
  </data>
  <data name="LabelRuleList" xml:space="preserve">
    <value>Rule List</value>
  </data>
  <data name="LabelIndex" xml:space="preserve">
    <value>Index</value>
  </data>
  <data name="LabelThreshold" xml:space="preserve">
    <value>Threshold</value>
  </data>
  <data name="LabelFormula" xml:space="preserve">
    <value>Formula</value>
  </data>
  <data name="LabelDeviceList" xml:space="preserve">
    <value>Device List</value>
  </data>
  <data name="MessagePleaseSelectRuleGroup" xml:space="preserve">
    <value>Please select a rule group</value>
  </data>
  <data name="TextNewRuleGroup" xml:space="preserve">
    <value>New Rule Group</value>
  </data>
  <data name="TextDeviceSN" xml:space="preserve">
    <value>Device SN</value>
  </data>
  <data name="TextFormula" xml:space="preserve">
    <value>Formula</value>
  </data>
  <data name="MessageRuleGroupNameRenamed" xml:space="preserve">
    <value>Rule group name is duplicated. Renamed to ensure uniqueness. Original name: [{0}] Changed to: [{1}]</value>
  </data>
  <data name="MonitorViewSettingWindowTitle" xml:space="preserve">
    <value>Monitor Window Property Settings</value>
  </data>
  <data name="LabelBasicSettings" xml:space="preserve">
    <value>Basic Settings</value>
  </data>
  <data name="LabelMonitorSchemeName" xml:space="preserve">
    <value>Monitor Scheme Name:</value>
  </data>
  <data name="LabelCurveWidth" xml:space="preserve">
    <value>Curve Width:</value>
  </data>
  <data name="LabelSmallGridCount" xml:space="preserve">
    <value>Small Grid Count:</value>
  </data>
  <data name="LabelBigGridCount" xml:space="preserve">
    <value>Big Grid Count:</value>
  </data>
  <data name="LabelYAxisInterval" xml:space="preserve">
    <value>Y-Axis Interval:</value>
  </data>
  <data name="LabelDisplaySettings" xml:space="preserve">
    <value>Display Settings</value>
  </data>
  <data name="LabelFontSize" xml:space="preserve">
    <value>Font Size:</value>
  </data>
  <data name="LabelBackgroundColor" xml:space="preserve">
    <value>Background Color:</value>
  </data>
  <data name="LabelTitleColor" xml:space="preserve">
    <value>Title Color:</value>
  </data>
  <data name="LabelTableLineColor" xml:space="preserve">
    <value>Table Line Color:</value>
  </data>
  <data name="LabelBigGridLineColor" xml:space="preserve">
    <value>Big Grid Line Color:</value>
  </data>
  <data name="LabelAdvancedSettings" xml:space="preserve">
    <value>Advanced Settings</value>
  </data>
  <data name="LabelDataSamplingInterval" xml:space="preserve">
    <value>Data Sampling Interval (seconds):</value>
  </data>
  <data name="TextUnknown" xml:space="preserve">
    <value>Unknown</value>
  </data>
  <data name="TextNewMonitorScheme" xml:space="preserve">
    <value>New Monitor Scheme</value>
  </data>
  <data name="MessageMonitorSchemeNameRequired" xml:space="preserve">
    <value>Please enter monitor scheme name</value>
  </data>
  <data name="MessageCurveWidthFormatError" xml:space="preserve">
    <value>Curve width format error</value>
  </data>
  <data name="MessageCurveWidthMinValue" xml:space="preserve">
    <value>Curve width cannot be less than 1</value>
  </data>
  <data name="MessageSmallGridCountFormatError" xml:space="preserve">
    <value>Small grid count format error</value>
  </data>
  <data name="MessageSmallGridCountMinValue" xml:space="preserve">
    <value>Small grid count cannot be less than {0}</value>
  </data>
  <data name="MessageBigGridCountFormatError" xml:space="preserve">
    <value>Big grid count format error</value>
  </data>
  <data name="MessageBigGridCountMinValue" xml:space="preserve">
    <value>Big grid count cannot be less than {0}</value>
  </data>
  <data name="MessageYAxisIntervalFormatError" xml:space="preserve">
    <value>Y-axis interval format error</value>
  </data>
  <data name="MessageYAxisIntervalMinValue" xml:space="preserve">
    <value>Y-axis interval cannot be less than {0}</value>
  </data>
  <data name="MessageFontSizeRequired" xml:space="preserve">
    <value>Please enter font size</value>
  </data>
  <data name="MessageFontSizeFormatError" xml:space="preserve">
    <value>Font size format error</value>
  </data>
  <data name="MessageFontSizeMinValue" xml:space="preserve">
    <value>Font size cannot be less than 1</value>
  </data>
  <data name="MessageSamplingIntervalRequired" xml:space="preserve">
    <value>Please enter data sampling interval</value>
  </data>
  <data name="MessageSamplingIntervalFormatError" xml:space="preserve">
    <value>Data sampling interval format error</value>
  </data>
  <data name="MessageSamplingIntervalMinValue" xml:space="preserve">
    <value>Data sampling interval cannot be less than 0</value>
  </data>
  <data name="EnvironmentVariableSettingWindowTitle" xml:space="preserve">
    <value>Well Station Environment Variable Settings</value>
  </data>
  <data name="LabelEnvironmentVariableName" xml:space="preserve">
    <value>Environment Variable Name</value>
  </data>
  <data name="LabelEnvironmentVariableValue" xml:space="preserve">
    <value>Environment Variable Value</value>
  </data>
  <data name="MessageEnvironmentVariableDescription" xml:space="preserve">
    <value>Environment variable values take effect in formulas that use these variables!</value>
  </data>
  <data name="MessagePleaseUnderstandBeforeModifying" xml:space="preserve">
    <value>Please understand carefully before modifying.</value>
  </data>
  <data name="MessageDoNotModifyNameUsually" xml:space="preserve">
    <value>Usually do not modify the name!</value>
  </data>
  <data name="ButtonAddOrModify" xml:space="preserve">
    <value>Add/Modify</value>
  </data>
  <data name="ButtonNationalStandardGasParameterSetting" xml:space="preserve">
    <value>National Standard Gas Parameter Settings</value>
  </data>
  <data name="LabelNationalStandard" xml:space="preserve">
    <value>Standard: GB/T 21446-2008</value>
  </data>
  <data name="ButtonUSAStandardGasParameterSetting" xml:space="preserve">
    <value>USA Standard Gas Parameter Settings</value>
  </data>
  <data name="LabelUSAStandard" xml:space="preserve">
    <value>Standard: API MPMS 14.2</value>
  </data>
  <data name="ButtonSandParameterSetting" xml:space="preserve">
    <value>Sand Parameter Settings</value>
  </data>
  <data name="MessageSaveWillTakeEffectImmediately" xml:space="preserve">
    <value>Note: Changes will take effect immediately after saving!</value>
  </data>
  <data name="TextSystemTemplate" xml:space="preserve">
    <value>System Template</value>
  </data>
  <data name="MessagePleaseSelectItemToDelete" xml:space="preserve">
    <value>Please select an item to delete first!</value>
  </data>
  <data name="MessageConfirmDeleteEnvironmentVariable" xml:space="preserve">
    <value>Are you sure you want to delete this environment variable?</value>
  </data>
  <data name="MessageConfirmSaveAndApply" xml:space="preserve">
    <value>Are you sure you want to save and apply?</value>
  </data>
  <data name="LogModifyEnvironmentVariableSettings" xml:space="preserve">
    <value>Modify Environment Variable Settings</value>
  </data>
  <data name="SandParameterSettingWindowTitle" xml:space="preserve">
    <value>Sand Parameter Settings</value>
  </data>
  <data name="LabelWorkflow" xml:space="preserve">
    <value>Workflow:</value>
  </data>
  <data name="LabelPipelineMedium" xml:space="preserve">
    <value>Pipeline Medium:</value>
  </data>
  <data name="TextAutomatic" xml:space="preserve">
    <value>Auto</value>
  </data>
  <data name="LabelGasCompressionCoefficient" xml:space="preserve">
    <value>Gas Compression Coefficient:</value>
  </data>
  <data name="LabelPipelineInnerDiameter" xml:space="preserve">
    <value>Pipeline Inner Diameter (mm):</value>
  </data>
  <data name="LabelBackgroundNoise" xml:space="preserve">
    <value>Background Noise:</value>
  </data>
  <data name="LabelStableRawTimeRange" xml:space="preserve">
    <value>Stable Raw Time Range:</value>
  </data>
  <data name="TextSeconds" xml:space="preserve">
    <value>seconds</value>
  </data>
  <data name="LabelNoiseIncreaseRange" xml:space="preserve">
    <value>Noise Increase Range:</value>
  </data>
  <data name="TextPercent" xml:space="preserve">
    <value>%</value>
  </data>
  <data name="MessagePipelineTemperaturePressureInstruction" xml:space="preserve">
    <value>Pipeline temperature and pressure should be introduced as parameters when editing formulas.</value>
  </data>
  <data name="MessageIntroductionMethod1" xml:space="preserve">
    <value>Method 1: Use values directly to represent fixed values</value>
  </data>
  <data name="MessageIntroductionMethod2" xml:space="preserve">
    <value>Method 2: Fill in sensor name to introduce sensor values</value>
  </data>
  <data name="SelectEnvironmentVariableWindowTitle" xml:space="preserve">
    <value>Select Environment Variable</value>
  </data>
  <data name="LabelKeyword" xml:space="preserve">
    <value>Keyword:</value>
  </data>
  <data name="ButtonSearch" xml:space="preserve">
    <value>Search</value>
  </data>
  <data name="LabelLogType" xml:space="preserve">
    <value>Log Type:</value>
  </data>
  <data name="LabelTimeRange" xml:space="preserve">
    <value>Time Range:</value>
  </data>
  <data name="TextTo" xml:space="preserve">
    <value>To</value>
  </data>
  <data name="TextToday" xml:space="preserve">
    <value>Today</value>
  </data>
  <data name="TextLastThreeDays" xml:space="preserve">
    <value>Last 3 Days</value>
  </data>
  <data name="TextLastWeek" xml:space="preserve">
    <value>Last Week</value>
  </data>
  <data name="TextLastMonth" xml:space="preserve">
    <value>Last Month</value>
  </data>
  <data name="TextCustom" xml:space="preserve">
    <value>Custom</value>
  </data>
  <data name="LabelTime" xml:space="preserve">
    <value>Time</value>
  </data>
  <data name="LabelType" xml:space="preserve">
    <value>Type</value>
  </data>
  <data name="LabelMessage" xml:space="preserve">
    <value>Message</value>
  </data>
  <data name="MenuCopy" xml:space="preserve">
    <value>Copy</value>
  </data>
  <data name="MenuExportToFile" xml:space="preserve">
    <value>Export to File...</value>
  </data>
  <data name="MessageErrorLoadingLogs" xml:space="preserve">
    <value>Error loading logs</value>
  </data>
  <data name="TextError" xml:space="preserve">
    <value>Error</value>
  </data>
  <data name="MessageErrorReadingLogFile" xml:space="preserve">
    <value>Error reading log file</value>
  </data>
  <data name="LabelLevel" xml:space="preserve">
    <value>Level</value>
  </data>
  <data name="LabelSource" xml:space="preserve">
    <value>Source</value>
  </data>
  <data name="FilterCSVAndTextFiles" xml:space="preserve">
    <value>CSV Files (*.csv)|*.csv|Text Files (*.txt)|*.txt|All Files (*.*)|*.*</value>
  </data>
  <data name="TitleExportLogs" xml:space="preserve">
    <value>Export Logs</value>
  </data>
  <data name="MessageLogsExportedSuccessfully" xml:space="preserve">
    <value>Logs exported successfully to</value>
  </data>
  <data name="TextExportSuccess" xml:space="preserve">
    <value>Export Success</value>
  </data>
  <data name="MessageErrorExportingLogs" xml:space="preserve">
    <value>Error exporting logs</value>
  </data>
  <data name="LabelReading2" xml:space="preserve">
    <value>Reading 2:</value>
  </data>
  <data name="LabelReading3" xml:space="preserve">
    <value>Reading 3:</value>
  </data>
  <data name="LabelReading4" xml:space="preserve">
    <value>Reading 4:</value>
  </data>
  <data name="LabelReading5" xml:space="preserve">
    <value>Reading 5:</value>
  </data>
  <data name="LabelReading6" xml:space="preserve">
    <value>Reading 6:</value>
  </data>
  <data name="LabelReading7" xml:space="preserve">
    <value>Reading 7:</value>
  </data>
  <data name="MessageCalibrationProcess" xml:space="preserve">
    <value>The calibration process is based on the test values of each point of the sensor according to the field standard test bench, that is, the front is the current reading of the sensor on the calibration bench, and the back is the actual calibration reading.</value>
  </data>
  <data name="MessageFormulaInputDescription" xml:space="preserve">
    <value>In the calculation formula, "Input" represents the sensor reading, and you can directly use the names of other sensors.</value>
  </data>
  <data name="LabelFormulaEditingArea" xml:space="preserve">
    <value>Formula editing area (Note: formulas are case sensitive):</value>
  </data>
  <data name="MenuInsertEnvironmentVariable" xml:space="preserve">
    <value>Insert Environment Variable...</value>
  </data>
  <data name="LabelFormulaAnalysisStatus" xml:space="preserve">
    <value>Formula analysis status and calculation verification:</value>
  </data>
  <data name="ButtonPreviousStep" xml:space="preserve">
    <value>Previous</value>
  </data>
  <data name="ButtonNextStep" xml:space="preserve">
    <value>Next</value>
  </data>
  <data name="SensorDataHistoryWindowTitle" xml:space="preserve">
    <value>Sensor Historical Data Query and Export</value>
  </data>
  <data name="LabelStartTime" xml:space="preserve">
    <value>Start Time:</value>
  </data>
  <data name="LabelEndTime" xml:space="preserve">
    <value>End Time:</value>
  </data>
  <data name="LabelStartPoint" xml:space="preserve">
    <value>Start Point</value>
  </data>
  <data name="LabelEndPoint" xml:space="preserve">
    <value>End Point</value>
  </data>
  <data name="LabelStartValue" xml:space="preserve">
    <value>Start Value</value>
  </data>
  <data name="LabelEndValue" xml:space="preserve">
    <value>End Value</value>
  </data>
  <data name="LabelRandomFluctuation" xml:space="preserve">
    <value>Random Fluctuation</value>
  </data>
  <data name="ButtonInsertData" xml:space="preserve">
    <value>Insert Data</value>
  </data>
  <data name="LabelSamplingInterval" xml:space="preserve">
    <value>Sampling:</value>
  </data>
  <data name="ButtonQuery" xml:space="preserve">
    <value>Query</value>
  </data>
  <data name="ButtonExportAsTextFile" xml:space="preserve">
    <value>Export as Text File</value>
  </data>
  <data name="ButtonExportAsExcel" xml:space="preserve">
    <value>Export as Excel</value>
  </data>
  <data name="LabelFirstPage" xml:space="preserve">
    <value>First</value>
  </data>
  <data name="LabelPreviousPage" xml:space="preserve">
    <value>Previous</value>
  </data>
  <data name="LabelNextPage" xml:space="preserve">
    <value>Next</value>
  </data>
  <data name="LabelLastPage" xml:space="preserve">
    <value>Last</value>
  </data>
  <data name="LabelPage" xml:space="preserve">
    <value>Page </value>
  </data>
  <data name="LabelPageSuffix" xml:space="preserve">
    <value> of </value>
  </data>
  <data name="LabelTotal" xml:space="preserve">
    <value>Total </value>
  </data>
  <data name="LabelRecords" xml:space="preserve">
    <value> records</value>
  </data>
  <data name="LabelJumpTo" xml:space="preserve">
    <value>Jump to</value>
  </data>
  <data name="LabelPageUnit" xml:space="preserve">
    <value>page</value>
  </data>
  <data name="ButtonJump" xml:space="preserve">
    <value>Jump</value>
  </data>
  <data name="LabelSelectSensor" xml:space="preserve">
    <value>Select Sensor:</value>
  </data>
  <data name="LabelProgress" xml:space="preserve">
    <value>Progress</value>
  </data>
  <data name="TextPageSize100K" xml:space="preserve">
    <value>100K records per page</value>
  </data>
  <data name="TextPageSize500K" xml:space="preserve">
    <value>500K records per page</value>
  </data>
  <data name="TextPageSize1M" xml:space="preserve">
    <value>1M records per page</value>
  </data>
  <data name="LabelModify" xml:space="preserve">
    <value>Modify</value>
  </data>
  <data name="MessageJumpPageNumberRequired" xml:space="preserve">
    <value>Jump page number cannot be empty!</value>
  </data>
  <data name="MessageJumpPageNumberInvalid" xml:space="preserve">
    <value>Jump page number format is incorrect!</value>
  </data>
  <data name="ButtonQuerying" xml:space="preserve">
    <value>Querying...</value>
  </data>
  <data name="MessagePleaseSelectSensor" xml:space="preserve">
    <value>Please select a sensor!</value>
  </data>
  <data name="MessagePleaseSelectStartDate" xml:space="preserve">
    <value>Please select start date!</value>
  </data>
  <data name="MessagePleaseSelectEndDate" xml:space="preserve">
    <value>Please select end date!</value>
  </data>
  <data name="MessageSamplingIntervalRequired" xml:space="preserve">
    <value>Sampling interval cannot be empty!</value>
  </data>
  <data name="MessageSamplingIntervalInvalid" xml:space="preserve">
    <value>Sampling interval format is incorrect!</value>
  </data>
  <data name="MonitorWindowViewTitle" xml:space="preserve">
    <value>Data Monitor Window</value>
  </data>
  <data name="ButtonShow" xml:space="preserve">
    <value>Show</value>
  </data>
  <data name="MenuDisplaySensorInChart" xml:space="preserve">
    <value>Display Sensor in Chart</value>
  </data>
  <data name="MenuRemoveFromChart" xml:space="preserve">
    <value>Remove from Chart</value>
  </data>
  <data name="MenuSetColor" xml:space="preserve">
    <value>Set Color</value>
  </data>
  <data name="MenuMoveUp" xml:space="preserve">
    <value>Move Up</value>
  </data>
  <data name="MenuMoveDown" xml:space="preserve">
    <value>Move Down</value>
  </data>
  <data name="MenuSensorSettings" xml:space="preserve">
    <value>Sensor Settings</value>
  </data>
  <data name="MenuModifyLastValue" xml:space="preserve">
    <value>Modify Last Value</value>
  </data>
  <data name="MenuHideSensor" xml:space="preserve">
    <value>Hide Sensor</value>
  </data>
  <data name="MenuUnhideSensor" xml:space="preserve">
    <value>Unhide Sensor</value>
  </data>
  <data name="MenuShowHiddenSensors" xml:space="preserve">
    <value>Show Hidden Sensors</value>
  </data>
  <data name="MenuHideAllHiddenSensors" xml:space="preserve">
    <value>Hide All Hidden Sensors</value>
  </data>
  <data name="MenuIndependentWindow" xml:space="preserve">
    <value>Independent Window</value>
  </data>
  <data name="LabelCurrentValue" xml:space="preserve">
    <value>Current Value</value>
  </data>
  <data name="SectionBasicInfo" xml:space="preserve">
    <value>Basic Information</value>
  </data>
  <data name="SectionSensorConfig" xml:space="preserve">
    <value>Sensor Configuration</value>
  </data>
  <data name="SectionAdvancedSettings" xml:space="preserve">
    <value>Advanced Settings</value>
  </data>
  <data name="SectionDisplaySettings" xml:space="preserve">
    <value>Display Settings</value>
  </data>
  <data name="SectionCalculationFormula" xml:space="preserve">
    <value>Calculation Formula</value>
  </data>

  <!-- SensorAlarmConfigControl Internationalization Resources -->
  <data name="SensorAlarmConfig_AddSensor" xml:space="preserve">
    <value>+ Add Sensor</value>
  </data>
  <data name="SensorAlarmConfig_DeleteSelected" xml:space="preserve">
    <value>- Delete Selected</value>
  </data>
  <data name="SensorAlarmConfig_Title" xml:space="preserve">
    <value>Sensor Alert Configuration</value>
  </data>
  <data name="SensorAlarmConfig_SensorName" xml:space="preserve">
    <value>Sensor Name</value>
  </data>
  <data name="SensorAlarmConfig_SensorSN" xml:space="preserve">
    <value>Sensor SN</value>
  </data>
  <data name="SensorAlarmConfig_SensorID" xml:space="preserve">
    <value>Sensor ID</value>
  </data>
  <data name="SensorAlarmConfig_Operation" xml:space="preserve">
    <value>Operation</value>
  </data>
  <data name="SensorAlarmConfig_AddSensorTooltip" xml:space="preserve">
    <value>Add Sensor</value>
  </data>
  <data name="SensorAlarmConfig_DeleteSensorTooltip" xml:space="preserve">
    <value>Delete Sensor</value>
  </data>
  <data name="SensorAlarmConfig_RuleCount" xml:space="preserve">
    <value>{0} Rules</value>
  </data>
  <data name="SensorAlarmConfig_AddRuleTooltip" xml:space="preserve">
    <value>Add Rule</value>
  </data>
  <data name="SensorAlarmConfig_DeleteRuleTooltip" xml:space="preserve">
    <value>Delete Rule</value>
  </data>

  <!-- Threshold Alert Rules -->
  <data name="SensorAlarmConfig_ThresholdRules" xml:space="preserve">
    <value>Threshold Alert Rules</value>
  </data>
  <data name="SensorAlarmConfig_Severity" xml:space="preserve">
    <value>Severity</value>
  </data>
  <data name="SensorAlarmConfig_Operator" xml:space="preserve">
    <value>Operator</value>
  </data>
  <data name="SensorAlarmConfig_Threshold" xml:space="preserve">
    <value>Threshold</value>
  </data>
  <data name="SensorAlarmConfig_Deadband" xml:space="preserve">
    <value>Deadband</value>
  </data>
  <data name="SensorAlarmConfig_Message" xml:space="preserve">
    <value>Message</value>
  </data>

  <!-- Severity Options -->
  <data name="SensorAlarmConfig_SeverityCritical" xml:space="preserve">
    <value>Critical</value>
  </data>
  <data name="SensorAlarmConfig_SeverityHigh" xml:space="preserve">
    <value>High</value>
  </data>
  <data name="SensorAlarmConfig_SeverityMedium" xml:space="preserve">
    <value>Medium</value>
  </data>
  <data name="SensorAlarmConfig_SeverityLow" xml:space="preserve">
    <value>Low</value>
  </data>

  <!-- Trend Alert Rules -->
  <data name="SensorAlarmConfig_TrendRules" xml:space="preserve">
    <value>Trend Alert Rules</value>
  </data>
  <data name="SensorAlarmConfig_RateOfChange" xml:space="preserve">
    <value>Rate of Change</value>
  </data>
  <data name="SensorAlarmConfig_TimeWindowSeconds" xml:space="preserve">
    <value>Time Window (sec)</value>
  </data>
  <data name="SensorAlarmConfig_Direction" xml:space="preserve">
    <value>Direction</value>
  </data>

  <!-- Direction Options -->
  <data name="SensorAlarmConfig_DirectionRising" xml:space="preserve">
    <value>Rising</value>
  </data>
  <data name="SensorAlarmConfig_DirectionFalling" xml:space="preserve">
    <value>Falling</value>
  </data>
  <data name="SensorAlarmConfig_DirectionBoth" xml:space="preserve">
    <value>Bidirectional</value>
  </data>

  <!-- Pattern Alert Rules -->
  <data name="SensorAlarmConfig_PatternRules" xml:space="preserve">
    <value>Pattern Alert Rules</value>
  </data>
  <data name="SensorAlarmConfig_Pattern" xml:space="preserve">
    <value>Pattern</value>
  </data>
  <data name="SensorAlarmConfig_Tolerance" xml:space="preserve">
    <value>Tolerance</value>
  </data>

  <!-- Correlation Alert Rules -->
  <data name="SensorAlarmConfig_CorrelationRules" xml:space="preserve">
    <value>Correlation Alert Rules</value>
  </data>
  <data name="SensorAlarmConfig_RelatedSensor" xml:space="preserve">
    <value>Related Sensor</value>
  </data>
  <data name="SensorAlarmConfig_Expression" xml:space="preserve">
    <value>Expression</value>
  </data>

  <!-- Operator Options -->
  <data name="SensorAlarmConfig_OperatorGreaterThan" xml:space="preserve">
    <value>&gt;</value>
  </data>
  <data name="SensorAlarmConfig_OperatorGreaterThanOrEqual" xml:space="preserve">
    <value>&gt;=</value>
  </data>
  <data name="SensorAlarmConfig_OperatorLessThan" xml:space="preserve">
    <value>&lt;</value>
  </data>
  <data name="SensorAlarmConfig_OperatorLessThanOrEqual" xml:space="preserve">
    <value>&lt;=</value>
  </data>
  <data name="SensorAlarmConfig_OperatorEqual" xml:space="preserve">
    <value>=</value>
  </data>
  <data name="SensorAlarmConfig_OperatorNotEqual" xml:space="preserve">
    <value>!=</value>
  </data>

  <!-- Rule Count Format -->
  <data name="SensorAlarmConfig_RuleCountFormat" xml:space="preserve">
    <value>{0} Rules</value>
  </data>

  <!-- Rule Category Names -->
  <data name="SensorAlarmConfig_ThresholdRuleCategory" xml:space="preserve">
    <value>Threshold Rules</value>
  </data>
  <data name="SensorAlarmConfig_TrendRuleCategory" xml:space="preserve">
    <value>Trend Rules</value>
  </data>
  <data name="SensorAlarmConfig_PatternRuleCategory" xml:space="preserve">
    <value>Pattern Rules</value>
  </data>
  <data name="SensorAlarmConfig_CorrelationRuleCategory" xml:space="preserve">
    <value>Correlation Rules</value>
  </data>

  <!-- Default Values and Messages -->
  <data name="SensorAlarmConfig_NewSensor" xml:space="preserve">
    <value>New Sensor</value>
  </data>
  <data name="SensorAlarmConfig_UnknownSensor" xml:space="preserve">
    <value>Unknown Sensor</value>
  </data>
  <data name="SensorAlarmConfig_NewRule" xml:space="preserve">
    <value>New {0}</value>
  </data>
  <data name="SensorAlarmConfig_PleaseSelectSensor" xml:space="preserve">
    <value>Please select a sensor to delete</value>
  </data>
  <data name="SensorAlarmConfig_Tip" xml:space="preserve">
    <value>Tip</value>
  </data>
  <data name="SensorAlarmConfig_NewAlarmMessage" xml:space="preserve">
    <value>New threshold alarm</value>
  </data>
  <data name="SensorAlarmConfig_EditThresholdRule" xml:space="preserve">
    <value>Edit Threshold Rule</value>
  </data>
  <data name="SensorAlarmConfig_UnknownRuleType" xml:space="preserve">
    <value>Unknown rule type</value>
  </data>
  <data name="SensorAlarmConfig_Error" xml:space="preserve">
    <value>Error</value>
  </data>
  <data name="SensorAlarmConfig_AddRuleTooltip" xml:space="preserve">
    <value>Add Rule</value>
  </data>

  <!-- RealTimeDeviceListWindow -->
  <data name="RealTimeDeviceList_Title" xml:space="preserve">
    <value>Real-time Device List</value>
  </data>
  <data name="RealTimeDeviceList_Refresh" xml:space="preserve">
    <value>🔄 Refresh</value>
  </data>
  <data name="RealTimeDeviceList_DeviceList" xml:space="preserve">
    <value>Real-time Device List</value>
  </data>
  <data name="RealTimeDeviceList_DeviceCount" xml:space="preserve">
    <value> ({0} devices)</value>
  </data>
  <data name="RealTimeDeviceList_DeviceSN" xml:space="preserve">
    <value>Device SN</value>
  </data>
  <data name="RealTimeDeviceList_IPAddress" xml:space="preserve">
    <value>IP Address</value>
  </data>
  <data name="RealTimeDeviceList_DeviceType" xml:space="preserve">
    <value>Device Type</value>
  </data>
  <data name="RealTimeDeviceList_Status" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="RealTimeDeviceList_Value" xml:space="preserve">
    <value>Value</value>
  </data>
  <data name="RealTimeDeviceList_Timeout" xml:space="preserve">
    <value>Timeout</value>
  </data>
  <data name="RealTimeDeviceList_ChannelName" xml:space="preserve">
    <value>Channel Name</value>
  </data>
  <data name="RealTimeDeviceList_LoadingChannels" xml:space="preserve">
    <value>Loading channel information...</value>
  </data>
  <data name="RealTimeDeviceList_ChannelPrefix" xml:space="preserve">
    <value>Channel {0}</value>
  </data>
  <data name="RealTimeDeviceList_DataTypeAnalog" xml:space="preserve">
    <value>Analog</value>
  </data>
  <data name="RealTimeDeviceList_DataTypeDigital" xml:space="preserve">
    <value>Digital</value>
  </data>
  <data name="RealTimeDeviceList_DataTypePulse" xml:space="preserve">
    <value>Pulse</value>
  </data>
  <data name="RealTimeDeviceList_DataTypeUnknown" xml:space="preserve">
    <value>Unknown</value>
  </data>
  <data name="RealTimeDeviceList_SensorOccupied" xml:space="preserve">
    <value>Sensor: {0}</value>
  </data>
  <data name="RealTimeDeviceList_Yes" xml:space="preserve">
    <value>Yes</value>
  </data>
  <data name="RealTimeDeviceList_No" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="RealTimeDeviceList_Unknown" xml:space="preserve">
    <value>Unknown</value>
  </data>

  <!-- RealTimeDeviceConnectionListWindow -->
  <data name="RealTimeDeviceConnection_Title" xml:space="preserve">
    <value>Device Connection Status List</value>
  </data>
  <data name="RealTimeDeviceConnection_IPAddress" xml:space="preserve">
    <value>IP Address</value>
  </data>
  <data name="RealTimeDeviceConnection_Status" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="RealTimeDeviceConnection_Online" xml:space="preserve">
    <value>Online</value>
  </data>
  <data name="RealTimeDeviceConnection_Offline" xml:space="preserve">
    <value>Offline</value>
  </data>
  <data name="RealTimeDeviceConnection_Timeout" xml:space="preserve">
    <value>Timeout</value>
  </data>
  <data name="RealTimeDeviceConnection_Normal" xml:space="preserve">
    <value>Normal</value>
  </data>
  <data name="RealTimeDeviceConnection_Occupied" xml:space="preserve">
    <value>Occupied</value>
  </data>
  <data name="RealTimeDeviceConnection_Free" xml:space="preserve">
    <value>Free</value>
  </data>
</root>
